{"data_mtime": 1752110370, "dep_lines": [6, 7, 8, 9, 10, 11, 12, 232, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["abc", "typing", "dataclasses", "enum", "asyncio", "logging", "collections", "time", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "types", "typing_extensions"], "hash": "aaecec0c6018450f05335c4efd6d883b47edf7e2", "id": "processing_pipeline", "ignore_all": true, "interface_hash": "aa999a30307137714cafaff7e9795af3d252bff6", "mtime": 1752110129, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\processing_pipeline.py", "plugin_data": null, "size": 12757, "suppressed": [], "version_id": "1.15.0"}