#!/usr/bin/env python3
"""
Debug what the OpenWebUI API is actually returning
"""

import requests
import json
import time

def test_single_api_query():
    """Test a single query and show the full response"""
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    model = "llama3:latest"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test the query that worked in GUI
    query = "How is the TMW library structured?"
    
    # Enhanced prompt like our test script
    enhanced_prompt = f"""I need help analyzing the utils codebase. Please use the codebase analyzer tool to help me with: {query}

Please analyze the actual code in the utils codebase and provide specific details from the source code."""
    
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user", 
                "content": enhanced_prompt
            }
        ],
        "stream": False
    }
    
    print(f"🔧 Testing query: '{query}'")
    print(f"🔧 Enhanced prompt: '{enhanced_prompt[:100]}...'")
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=60
        )
        end_time = time.time()
        
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📊 Response Time: {end_time - start_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            
            # Extract the actual content
            choices = result.get("choices", [])
            if choices:
                message = choices[0].get("message", {})
                content = message.get("content", "")
                
                print(f"\n💬 Full Message Content:")
                print("="*100)
                print(content)
                print("="*100)
                
                # Analyze for tool usage
                tool_indicators = [
                    "code context retrieved",
                    "chunks found", 
                    "codebase analyzer",
                    "=== code metadata ===",
                    "=== relevant code context ===",
                    "file:", "language:", "type:",
                    "tmwmem", "tmwdiag",
                    "context 1:", "context 2:",
                    "domains:", "quality:", "complexity:"
                ]
                
                found_indicators = [indicator for indicator in tool_indicators if indicator in content.lower()]
                
                print(f"\n🔍 Analysis:")
                print(f"   Content Length: {len(content)} characters")
                print(f"   Found Tool Indicators: {found_indicators}")
                print(f"   Tool Likely Used: {len(found_indicators) > 0}")
                
                # Look for chunk count patterns
                import re
                chunk_patterns = [
                    r'(\d+)\s+chunks?\s+found',
                    r'code context retrieved successfully \((\d+) chunks?\s+found\)',
                    r'retrieved successfully \((\d+) chunks?\)',
                    r'found (\d+) relevant',
                    r'(\d+) relevant code sections'
                ]
                
                chunk_matches = []
                for pattern in chunk_patterns:
                    matches = re.findall(pattern, content.lower())
                    if matches:
                        chunk_matches.extend(matches)
                
                print(f"   Chunk Matches: {chunk_matches}")
                
                # Check for specific success patterns
                success_patterns = [
                    "code context retrieved successfully",
                    "relevant code context",
                    "context 1",
                    "=== code metadata ==="
                ]
                
                found_success = [pattern for pattern in success_patterns if pattern in content.lower()]
                print(f"   Success Patterns: {found_success}")
                
            else:
                print("❌ No choices in response")
        else:
            print(f"❌ Error Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

def test_simple_query():
    """Test a simple query that we know works"""
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    model = "llama3:latest"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test simple function name
    query = "tmwmem_alloc"
    
    enhanced_prompt = f"""I need help analyzing the utils codebase. Please use the codebase analyzer tool to help me with: {query}

Please analyze the actual code in the utils codebase and provide specific details from the source code."""
    
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user", 
                "content": enhanced_prompt
            }
        ],
        "stream": False
    }
    
    print(f"\n\n🔧 Testing simple query: '{query}'")
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=60
        )
        end_time = time.time()
        
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📊 Response Time: {end_time - start_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            
            # Extract the actual content
            choices = result.get("choices", [])
            if choices:
                message = choices[0].get("message", {})
                content = message.get("content", "")
                
                print(f"\n💬 Message Content (first 500 chars):")
                print("="*80)
                print(content[:500])
                print("="*80)
                
                # Quick analysis
                tool_used = any(indicator in content.lower() for indicator in [
                    "code context retrieved", "chunks found", "=== relevant code context ==="
                ])
                
                print(f"\n🔍 Quick Analysis:")
                print(f"   Tool Used: {tool_used}")
                print(f"   Contains 'tmwmem': {'tmwmem' in content.lower()}")
                print(f"   Contains 'context': {'context' in content.lower()}")
                
            else:
                print("❌ No choices in response")
        else:
            print(f"❌ Error Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    print("🚀 DEBUGGING OPENWEBUI API RESPONSES")
    print("="*100)
    
    # Test the complex query that worked in GUI
    test_single_api_query()
    
    # Test simple function name
    test_simple_query()
    
    print(f"\n{'='*100}")
    print("🎯 DEBUG COMPLETE")
    print("="*100)
