#!/usr/bin/env python3
"""
Test the new semantic-driven query enhancement system inspired by code_preprocessor.py.
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path to import the tool
sys.path.append(str(Path(__file__).parent))

from open_webui_code_analyzer_tool import Tools

async def test_semantic_enhancement():
    """Test the semantic-driven query enhancement system"""
    print("🔍 Testing Semantic-Driven Query Enhancement System")
    print("=" * 60)
    
    # Initialize the tool
    tool = Tools()
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    
    # Clear cache to test fresh
    if tool.cache:
        await tool.clear_cache()
    
    # Ensure utils is selected
    print("1. Selecting utils codebase...")
    await tool.select_codebase("utils")
    
    # Test queries that should benefit from semantic enhancement
    test_queries = [
        # Memory management (should get tmwmem, malloc, free, etc.)
        "how is memory managed",
        "show me memory allocation functions",
        "find memory deallocation code",
        
        # Error handling (should get tmwerr, errno, perror, etc.)
        "how are errors handled",
        "show me error reporting",
        "find error checking code",
        
        # Network operations (should get socket, bind, listen, etc.)
        "show me network code",
        "how does socket communication work",
        "find network protocol handling",
        
        # Timer operations (should get tmwtimer, timeout, interval, etc.)
        "find timer functions",
        "how are timers managed",
        "show me timeout handling",
        
        # I/O operations (should get printf, scanf, fopen, etc.)
        "show me file operations",
        "how is input/output handled",
        
        # Configuration (should get tmwcnfg, configuration, parameter, etc.)
        "show me configuration",
        "how are settings handled",
        
        # Initialization (should get init, initialize, setup, etc.)
        "show me initialization code",
        "find startup functions",
        
        # Cleanup (should get cleanup, destroy, close, etc.)
        "show me cleanup functions",
        "find shutdown code",
        
        # Debug (should get debug, trace, log, etc.)
        "show me debug functions",
        "find logging code"
    ]
    
    successful = 0
    failed = 0
    
    for i, query in enumerate(test_queries, 2):
        print(f"\n{i}. Testing: '{query}'")
        try:
            result = await tool.get_code_context(query, codebase_name="utils", n_results=10)
            
            if "❌ Unable to retrieve code context" in result or "Context retrieval failed" in result:
                print(f"   ❌ FAILED: Context retrieval failed")
                failed += 1
            elif len(result) > 300:
                print(f"   ✅ SUCCESS: Got {len(result)} characters")
                successful += 1
                
                # Check for semantic relevance
                query_words = query.lower().split()
                if any(word in result.lower() for word in query_words):
                    print(f"   ✅ Contains semantically relevant content")
                else:
                    print(f"   ⚠️ Content might be generic")
            else:
                print(f"   ⚠️ SHORT: {len(result)} characters")
                print(f"   Content: {result[:150]}...")
                failed += 1
                
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
            failed += 1
    
    print(f"\n" + "=" * 60)
    print(f"📊 SEMANTIC ENHANCEMENT RESULTS: {successful} successful, {failed} failed")
    success_rate = successful/(successful+failed)*100 if (successful+failed) > 0 else 0
    print(f"🎯 SUCCESS RATE: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 OUTSTANDING! Semantic enhancement is working excellently!")
    elif success_rate >= 70:
        print("🎉 EXCELLENT! Semantic enhancement achieved 70%+ success rate!")
    elif success_rate >= 60:
        print("✅ GOOD! Semantic enhancement is working well!")
    else:
        print("⚠️ Semantic enhancement needs refinement")
    
    print("🎉 Semantic enhancement test completed!")

if __name__ == "__main__":
    try:
        asyncio.run(test_semantic_enhancement())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
