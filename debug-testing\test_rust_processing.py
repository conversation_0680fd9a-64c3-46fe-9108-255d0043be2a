#!/usr/bin/env python3
"""
Test script to verify that Rust code processing works correctly
"""

import tempfile
import shutil
from pathlib import Path
from code_preprocessor import MultiLanguageCodeProcessor

def test_rust_processing():
    """Test Rust processing with sample files"""
    
    print("🦀 Testing Rust Code Processing")
    print("=" * 60)
    
    # Create temporary directory with sample Rust files
    temp_dir = tempfile.mkdtemp()
    
    try:
        rust_dir = Path(temp_dir) / "rust_project"
        rust_dir.mkdir()
        
        # Create sample Rust files
        
        # 1. Main.rs file
        main_file = rust_dir / "main.rs"
        main_file.write_text("""
//! Main entry point for the Rust application
//! This module contains the main function and core logic

use std::collections::HashMap;
use std::io::{self, Write};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    pub id: u32,
    pub name: String,
    pub email: String,
}

#[derive(Debug)]
pub enum UserError {
    NotFound,
    InvalidData,
    DatabaseError(String),
}

impl std::fmt::Display for UserError {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        match self {
            UserError::NotFound => write!(f, "User not found"),
            UserError::InvalidData => write!(f, "Invalid user data"),
            UserError::DatabaseError(msg) => write!(f, "Database error: {}", msg),
        }
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Starting Rust application...");
    
    let mut users: HashMap<u32, User> = HashMap::new();
    
    let user = User {
        id: 1,
        name: "John Doe".to_string(),
        email: "<EMAIL>".to_string(),
    };
    
    users.insert(user.id, user);
    
    match users.get(&1) {
        Some(user) => println!("Found user: {:?}", user),
        None => return Err(Box::new(UserError::NotFound)),
    }
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_user_creation() {
        let user = User {
            id: 1,
            name: "Test User".to_string(),
            email: "<EMAIL>".to_string(),
        };
        
        assert_eq!(user.id, 1);
        assert_eq!(user.name, "Test User");
    }
}
""")
        
        # 2. Lib.rs file
        lib_file = rust_dir / "lib.rs"
        lib_file.write_text("""
//! Library module for shared functionality
//! Contains common utilities and data structures

pub mod utils;
pub mod database;

use std::collections::HashMap;
use std::sync::{Arc, Mutex};

pub type UserDatabase = Arc<Mutex<HashMap<u32, User>>>;

#[derive(Debug, Clone)]
pub struct User {
    pub id: u32,
    pub name: String,
    pub email: String,
}

pub trait UserRepository {
    fn create_user(&mut self, user: User) -> Result<(), String>;
    fn get_user(&self, id: u32) -> Option<&User>;
    fn update_user(&mut self, id: u32, user: User) -> Result<(), String>;
    fn delete_user(&mut self, id: u32) -> Result<(), String>;
}

pub struct InMemoryUserRepository {
    users: HashMap<u32, User>,
}

impl InMemoryUserRepository {
    pub fn new() -> Self {
        Self {
            users: HashMap::new(),
        }
    }
}

impl UserRepository for InMemoryUserRepository {
    fn create_user(&mut self, user: User) -> Result<(), String> {
        if self.users.contains_key(&user.id) {
            return Err("User already exists".to_string());
        }
        self.users.insert(user.id, user);
        Ok(())
    }
    
    fn get_user(&self, id: u32) -> Option<&User> {
        self.users.get(&id)
    }
    
    fn update_user(&mut self, id: u32, user: User) -> Result<(), String> {
        if !self.users.contains_key(&id) {
            return Err("User not found".to_string());
        }
        self.users.insert(id, user);
        Ok(())
    }
    
    fn delete_user(&mut self, id: u32) -> Result<(), String> {
        match self.users.remove(&id) {
            Some(_) => Ok(()),
            None => Err("User not found".to_string()),
        }
    }
}
""")
        
        # 3. Cargo.toml
        cargo_file = rust_dir / "Cargo.toml"
        cargo_file.write_text("""
[package]
name = "rust-starter"
version = "0.1.0"
edition = "2021"

[dependencies]
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
""")
        
        print(f"📁 Created test Rust project at: {rust_dir}")
        print(f"📄 Files created:")
        for file in rust_dir.iterdir():
            print(f"  - {file.name} ({file.stat().st_size} bytes)")
        
        # Test processing with MultiLanguageCodeProcessor
        print("\n🧪 Testing Rust Processing...")
        processor = MultiLanguageCodeProcessor(str(rust_dir))
        
        # Check file detection
        print("\n1. File Detection Test:")
        for file in rust_dir.iterdir():
            if file.is_file():
                parser, language, detected_lang = processor.get_parser_for_file(file)
                print(f"  {file.name}: {detected_lang} (parser: {'✅' if parser else '❌'})")
        
        # Process the directory
        print("\n2. Processing Test:")
        chunks = processor.process_repository(str(rust_dir))
        
        print(f"✅ Generated {len(chunks)} chunks")
        
        if len(chunks) > 0:
            print("\n📊 Chunk Analysis:")
            for i, chunk in enumerate(chunks[:3]):  # Show first 3 chunks
                metadata = chunk.get('metadata', {})
                print(f"  Chunk {i+1}:")
                print(f"    File: {metadata.get('relative_path', 'unknown')}")
                print(f"    Language: {metadata.get('language', 'unknown')}")
                print(f"    Type: {metadata.get('type', 'unknown')}")
                print(f"    Lines: {metadata.get('start_line', '?')}-{metadata.get('end_line', '?')}")
                print(f"    Content preview: {chunk.get('content', '')[:100]}...")
                print()
                
            # Check for Rust-specific semantic tags
            print("🏷️ Rust-specific semantic tags found:")
            all_tags = []
            for chunk in chunks:
                tags = chunk.get('metadata', {}).get('semantic_tags', [])
                all_tags.extend(tags)
            
            unique_tags = list(set(all_tags))
            for tag in sorted(unique_tags):
                print(f"  - {tag}")
                
        else:
            print("❌ No chunks generated - Rust processing failed!")
            return False
        
        return len(chunks) > 0
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        shutil.rmtree(temp_dir)

if __name__ == "__main__":
    print("🦀 Rust Processing Test")
    print("=" * 70)
    
    success = test_rust_processing()
    
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS:")
    print("=" * 70)
    
    if success:
        print("🎉 SUCCESS! Rust processing is working correctly!")
        print("✅ Rust files are detected and processed")
        print("✅ Rust header detection works")
        print("✅ Rust chunks are generated with metadata")
        print("\n🚀 The rust-starter-master codebase should now process successfully!")
        
    else:
        print("❌ FAILED! Rust processing has issues")
        print("🔧 Check the error messages above for details")
        
    print("\n💡 Next steps:")
    print("1. Try processing rust-starter-master again in OpenWebUI")
    print("2. The Rust header detection should now work correctly")
    print("3. Rust files should generate proper chunks with semantic tags")
