#!/usr/bin/env python3
"""
Test the enhanced query with a fresh cache.
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path to import the tool
sys.path.append(str(Path(__file__).parent))

from open_webui_code_analyzer_tool import Tools

class MockEventEmitter:
    """Mock event emitter to capture status messages"""
    def __init__(self):
        self.events = []
    
    async def __call__(self, event):
        self.events.append(event)
        print(f"📡 Event: {event}")

async def test_fresh_enhanced_query():
    """Test enhanced query with fresh cache"""
    print("🔍 Testing Fresh Enhanced Query")
    print("=" * 60)
    
    # Initialize the tool
    tool = Tools()
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    
    # Clear cache completely
    print("1. Clearing cache completely...")
    if tool.cache:
        await tool.clear_cache()
        print("   ✅ Cache cleared")
    
    # Ensure utils is selected
    print("\n2. Selecting utils codebase...")
    await tool.select_codebase("utils")
    
    # Create mock event emitter
    event_emitter = MockEventEmitter()
    
    # Test with a slightly different query to avoid cache
    print("\n3. Testing fresh enhanced query...")
    fresh_query = "how is memory managed in utils codebase"  # Slightly different to avoid cache
    
    try:
        result = await tool.get_code_context(
            fresh_query,
            codebase_name="utils",
            n_results=20,
            __event_emitter__=event_emitter
        )
        
        print(f"   Query: '{fresh_query}'")
        print(f"   Result length: {len(result)} characters")
        print(f"   Events captured: {len(event_emitter.events)}")
        
        # Check the final event for chunk count
        if event_emitter.events:
            final_event = event_emitter.events[-1]
            description = final_event.get('data', {}).get('description', '')
            print(f"   Final status: {description}")
        
        # Manual verification
        import re
        found_match = re.search(r'Found (\d+) relevant code section', result)
        context_matches = re.findall(r'## Context \d+', result)
        
        print(f"   'Found X relevant': {found_match.group(1) if found_match else 'None'}")
        print(f"   '## Context X' matches: {len(context_matches)}")
        
        if found_match and int(found_match.group(1)) > 1:
            print(f"   🎉 SUCCESS: Enhanced search found {found_match.group(1)} chunks!")
        elif len(context_matches) > 1:
            print(f"   🎉 SUCCESS: Found {len(context_matches)} context sections!")
        else:
            print(f"   ⚠️ Still only finding limited results")
            print(f"   First 300 chars: {result[:300]}...")
        
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        import traceback
        traceback.print_exc()
    
    # Test the main entry point (simulating OpenWebUI)
    print("\n4. Testing main entry point with fresh query...")
    try:
        main_result = await tool.__call__("how does memory management work in utils")
        
        import re
        found_match = re.search(r'Found (\d+) relevant code section', main_result)
        context_matches = re.findall(r'## Context \d+', main_result)
        
        print(f"   Main entry 'Found X relevant': {found_match.group(1) if found_match else 'None'}")
        print(f"   Main entry '## Context X' matches: {len(context_matches)}")
        
        if found_match and int(found_match.group(1)) > 1:
            print(f"   🎉 SUCCESS: Main entry found {found_match.group(1)} chunks!")
        
    except Exception as e:
        print(f"   ❌ Main entry exception: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 Fresh enhanced query test completed!")

if __name__ == "__main__":
    try:
        asyncio.run(test_fresh_enhanced_query())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
