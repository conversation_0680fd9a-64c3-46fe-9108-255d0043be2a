﻿#pragma checksum "..\..\..\CpuStatus.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "FEC47580F211112B110CCD5E9A22849B93F5845C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using Essenbee.Z80.Debugger;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Essenbee.Z80.Debugger {
    
    
    /// <summary>
    /// CpuStatus
    /// </summary>
    public partial class CpuStatus : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 19 "..\..\..\CpuStatus.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AF;
        
        #line default
        #line hidden
        
        
        #line 27 "..\..\..\CpuStatus.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ShadowAF;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\CpuStatus.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PrimeHL;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\CpuStatus.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AltHL;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\CpuStatus.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PrimeBC;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\CpuStatus.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AltBC;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\CpuStatus.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PrimeDE;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\CpuStatus.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AltDE;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\CpuStatus.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PC;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\CpuStatus.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StackPtr;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\CpuStatus.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IX;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\CpuStatus.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IY;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\CpuStatus.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox I;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\CpuStatus.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox R;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\CpuStatus.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IM;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\CpuStatus.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox Q;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\CpuStatus.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MemPtr;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Essenbee.Z80.Debugger;V1.0.0.0;component/cpustatus.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\CpuStatus.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AF = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.ShadowAF = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.PrimeHL = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.AltHL = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.PrimeBC = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.AltBC = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.PrimeDE = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.AltDE = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.PC = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.StackPtr = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.IX = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.IY = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.I = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.R = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.IM = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.Q = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.MemPtr = ((System.Windows.Controls.TextBox)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

