#!/usr/bin/env python3
"""
Comprehensive test script to identify the logic bug causing intermittent failures in OpenWebUI.
"""

import asyncio
import sys
import time
from pathlib import Path

# Add the current directory to Python path to import the tool
sys.path.append(str(Path(__file__).parent))

from open_webui_code_analyzer_tool import Tools

class MockEventEmitter:
    """Mock event emitter to capture status messages"""
    def __init__(self):
        self.events = []
    
    async def __call__(self, event):
        self.events.append(event)
        print(f"📡 Event: {event}")

async def test_state_persistence():
    """Test if plugin state persists between calls"""
    print("🔍 Testing State Persistence")
    print("=" * 60)
    
    # Initialize the tool
    tool = Tools()
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    
    # Test sequence to identify where the logic breaks
    test_sequence = [
        ("Initial Status", lambda: tool.get_status()),
        ("Select Utils", lambda: tool.select_codebase("utils")),
        ("Check Status After Selection", lambda: tool.get_status()),
        ("Working Query 1", lambda: tool.get_code_context("tmwmem_lowAlloc", "utils", 3)),
        ("Check Status After Working Query", lambda: tool.get_status()),
        ("Generic Query 1", lambda: tool.__call__("how is memory managed in the utils codebase?")),
        ("Check Status After Generic Query", lambda: tool.get_status()),
        ("Working Query 2", lambda: tool.get_code_context("tmwmem_free", "utils", 3)),
        ("Check Status After Working Query 2", lambda: tool.get_status()),
        ("Generic Query 2", lambda: tool.__call__("show me error handling in utils")),
        ("Check Status After Generic Query 2", lambda: tool.get_status()),
        ("Direct Function Call", lambda: tool.get_code_context("error handling", "utils", 5)),
        ("Final Status", lambda: tool.get_status()),
    ]
    
    results = []
    
    for i, (test_name, test_func) in enumerate(test_sequence, 1):
        print(f"\n{i}. {test_name}")
        print("-" * 40)
        
        try:
            # Capture state before test
            pre_state = {
                'current_codebase': getattr(tool.valves, 'current_codebase', 'None'),
                'server_url': tool.valves.code_analyzer_server_url,
            }
            
            start_time = time.time()
            result = await test_func()
            end_time = time.time()
            
            # Capture state after test
            post_state = {
                'current_codebase': getattr(tool.valves, 'current_codebase', 'None'),
                'server_url': tool.valves.code_analyzer_server_url,
            }
            
            # Analyze result
            success = True
            error_msg = None
            
            if isinstance(result, str):
                if "Context retrieval failed" in result:
                    success = False
                    error_msg = "Context retrieval failed"
                elif "❌" in result:
                    success = False
                    error_msg = "Error in response"
                elif len(result) < 50 and "successfully" not in result.lower():
                    success = False
                    error_msg = "Suspiciously short response"
            
            # Log results
            status = "✅ SUCCESS" if success else f"❌ FAILED ({error_msg})"
            duration = f"{end_time - start_time:.2f}s"
            
            print(f"   Status: {status}")
            print(f"   Duration: {duration}")
            print(f"   Pre-state: {pre_state}")
            print(f"   Post-state: {post_state}")
            print(f"   Response length: {len(str(result))} chars")
            
            if not success:
                print(f"   Error details: {str(result)[:200]}...")
            
            # Check for state changes
            if pre_state != post_state:
                print(f"   ⚠️ STATE CHANGED!")
                for key in pre_state:
                    if pre_state[key] != post_state[key]:
                        print(f"     {key}: '{pre_state[key]}' → '{post_state[key]}'")
            
            results.append({
                'test': test_name,
                'success': success,
                'error': error_msg,
                'duration': end_time - start_time,
                'pre_state': pre_state,
                'post_state': post_state,
                'response_length': len(str(result))
            })
            
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
            results.append({
                'test': test_name,
                'success': False,
                'error': f"Exception: {e}",
                'duration': 0,
                'pre_state': pre_state if 'pre_state' in locals() else {},
                'post_state': {},
                'response_length': 0
            })
    
    # Analysis
    print("\n" + "=" * 60)
    print("📊 ANALYSIS")
    print("=" * 60)
    
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    
    print(f"✅ Successful tests: {len(successful_tests)}")
    print(f"❌ Failed tests: {len(failed_tests)}")
    
    if failed_tests:
        print("\n🔍 FAILURE PATTERN ANALYSIS:")
        for result in failed_tests:
            print(f"   • {result['test']}: {result['error']}")
        
        # Look for patterns
        first_failure = failed_tests[0]['test'] if failed_tests else None
        if first_failure:
            print(f"\n🚨 FIRST FAILURE: {first_failure}")
            
            # Find what happened before first failure
            first_failure_index = next(i for i, r in enumerate(results) if r['test'] == first_failure)
            if first_failure_index > 0:
                previous_test = results[first_failure_index - 1]
                print(f"   Previous successful test: {previous_test['test']}")
                print(f"   State after previous test: {previous_test['post_state']}")
    
    # State consistency check
    print("\n🔧 STATE CONSISTENCY:")
    codebase_states = [r['post_state'].get('current_codebase', 'None') for r in results if r['post_state']]
    unique_states = set(codebase_states)
    
    if len(unique_states) > 1:
        print(f"   ⚠️ INCONSISTENT CODEBASE STATES: {unique_states}")
    else:
        print(f"   ✅ Consistent codebase state: {list(unique_states)[0] if unique_states else 'None'}")
    
    print("\n" + "=" * 60)
    print("🎉 Logic debugging completed!")
    
    return results

if __name__ == "__main__":
    try:
        results = asyncio.run(test_state_persistence())
        
        # Save results to file for analysis
        import json
        with open('debug_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n📄 Results saved to debug_results.json")
        
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
