#!/usr/bin/env python3
"""
Test the dynamic analyzer with all 27 supported languages
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

from main import IntegratedCodebaseAnalyzer

async def test_all_languages():
    """Test function extraction for all 27 supported languages"""
    print("🌍 TESTING ALL 27 SUPPORTED LANGUAGES")
    print("=" * 70)
    
    # Mock ChromaDB client
    class MockChromaClient:
        pass
    
    analyzer = IntegratedCodebaseAnalyzer(MockChromaClient())
    
    # Test code samples for all 27 languages
    language_samples = {
        # Core programming languages
        'python': 'def calculate_memory_usage(data): return len(data)',
        'c': 'int calculate_sum(int* array, size_t length) { return sum; }',
        'cpp': 'class Calculator { public: int add(int a, int b); };',
        'csharp': 'public async Task<string> ProcessAsync(string input) { return result; }',
        'javascript': 'function processData(items) { return items.map(x => x * 2); }',
        'typescript': 'interface User { getName(): string; } class UserImpl implements User {}',
        
        # Systems programming
        'rust': 'fn calculate_hash(data: &[u8]) -> u64 { hash_function(data) }',
        'go': 'func ProcessRequest(req *http.Request) (*Response, error) { return nil, nil }',
        'java': 'public class DataProcessor { public void processData(List<String> data) {} }',
        
        # Database and query languages
        'sql': 'CREATE FUNCTION calculate_total(price DECIMAL) RETURNS DECIMAL BEGIN RETURN price * 1.1; END',
        
        # Web development
        'php': 'function getUserData($userId) { return $database->query("SELECT * FROM users"); }',
        'html': '<div class="container"><h1>Title</h1><p>Content</p></div>',
        
        # Scripting languages
        'bash': 'function backup_files() { tar -czf backup.tar.gz /home/<USER>/documents; }',
        'perl': 'sub process_log_file { my ($filename) = @_; open(my $fh, "<", $filename); }',
        'lua': 'function calculate_distance(x1, y1, x2, y2) return math.sqrt((x2-x1)^2 + (y2-y1)^2) end',
        'tcl': 'proc calculate_fibonacci {n} { if {$n <= 1} { return $n } }',
        
        # Functional languages
        'commonlisp': '(defun factorial (n) (if (<= n 1) 1 (* n (factorial (- n 1)))))',
        'emacslisp': '(defun my-custom-function (arg) (message "Processing: %s" arg))',
        'scheme': '(define (square x) (* x x)) (define (sum-of-squares x y) (+ (square x) (square y)))',
        
        # Hardware description languages
        'verilog': 'module counter(input clk, input reset, output reg [7:0] count); always @(posedge clk) begin end endmodule',
        'vhdl': 'ENTITY counter IS PORT(clk : IN STD_LOGIC; count : OUT INTEGER); END ENTITY;',
        
        # Scientific computing
        'fortran': 'SUBROUTINE CALCULATE_MATRIX(A, B, C, N) INTEGER N REAL A(N,N), B(N,N), C(N,N) END SUBROUTINE',
        
        # Data formats
        'json': '{"name": "test", "functions": ["getData", "processData"], "version": "1.0"}',
        'yaml': 'functions:\n  - name: getData\n    type: async\n  - name: processData\n    type: sync',
        'xml': '<functions><function name="getData" type="async"/><function name="processData"/></functions>',
        'toml': '[functions]\ngetData = "async"\nprocessData = "sync"',
        
        # Documentation
        'markdown': '# Functions\n## getData()\nRetrieves data from API\n## processData()\nProcesses the data',
    }
    
    # Test function extraction for each language
    print("\n📋 TESTING FUNCTION EXTRACTION BY LANGUAGE")
    print("-" * 50)
    
    results = {}
    total_functions = 0
    
    for language, code in language_samples.items():
        try:
            functions = analyzer._extract_functions_multi_language(code, language)
            keywords = analyzer._extract_keywords_multi_language(code, language)
            types = analyzer._extract_types_multi_language(code, language)
            constants = analyzer._extract_constants_multi_language(code, language)
            
            results[language] = {
                'functions': functions,
                'keywords': list(keywords)[:5],  # First 5 keywords
                'types': list(types),
                'constants': list(constants)
            }
            
            total_functions += len(functions)
            
            # Display results
            status = "✅" if functions else "⚠️"
            print(f"{status} {language:12} | Functions: {len(functions):2} | Keywords: {len(keywords):2} | Types: {len(types):2}")
            if functions:
                print(f"{'':15} | Sample functions: {functions[:3]}")
            
        except Exception as e:
            print(f"❌ {language:12} | Error: {e}")
            results[language] = {'error': str(e)}
    
    # Test semantic patterns
    print(f"\n📋 TESTING SEMANTIC PATTERNS")
    print("-" * 50)
    
    semantic_coverage = 0
    for language in language_samples.keys():
        if language in analyzer.semantic_patterns:
            patterns = analyzer.semantic_patterns[language]
            print(f"✅ {language:12} | {len(patterns)} semantic domains")
            semantic_coverage += 1
        else:
            print(f"⚠️ {language:12} | No semantic patterns")
    
    # Test language-specific patterns
    print(f"\n📋 TESTING LANGUAGE-SPECIFIC PATTERNS")
    print("-" * 50)
    
    pattern_coverage = 0
    for language in language_samples.keys():
        if language in analyzer.language_patterns:
            patterns = analyzer.language_patterns[language]
            print(f"✅ {language:12} | {len(patterns)} extraction patterns")
            pattern_coverage += 1
        else:
            print(f"⚠️ {language:12} | Using generic patterns")
    
    # Test with multi-language chunk
    print(f"\n📋 TESTING MULTI-LANGUAGE ANALYSIS")
    print("-" * 50)
    
    multi_language_chunks = []
    for language, code in list(language_samples.items())[:10]:  # Test first 10 languages
        multi_language_chunks.append({
            'content': code,
            'metadata': {
                'language': language,
                'semantic_tags': ['testing', 'multi_language']
            }
        })
    
    # Analyze the multi-language chunks
    patterns = analyzer.analyze_chunks(multi_language_chunks)
    
    print(f"✅ Multi-language analysis complete:")
    print(f"   Total functions discovered: {len(patterns['functions'])}")
    print(f"   Languages found: {patterns['analysis_metadata']['languages_found']}")
    print(f"   Dominant language: {patterns['analysis_metadata']['dominant_language']}")
    print(f"   Discovered prefixes: {len(patterns['discovered_prefixes'])}")
    print(f"   Semantic domains: {len(patterns['domains'])}")
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 COMPREHENSIVE LANGUAGE SUPPORT TEST RESULTS")
    print("=" * 70)
    
    languages_tested = len(language_samples)
    functions_extracted = sum(len(r.get('functions', [])) for r in results.values() if 'functions' in r)
    
    print(f"🌍 LANGUAGE COVERAGE:")
    print(f"   Languages tested: {languages_tested}/27")
    print(f"   Functions extracted: {functions_extracted}")
    print(f"   Semantic pattern coverage: {semantic_coverage}/{languages_tested} ({semantic_coverage/languages_tested*100:.1f}%)")
    print(f"   Language pattern coverage: {pattern_coverage}/{languages_tested} ({pattern_coverage/languages_tested*100:.1f}%)")
    
    print(f"\n🎯 LANGUAGE CATEGORIES SUPPORTED:")
    categories = {
        'Programming Languages': ['python', 'c', 'cpp', 'csharp', 'javascript', 'typescript', 'rust', 'go', 'java'],
        'Scripting Languages': ['bash', 'perl', 'lua', 'tcl', 'php'],
        'Functional Languages': ['commonlisp', 'emacslisp', 'scheme'],
        'Hardware Description': ['verilog', 'vhdl'],
        'Scientific Computing': ['fortran'],
        'Database Languages': ['sql'],
        'Data Formats': ['json', 'yaml', 'xml', 'toml'],
        'Documentation': ['markdown', 'html']
    }
    
    for category, langs in categories.items():
        supported = sum(1 for lang in langs if lang in language_samples)
        print(f"   {category}: {supported}/{len(langs)} languages")
    
    print(f"\n🚀 UNIVERSAL CAPABILITIES:")
    print(f"   ✅ Multi-language function extraction")
    print(f"   ✅ Language-aware keyword filtering")
    print(f"   ✅ Dynamic prefix discovery")
    print(f"   ✅ Semantic pattern integration")
    print(f"   ✅ Cross-language analysis")
    print(f"   ✅ Universal domain inference")
    
    if functions_extracted > 50:
        print(f"\n🎉 OUTSTANDING! Universal language support working excellently!")
        print(f"🌍 Your analyzer now supports ALL major programming paradigms!")
    elif functions_extracted > 30:
        print(f"\n🎉 EXCELLENT! Comprehensive language support implemented!")
    else:
        print(f"\n👍 GOOD! Multi-language support working, room for optimization!")
    
    print(f"\n🎯 Your dynamic analyzer is now truly universal and language-agnostic!")

if __name__ == "__main__":
    try:
        asyncio.run(test_all_languages())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
