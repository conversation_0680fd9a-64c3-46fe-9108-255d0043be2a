/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2023 */
/*****************************************************************************/
/*                                                                           */
/* This file is the property of:                                             */
/*                                                                           */
/*                       Triangle MicroWorks, Inc.                           */
/*                      Raleigh, North Carolina USA                          */
/*                       www.TriangleMicroWorks.com                          */
/*                          (919) 870-6615                                   */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*                                                                           */
/*****************************************************************************/

/* file: smbmem.c
 * description:  Implementation of Server Modbus specific memory allocation functions.
 */
#include "tmwscl/modbus/mbdiag.h"
#include "tmwscl/modbus/smbmem.h"
#if TMWCNFG_USE_SIMULATED_DB
#include "tmwscl/modbus/smbsim.h"
#endif
#include "tmwscl/modbus/mbsesn.h"
#include "tmwscl/modbus/smbsesn.h"
#include "tmwscl/utils/tmwtarg.h"

typedef struct SMBmemSMBSesn
{
  TMWMEM_HEADER              header;
  SMBSESN                    data;
} SMBMEM_SMBSESN;

#if TMWCNFG_USE_SIMULATED_DB
typedef struct SMBmemSimDatabase
{
  TMWMEM_HEADER              header;
  SMBSIM_DATABASE            data;
} SMBMEM_SIM_DATABASE;
#endif

static const TMWTYPES_CHAR *_nameTable[SMBMEM_ALLOC_TYPE_MAX] = {
  "SMBSESN"
#if TMWCNFG_USE_SIMULATED_DB
  ,"SMBSIM_DATABASE"
#endif
};
 
#if !TMWCNFG_USE_DYNAMIC_MEMORY
/* Use static allocated memory instead of dynamic memory */
static SMBMEM_SMBSESN         smbmem_smbsessions[SMBCNFG_NUMALLOC_SESNS]; 
#if TMWCNFG_USE_SIMULATED_DB
static SMBMEM_SIM_DATABASE    smbmem_simDatabase[SMBCNFG_NUMALLOC_SIM_DATABASES]; 
#endif
#endif

static TMWMEM_POOL_STRUCT    _smbmemAllocTable[SMBMEM_ALLOC_TYPE_MAX];

#if TMWCNFG_USE_DYNAMIC_MEMORY
/* function: _initConfig */
static void TMWDEFS_LOCAL _initConfig(
  SMBMEM_CONFIG *pConfig)
{
  pConfig->numSessions  = SMBCNFG_NUMALLOC_SESNS;
#if TMWCNFG_USE_SIMULATED_DB
  pConfig->numSimDbases = SMBCNFG_NUMALLOC_SIM_DATABASES;
#endif
}

/* function: smbmem_initConfig */
void TMWDEFS_GLOBAL smbmem_initConfig(
  SMBMEM_CONFIG *pSMBConfig,
  MBMEM_CONFIG  *pMBConfig,
  TMWMEM_CONFIG   *pTmwConfig)
{
  tmwmem_initConfig(pTmwConfig);
  mbmem_initConfig(pMBConfig);
  _initConfig(pSMBConfig);
}

/* function: smbmem_initMemory */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbmem_initMemory(
  SMBMEM_CONFIG *pSMBConfig,
  MBMEM_CONFIG  *pMBConfig,
  TMWMEM_CONFIG *pTmwConfig)
{
  /* Initialize memory management if not yet done */
  if (!tmwmem_init(pTmwConfig))
    return TMWDEFS_FALSE;

  if(!tmwappl_getInitialized(TMWAPPL_INIT_SMB))
  {
    if(!smbmem_init(pSMBConfig))
      return TMWDEFS_FALSE;

    tmwappl_setInitialized(TMWAPPL_INIT_SMB);
  }
    
  /* Initialize memory management if not yet done */
  if(!tmwappl_getInitialized(TMWAPPL_INIT_MB))
  {
    if(!mbmem_init(pMBConfig))
      return TMWDEFS_FALSE;

    tmwappl_setInitialized(TMWAPPL_INIT_MB);
  }
  return TMWDEFS_TRUE;
}
#endif

/* routine: smbmem_init */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbmem_init(
  SMBMEM_CONFIG  *pConfig)
{
#if TMWCNFG_USE_DYNAMIC_MEMORY
  /* dynamic memory allocation supported */
  SMBMEM_CONFIG  config; 

  /* If caller has not specified memory pool configuration, use the
   * default compile time values 
   */
  if(pConfig == TMWDEFS_NULL)
  {
    pConfig = &config;
    _initConfig(pConfig);
  }

  if(!tmwmem_lowInit(_smbmemAllocTable, SMBMEM_SMBSESN_TYPE,      pConfig->numSessions,  sizeof(SMBMEM_SMBSESN),      TMWDEFS_NULL))
    return TMWDEFS_FALSE;
 #if TMWCNFG_USE_SIMULATED_DB
  if(!tmwmem_lowInit(_smbmemAllocTable, SMBMEM_SIM_DATABASE_TYPE, pConfig->numSimDbases, sizeof(SMBMEM_SIM_DATABASE), TMWDEFS_NULL))
    return TMWDEFS_FALSE;
#endif

#else
  /* static memory allocation supported */ 
  if(!tmwmem_lowInit(_smbmemAllocTable, SMBMEM_SMBSESN_TYPE,      SMBCNFG_NUMALLOC_SESNS,         sizeof(SMBMEM_SMBSESN),      (TMWTYPES_UCHAR*)smbmem_smbsessions))
    return TMWDEFS_FALSE;
#if TMWCNFG_USE_SIMULATED_DB
  if(!tmwmem_lowInit(_smbmemAllocTable, SMBMEM_SIM_DATABASE_TYPE, SMBCNFG_NUMALLOC_SIM_DATABASES, sizeof(SMBMEM_SIM_DATABASE), (TMWTYPES_UCHAR*)smbmem_simDatabase))
    return TMWDEFS_FALSE;
#endif

#endif
  return TMWDEFS_TRUE;
}

/* function: smbmem_alloc */
void * TMWDEFS_GLOBAL smbmem_alloc(
  SMBMEM_ALLOC_TYPE type)
{
  if ((type >= SMBMEM_ALLOC_TYPE_MAX)
    || (type < 0))
  {
    return(TMWDEFS_NULL);
  }

  return(tmwmem_lowAlloc(&_smbmemAllocTable[type]));
}

/* function: smbmem_free */
void TMWDEFS_GLOBAL smbmem_free(
  void *pBuf)
{    
  TMWMEM_HEADER *pHeader = TMWMEM_GETHEADER(pBuf);
  TMWTYPES_UCHAR   type = TMWMEM_GETTYPE(pHeader);

  if(type >= SMBMEM_ALLOC_TYPE_MAX)
  {
    return;
  }

  tmwmem_lowFree(&_smbmemAllocTable[type], pHeader);
}

/* function: smbmem_getUsage */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbmem_getUsage(
  TMWTYPES_UCHAR index,
  const TMWTYPES_CHAR **pName,
  TMWMEM_POOL_STRUCT *pStruct)
{
  if(index >= SMBMEM_ALLOC_TYPE_MAX)
  {
    return(TMWDEFS_FALSE);
  }

  *pName = _nameTable[index];
  *pStruct = _smbmemAllocTable[index];
  return(TMWDEFS_TRUE);
}

