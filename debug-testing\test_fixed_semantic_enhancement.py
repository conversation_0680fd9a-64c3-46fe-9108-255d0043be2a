#!/usr/bin/env python3
"""
Test the fixed semantic pattern matching system.
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path to import the tool
sys.path.append(str(Path(__file__).parent))

from open_webui_code_analyzer_tool import Tools

async def test_fixed_semantic_enhancement():
    """Test the fixed semantic pattern matching"""
    print("🔍 Testing Fixed Semantic Pattern Matching")
    print("=" * 60)
    
    # Initialize the tool
    tool = Tools()
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    
    # Clear cache to test fresh
    if tool.cache:
        await tool.clear_cache()
    
    # Ensure utils is selected
    print("1. Selecting utils codebase...")
    await tool.select_codebase("utils")
    
    # Test the previously problematic queries
    test_queries = [
        # These should now get correct semantic patterns
        "find network protocol handling",  # Should get network patterns, not error patterns
        "how is input/output handled",     # Should get I/O patterns, not debug patterns
        "show me timeout handling",        # Should get timer patterns
        
        # Test other domains
        "show me error reporting",         # Should get error patterns
        "find memory allocation",          # Should get memory patterns
        "show me configuration settings",  # Should get config patterns
        "find initialization functions",   # Should get init patterns
        "show me cleanup code",            # Should get cleanup patterns
        "find debug logging",              # Should get debug patterns
        
        # Edge cases
        "network error handling",          # Should prioritize network over error
        "memory allocation errors",        # Should prioritize memory over error
        "timer configuration",             # Should prioritize timer over config
    ]
    
    successful = 0
    failed = 0
    pattern_correct = 0
    
    for i, query in enumerate(test_queries, 2):
        print(f"\n{i}. Testing: '{query}'")
        try:
            result = await tool.get_code_context(query, codebase_name="utils", n_results=10)
            
            if "❌ Unable to retrieve code context" in result or "Context retrieval failed" in result:
                print(f"   ❌ FAILED: Context retrieval failed")
                failed += 1
            elif len(result) > 300:
                print(f"   ✅ SUCCESS: Got {len(result)} characters")
                successful += 1
                
                # Check for semantic relevance
                query_words = query.lower().split()
                if any(word in result.lower() for word in query_words):
                    print(f"   ✅ Contains semantically relevant content")
                    pattern_correct += 1
                else:
                    print(f"   ⚠️ Content might be generic")
            else:
                print(f"   ⚠️ SHORT: {len(result)} characters")
                failed += 1
                
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
            failed += 1
    
    print(f"\n" + "=" * 60)
    print(f"📊 FIXED SEMANTIC RESULTS: {successful} successful, {failed} failed")
    success_rate = successful/(successful+failed)*100 if (successful+failed) > 0 else 0
    pattern_accuracy = pattern_correct/successful*100 if successful > 0 else 0
    
    print(f"🎯 SUCCESS RATE: {success_rate:.1f}%")
    print(f"🎯 PATTERN ACCURACY: {pattern_accuracy:.1f}%")
    
    if success_rate >= 80:
        print("🎉 OUTSTANDING! Fixed semantic enhancement is working excellently!")
    elif success_rate >= 70:
        print("🎉 EXCELLENT! Fixed semantic enhancement achieved 70%+ success rate!")
    elif success_rate >= 60:
        print("✅ GOOD! Fixed semantic enhancement is working well!")
    else:
        print("⚠️ Fixed semantic enhancement still needs work")
    
    print("🎉 Fixed semantic enhancement test completed!")

if __name__ == "__main__":
    try:
        asyncio.run(test_fixed_semantic_enhancement())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
