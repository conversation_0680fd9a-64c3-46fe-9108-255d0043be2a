#!/usr/bin/env python3
"""
Test the code_analyzer_tools tool specifically
"""

import requests
import json
from datetime import datetime

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_openwebui_endpoints():
    """Test different OpenWebUI API endpoints"""
    print("🔍 Testing OpenWebUI API Endpoints")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    endpoints_to_test = [
        ("/api/models", "List available models"),
        ("/api/tools", "List available tools/tools"),
        ("/api/chat", "Main chat endpoint"),
        ("/api/chat/completions", "Chat completions endpoint"),
    ]
    
    for endpoint, description in endpoints_to_test:
        try:
            response = session.get(f"{OPENWEBUI_URL}{endpoint}", timeout=10)
            print(f"📡 {endpoint}: {response.status_code} - {description}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if endpoint == "/api/tools" and isinstance(data, list):
                        print(f"   Found {len(data)} tools:")
                        for tool in data:
                            tool_id = tool.get('id', tool.get('name', 'Unknown'))
                            enabled = tool.get('enabled', 'Unknown')
                            print(f"   - {tool_id} (enabled: {enabled})")
                            
                    elif endpoint == "/api/models" and isinstance(data, list):
                        print(f"   Found {len(data)} models:")
                        for model in data[:3]:  # Show first 3
                            model_id = model.get('id', model.get('name', 'Unknown'))
                            print(f"   - {model_id}")
                            
                except json.JSONDecodeError:
                    print(f"   Non-JSON response (length: {len(response.text)})")
                    
        except Exception as e:
            print(f"❌ {endpoint}: {e}")

def test_with_codebase_analyzer():
    """Test chat with codebase_analyzer specifically enabled"""
    print(f"\n🧪 Testing with codebase_analyzer Tool")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test with the proper OpenWebUI chat API
    test_queries = [
        "status",
        "list codebases", 
        "select codebase utils"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Testing: '{query}'")
        print("-" * 30)
        
        # Try main chat endpoint with tools
        try:
            payload = {
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": query}],
                "tools": ["codebase_analyzer"],
                "stream": False,
                "temperature": 0.7,
                "max_tokens": 1000
            }
            
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            print(f"📡 /api/chat: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    # OpenWebUI chat endpoint might return different format
                    content = ""
                    if 'message' in result:
                        content = result['message'].get('content', '')
                    elif 'choices' in result:
                        content = result['choices'][0]['message']['content']
                    elif 'response' in result:
                        content = result['response']
                    else:
                        content = str(result)
                    
                    print(f"✅ Response received ({len(content)} chars)")
                    
                    # Check for tool indicators
                    tool_indicators = ["🔧", "📚", "✅", "RAG System", "Available Codebases"]
                    found = [ind for ind in tool_indicators if ind in content]
                    
                    if found:
                        print(f"🎉 PLUGIN WORKING! Found: {found}")
                    else:
                        print("⚠️ No tool indicators found")
                    
                    # Show response preview
                    preview = content[:200] + "..." if len(content) > 200 else content
                    print(f"Preview: {preview}")
                    
                except json.JSONDecodeError:
                    print(f"Non-JSON response: {response.text[:200]}...")
                    
            else:
                print(f"❌ Error: {response.text[:200]}...")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def test_without_tools():
    """Test the same queries without tools for comparison"""
    print(f"\n🔄 Testing WITHOUT Tools (for comparison)")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    try:
        payload = {
            "model": "llama3:latest",
            "messages": [{"role": "user", "content": "status"}],
            "stream": False,
            "temperature": 0.7,
            "max_tokens": 500
        }
        
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            print(f"✅ Response without tools ({len(content)} chars)")
            preview = content[:200] + "..." if len(content) > 200 else content
            print(f"Preview: {preview}")
            
        else:
            print(f"❌ Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main test function"""
    print("🧪 Code RAG Tools Tool Test")
    print("=" * 60)
    print(f"Target: {OPENWEBUI_URL}")
    print(f"Tool: codebase_analyzer")
    print(f"Time: {datetime.now()}")
    
    # Run tests
    test_openwebui_endpoints()
    test_with_codebase_analyzer()
    test_without_tools()
    
    print(f"\n📋 Analysis:")
    print("1. Check if codebase_analyzer appears in the tools list")
    print("2. Compare responses with vs without tools")
    print("3. Look for tool indicators (emojis, formatting)")
    
    print(f"\n🎯 Expected with working tool:")
    print("- Formatted responses with emojis")
    print("- Your actual codebase names")
    print("- Structured status reports")

if __name__ == "__main__":
    main()
