/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2023 */
/*****************************************************************************/
/*                                                                           */
/* This file is the property of:                                             */
/*                                                                           */
/*                       Triangle MicroWorks, Inc.                           */
/*                      Raleigh, North Carolina USA                          */
/*                       www.TriangleMicroWorks.com                          */
/*                          (919) 870-6615                                   */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*                                                                           */
/*****************************************************************************/

/* file: THtmwsim.c
 * description: Implement generic simulation functionality that can be used
 *  for both master and Outstation/ControlledStation/Server simulations.
 */
#include "tmwscl/utils/tmwtarg.h"
#include "tmwscl/utils/tmwcnfg.h"
#include "tmwscl/utils/tmwsim.h"
#include "tmwscl/utils/THtmwsim.h"

#if TMWCNFG_USE_SIMULATED_DB
#if TMW_USE_BINARY_TREE

#if !TMWCNFG_USE_DYNAMIC_MEMORY
#pragma message("If !TMWCNFG_USE_DYNAMIC_MEMORY, TMW_USE_BINARY_TREE is not allowed")
the build needs to fail 
#endif

void InfoDest(void *a)
{
  /* delete the simulated point structure pointed to by the tree node */
  tmwsim_deletePoint((TMWSIM_POINT *)a);
}

TMWTYPES_BOOL TMWDEFS_CALLBACK tmwsim_tableCreate(
  TMWSIM_TABLE_HEAD *pTableHead)
{
  TMWTREE_TREE *pTree = tmwtree_create(InfoDest);
  *pTableHead = pTree;

  TMWTARG_LOCK_INIT(&pTree->lock);
  return(TMWDEFS_TRUE);
}

void TMWDEFS_CALLBACK tmwsim_tableDestroy(
  TMWSIM_TABLE_HEAD *pTableHead)
{
  TMWSIM_TABLE *pTable = *pTableHead;
  if(pTable == TMWDEFS_NULL)
    return;

  TMWTARG_LOCK_DELETE(&pTable->lock);
  tmwtree_destroy(pTable);
  
  *pTableHead = TMWDEFS_NULL;
} 

TMWSIM_POINT * TMWDEFS_CALLBACK tmwsim_tableAdd(
  TMWSIM_TABLE_HEAD *pTableHead,
  TMWTYPES_ULONG pointNum)
{
  TMWSIM_POINT *pPoint;
  TMWSIM_TABLE *pTable = *pTableHead;
  if(pTable == TMWDEFS_NULL)
    return(TMWDEFS_NULL);

  /* If point already exists return failure */
  if(tmwsim_tableFindPoint(pTableHead, pointNum))
  {
    return(TMWDEFS_NULL);
  }

  TMWTARG_LOCK_SECTION(&pTable->lock);
  pPoint = tmwsim_newPoint();
  if(pPoint != TMWDEFS_NULL)
  { 
    TMWTREE_NODE *pNode;
    pNode = tmwtree_insert(pTable, pointNum, pPoint);
    if(pNode != TMWDEFS_NULL)
    {
      TMWTARG_UNLOCK_SECTION(&pTable->lock);
      return(pPoint);
    }
    tmwsim_deletePoint(pPoint);
  }

  TMWTARG_UNLOCK_SECTION(&pTable->lock);
  return(TMWDEFS_NULL);
} 

TMWTYPES_BOOL TMWDEFS_CALLBACK tmwsim_tableDelete(
  TMWSIM_TABLE_HEAD *pTableHead,
  TMWTYPES_ULONG pointNum)
{ 
  TMWTREE_NODE *pNode;
  TMWSIM_TABLE *pTable = *pTableHead;
  if(pTable == TMWDEFS_NULL)
    return(TMWDEFS_FALSE);

  TMWTARG_LOCK_SECTION(&pTable->lock);
  pNode = tmwtree_exactQuery(pTable, pointNum);
  if (pNode != TMWDEFS_NULL)
  {    
    /* This will also delete TMWSIM_POINT */
    tmwtree_delete(pTable, pNode); 

    pTable->pCurrentNode = TMWDEFS_NULL;
    pTable->currentIndex = 0;

    TMWTARG_UNLOCK_SECTION(&pTable->lock);
    return(TMWDEFS_TRUE);
  }

  TMWTARG_UNLOCK_SECTION(&pTable->lock);
  return(TMWDEFS_FALSE);
}

TMWSIM_POINT * TMWDEFS_CALLBACK tmwsim_tableFindPoint(
  TMWSIM_TABLE_HEAD *pTableHead,
  TMWTYPES_ULONG pointNum)
{
  TMWTREE_NODE *pNode;
  TMWSIM_TABLE *pTable = *pTableHead;
  if(pTable == TMWDEFS_NULL)
    return(TMWDEFS_NULL);
  
  TMWTARG_LOCK_SECTION(&pTable->lock);
  pNode = tmwtree_exactQuery(pTable, pointNum);
  if(pNode != TMWDEFS_NULL)
  {
    pTable->pCurrentNode = pNode;
    pTable->currentIndex = 0;
    TMWTARG_UNLOCK_SECTION(&pTable->lock);
    return((TMWSIM_POINT *)pNode->info);
  }
   
  TMWTARG_UNLOCK_SECTION(&pTable->lock);
  return(TMWDEFS_NULL);
} 
  
TMWSIM_POINT * TMWDEFS_CALLBACK tmwsim_tableGetFirstPoint(
  TMWSIM_TABLE_HEAD *pTableHead)
{
  TMWTREE_NODE *pNode;
  TMWSIM_TABLE *pTable = *pTableHead;
  if(pTable == TMWDEFS_NULL)
    return(TMWDEFS_NULL);

  TMWTARG_LOCK_SECTION(&pTable->lock);
  pNode = tmwtree_getFirst(pTable);
  if(pNode != TMWDEFS_NULL)
  {
    pTable->pCurrentNode = pNode;
    pTable->currentIndex = 0;
    TMWTARG_UNLOCK_SECTION(&pTable->lock);
    return((TMWSIM_POINT *)pNode->info);
  }
   
  TMWTARG_UNLOCK_SECTION(&pTable->lock);
  return(TMWDEFS_NULL);
} 

TMWSIM_POINT * TMWDEFS_CALLBACK tmwsim_tableGetLastPoint(
  TMWSIM_TABLE_HEAD *pTableHead)
{
  TMWTREE_NODE *pNode;
  TMWSIM_TABLE *pTable = *pTableHead;
  if(pTable == TMWDEFS_NULL)
    return(TMWDEFS_NULL);

  TMWTARG_LOCK_SECTION(&pTable->lock);
  pNode = tmwtree_getLast(pTable);
  if(pNode != TMWDEFS_NULL)
  {
    pTable->pCurrentNode = pNode;
    pTable->currentIndex = 0;
    TMWTARG_UNLOCK_SECTION(&pTable->lock);
    return((TMWSIM_POINT *)pNode->info);
  }
    
  TMWTARG_UNLOCK_SECTION(&pTable->lock);
  return(TMWDEFS_NULL);
} 

TMWSIM_POINT * TMWDEFS_CALLBACK tmwsim_tableGetNextPoint(
  TMWSIM_TABLE_HEAD *pTableHead,
  TMWSIM_POINT *pPoint)
{
  TMWSIM_TABLE *pTable = *pTableHead;
  TMWTREE_NODE *pNextNode;
 
  /* If caller specified NULL then return the first point. */
  if(pPoint == TMWDEFS_NULL)
  {
    return(tmwsim_tableGetFirstPoint(pTableHead));
  }

  TMWTARG_LOCK_SECTION(&pTable->lock);
  /* If the point passed in was not the last one returned 
   * find that one in the list, so next can be returned
   */
  if((pTable->pCurrentNode == TMWDEFS_NULL) 
    || (pTable->pCurrentNode->info != pPoint))
  {
    /* This will set pCurrentNode if successful */
    if(tmwsim_tableFindPoint(pTableHead, pPoint->pointNumber) == TMWDEFS_NULL)
    {
      TMWTARG_UNLOCK_SECTION(&pTable->lock);
      return(TMWDEFS_NULL);
    }
  }
 
  pNextNode = tmwtree_successor(pTable, pTable->pCurrentNode);

  /* Because successor can return nil node, check for info == NULL */
  if((pNextNode != TMWDEFS_NULL) && (pNextNode->info != TMWDEFS_NULL))
  { 
    pTable->pCurrentNode = pNextNode;
    pTable->currentIndex = 0;
    TMWTARG_UNLOCK_SECTION(&pTable->lock);
    return((TMWSIM_POINT *)pNextNode->info);
  }
    
  TMWTARG_UNLOCK_SECTION(&pTable->lock);
  return(TMWDEFS_NULL);
}

TMWSIM_POINT * TMWDEFS_CALLBACK tmwsim_tableFindPointByIndex(
  TMWSIM_TABLE_HEAD *pTableHead,
  TMWTYPES_USHORT pointIndex)
{
  int i;
  TMWTREE_NODE *pNode;
  TMWSIM_TABLE *pTable = *pTableHead;

  if(pointIndex >= tmwsim_tableSize(pTableHead))
    return(TMWDEFS_NULL);

  TMWTARG_LOCK_SECTION(&pTable->lock);
  /* if pointIndex != 0, check to see if we are getting the same one again */
  if(pointIndex && (pointIndex == pTable->currentIndex))
  {
    pNode = pTable->pCurrentNode;
  }
  /* if pointIndex > 1, check to see if we are getting the next one 
   * since currentIndex is set to 0, looking for pointIndex 1 can be 
   * a problem
   */
  else if(pointIndex>1 && (pointIndex == pTable->currentIndex+1))
  {
    pNode = tmwtree_successor(pTable, pTable->pCurrentNode);
  }
  else
  {
    /* Oh well, walk through the tree till we get to pointIndex */
    pNode = tmwtree_getFirst(pTable);
    for(i=0; i<pointIndex; i++)
    { 
      if(pNode == TMWDEFS_NULL)
        break;
      
      pNode = tmwtree_successor(pTable, pNode);
    }
  }

  if((pNode != TMWDEFS_NULL) && (pNode->info != TMWDEFS_NULL))
  { 
    pTable->pCurrentNode = pNode;
    pTable->currentIndex = pointIndex;
    TMWTARG_UNLOCK_SECTION(&pTable->lock);
    return((TMWSIM_POINT *)pNode->info);
  }

  pTable->currentIndex = 0;
     
  TMWTARG_UNLOCK_SECTION(&pTable->lock);
  return(TMWDEFS_NULL);
}

TMWTYPES_UINT TMWDEFS_CALLBACK tmwsim_tableSize(
  TMWSIM_TABLE_HEAD *pTableHead)
{
  TMWTREE_TREE *pTree = *pTableHead;
  if (pTree != TMWDEFS_NULL)
    return(pTree->quantity);
  return 0;
}

#endif
#endif /* TMWCNFG_USE_SIMULATED_DB */
