#!/usr/bin/env python3
"""
OpenWebUI Browser-Based Tester
Opens your OpenWebUI in browser and provides prompts to copy/paste
"""

import webbrowser
import time
import pyperclip  # type: ignore  # pip install pyperclip
from typing import List, Dict, Union

OPENWEBUI_URL = "http://home-ai-server.local:8080"

def get_test_prompts() -> List[Dict[str, Union[str, List[str]]]]:
    """Get organized test prompts"""
    return [
        # Quick Start Tests
        {"category": "🚀 Quick Start", "prompts": [
            "status",
            "detailed status", 
            "list codebases",
            "select codebase utils"
        ]},
        
        # Code Analysis Tests  
        {"category": "🔍 Code Analysis", "prompts": [
            "find memory allocation functions",
            "How does memory management work in this codebase?",
            "search for error handling code",
            "What security measures are in place?"
        ]},
        
        # Language-Specific Tests
        {"category": "💻 Language-Specific", "prompts": [
            "show me all C++ classes",
            "find C function definitions", 
            "search for struct definitions",
            "look for template implementations"
        ]},
        
        # Advanced Analysis
        {"category": "🎯 Advanced Analysis", "prompts": [
            "explain the overall architecture",
            "what design patterns are used?",
            "compare memory allocation between different modules",
            "Analyze the complete memory management strategy across all modules and explain how it prevents leaks while maintaining performance"
        ]},
        
        # Edge Cases
        {"category": "🧪 Edge Cases", "prompts": [
            "select codebase nonexistent",
            "what's the weather like?",
            "find malloc",
            ""
        ]}
    ]

def copy_to_clipboard(text: str):
    """Copy text to clipboard"""
    try:
        pyperclip.copy(text)
        return True
    except Exception:
        return False

def interactive_browser_testing():
    """Interactive browser-based testing"""
    print("🌐 OpenWebUI Browser-Based Testing")
    print("=" * 50)
    
    # Open OpenWebUI in browser
    print(f"🚀 Opening OpenWebUI at: {OPENWEBUI_URL}")
    try:
        webbrowser.open(OPENWEBUI_URL)
        print("✅ Browser opened")
    except Exception as e:
        print(f"❌ Failed to open browser: {e}")
        print(f"Please manually open: {OPENWEBUI_URL}")
    
    time.sleep(2)
    
    print("\n📋 Test Instructions:")
    print("1. Make sure your Code Analysis tool is installed and active")
    print("2. I'll show you prompts to test")
    print("3. Copy each prompt and paste it into OpenWebUI chat")
    print("4. Observe the responses and note any issues")
    print("\nPress Enter when ready to start...")
    input()
    
    # Get test prompts
    test_categories = get_test_prompts()
    
    for category_data in test_categories:
        category = category_data["category"]
        prompts = category_data["prompts"]
        
        print(f"\n{category}")
        print("=" * 60)
        
        for i, prompt in enumerate(prompts, 1):
            print(f"\n🧪 Test {i}/{len(prompts)}")
            print(f"📝 Prompt: '{prompt}'")
            
            # Try to copy to clipboard
            if copy_to_clipboard(prompt):
                print("📋 ✅ Copied to clipboard - paste into OpenWebUI")
            else:
                print("📋 ❌ Copy failed - please manually copy the prompt above")
            
            # Wait for user
            user_input = input("\nPress Enter for next prompt, 's' to skip category, 'q' to quit: ").strip().lower()
            
            if user_input == 'q':
                print("👋 Testing stopped by user")
                return
            elif user_input == 's':
                print("⏭️ Skipping to next category")
                break
    
    print("\n🎉 All test prompts completed!")
    print("\n📊 What to look for:")
    print("✅ Status commands return formatted health info")
    print("✅ Codebase selection works and persists") 
    print("✅ Code searches return relevant snippets")
    print("✅ AI analysis provides meaningful insights")
    print("✅ Management queries are auto-routed")
    print("✅ Context format adapts to query complexity")

def batch_prompt_generator():
    """Generate all prompts for batch testing"""
    print("📝 Batch Prompt Generator")
    print("=" * 30)
    
    test_categories = get_test_prompts()
    all_prompts = []
    
    for category_data in test_categories:
        all_prompts.extend(category_data["prompts"])
    
    print(f"Generated {len(all_prompts)} test prompts:")
    print("\n" + "="*50)
    
    for i, prompt in enumerate(all_prompts, 1):
        print(f"{i:2d}. {prompt}")
    
    print("\n" + "="*50)
    
    # Save to file
    filename = "batch_test_prompts.txt"
    try:
        with open(filename, 'w') as f:
            for i, prompt in enumerate(all_prompts, 1):
                f.write(f"{i:2d}. {prompt}\n")
        print(f"💾 Prompts saved to: {filename}")
    except Exception as e:
        print(f"❌ Failed to save: {e}")
    
    # Copy all to clipboard
    all_text = "\n".join(f"{i:2d}. {prompt}" for i, prompt in enumerate(all_prompts, 1))
    if copy_to_clipboard(all_text):
        print("📋 All prompts copied to clipboard!")

def main():
    """Main function"""
    print("🧪 OpenWebUI Code Analysis Tool Browser Tester")
    print("=" * 50)
    
    # Check if pyperclip is available
    try:
        import pyperclip
        clipboard_available = True
    except ImportError:
        clipboard_available = False
        print("⚠️  pyperclip not installed - clipboard features disabled")
        print("   Install with: pip install pyperclip")
    
    print("\nChoose testing mode:")
    print("1. Interactive browser testing (recommended)")
    print("2. Generate batch prompts for manual testing")
    print("3. Both")
    
    try:
        choice = input("\nEnter choice (1-3): ").strip()
        
        if choice == "1":
            interactive_browser_testing()
        elif choice == "2":
            batch_prompt_generator()
        elif choice == "3":
            batch_prompt_generator()
            print("\n" + "="*50)
            interactive_browser_testing()
        else:
            print("Invalid choice. Running interactive mode...")
            interactive_browser_testing()
            
    except KeyboardInterrupt:
        print("\n\n👋 Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
