#!/usr/bin/env python3
"""
Test the tool after manual installation in OpenWebUI
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_installed_tool():
    """Test if the tool is working after installation"""
    print("🧪 Testing Tool After Installation")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test 1: Codebase selection
    print("\n🎯 Test 1: Codebase Selection")
    print("-" * 30)
    
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "select codebase utils"}],
                "tool_ids": ["code_analyzer_tool"],  # Adjust if tool ID is different
                "stream": False,
                "max_tokens": 500
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"✅ Response ({len(content)} chars)")
            print(f"Preview: {content[:300]}...")
            
            # Check for success indicators
            if "Selected Enhanced Codebase: utils" in content:
                print("🎉 SUCCESS: Tool is working! Codebase selection successful!")
            elif "popular utility libraries" in content:
                print("❌ FAILED: Still getting generic response - tool not properly installed/enabled")
            else:
                print("⚠️ UNCLEAR: Response doesn't match expected patterns")
        else:
            print(f"❌ Request failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Test error: {e}")
    
    # Test 2: List codebases
    print("\n📚 Test 2: List Codebases")
    print("-" * 30)
    
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "list codebases"}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 500
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"✅ Response ({len(content)} chars)")
            
            # Check for actual codebase names
            if any(name in content for name in ["utils", "z80emu", "modbus", "networking_project"]):
                print("🎉 SUCCESS: Actual codebases listed!")
                print(f"Preview: {content[:200]}...")
            else:
                print("❌ FAILED: Generic response instead of actual codebases")
                print(f"Preview: {content[:200]}...")
        else:
            print(f"❌ Request failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Test error: {e}")
    
    # Test 3: Code search
    print("\n🔍 Test 3: Code Search")
    print("-" * 30)
    
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "search code memory allocation"}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 500
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"✅ Response ({len(content)} chars)")
            
            # Check for actual code indicators
            if any(indicator in content for indicator in ["tmwmem", "tmwdiag", ".c", ".cpp"]):
                print("🎉 SUCCESS: Actual code found!")
                print(f"Preview: {content[:200]}...")
            else:
                print("❌ FAILED: Generic response instead of actual code")
                print(f"Preview: {content[:200]}...")
        else:
            print(f"❌ Request failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Test error: {e}")
    
    print("\n📋 SUMMARY")
    print("=" * 30)
    print("If all tests show SUCCESS, the tool is working correctly!")
    print("If tests show FAILED, check:")
    print("1. Tool is properly installed in OpenWebUI")
    print("2. Tool is enabled for llama3:latest model")
    print("3. Tool ID matches what's used in the API call")

if __name__ == "__main__":
    test_installed_tool()
