#!/usr/bin/env python3
"""
Test OpenWebUI plugin routing to understand why codebase selection isn't working
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_plugin_routing():
    """Test different ways to call the plugin functions"""
    print("🔧 Testing OpenWebUI Plugin Routing")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test 1: Direct function call via OpenWebUI API
    print("\n🧪 Test 1: Direct select_codebase function call")
    print("-" * 50)
    
    try:
        # Try to call the function directly using OpenWebUI's tool calling mechanism
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [
                    {
                        "role": "user", 
                        "content": "Call the select_codebase function with parameter 'utils'"
                    }
                ],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 500
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"✅ Direct function call response ({len(content)} chars)")
            print(f"Preview: {content[:300]}...")
            
            # Check if it contains actual selection success
            if "Selected Enhanced Codebase: utils" in content:
                print("🎉 Direct function call worked!")
            else:
                print("❌ Direct function call failed")
        else:
            print(f"❌ Direct function call failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Direct function call error: {e}")
    
    # Test 2: Natural language query
    print("\n🧪 Test 2: Natural language 'select codebase utils'")
    print("-" * 50)
    
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "select codebase utils"}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 500
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"✅ Natural language response ({len(content)} chars)")
            print(f"Preview: {content[:300]}...")
            
            # Check if it contains actual selection success
            if "Selected Enhanced Codebase: utils" in content:
                print("🎉 Natural language routing worked!")
            elif "popular utility libraries" in content:
                print("❌ Natural language routing failed - got generic response")
            else:
                print("⚠️ Natural language routing unclear")
        else:
            print(f"❌ Natural language failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Natural language error: {e}")
    
    # Test 3: Check what functions are available
    print("\n🧪 Test 3: Check available functions")
    print("-" * 50)
    
    try:
        response = session.get(
            f"{OPENWEBUI_URL}/api/tools",
            timeout=30
        )
        
        if response.status_code == 200:
            tools = response.json()
            print(f"✅ Found {len(tools)} tools")
            
            # Look for our tool
            for tool in tools:
                if tool.get('id') == 'code_analyzer_tools':
                    print(f"🎯 Found code_analyzer_tools:")
                    print(f"   Name: {tool.get('name', 'N/A')}")
                    print(f"   Description: {tool.get('description', 'N/A')[:100]}...")
                    
                    # Check if it has function definitions
                    if 'functions' in tool:
                        functions = tool['functions']
                        print(f"   Functions: {len(functions)}")
                        for func in functions:
                            print(f"     - {func.get('name', 'unnamed')}")
                    break
            else:
                print("❌ code_analyzer_tools not found in available tools")
        else:
            print(f"❌ Tools list failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Tools list error: {e}")

if __name__ == "__main__":
    test_plugin_routing()
