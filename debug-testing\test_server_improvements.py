#!/usr/bin/env python3
"""
Test server-side improvements with fresh queries (no cache)
"""

import asyncio
import sys
import os

# Add the current directory to Python path so we can import the tool
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the tool
from open_webui_code_analyzer_tool import Tools

class MockEventEmitter:
    """Mock event emitter for testing"""
    def __init__(self, verbose=True):
        self.verbose = verbose
        self.events = []
    
    async def __call__(self, event):
        """Mock event emission"""
        if self.verbose:
            event_type = event.get("type", "unknown")
            data = event.get("data", {})
            description = data.get("description", "No description")
            done = data.get("done", False)
            status = "✅" if done else "🔄"
            print(f"   {status} Event: {description}")
        
        self.events.append(event)

class ServerImprovementTester:
    """Test server improvements with fresh queries"""
    
    def __init__(self):
        self.tool = Tools()
        self.emitter = MockEventEmitter(verbose=True)
        
        # Configure for our test environment
        self.tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
        self.tool.valves.current_codebase = "utils"
        self.tool.valves.enable_caching = False  # DISABLE CACHE
        
        print(f"🔧 Configured for server improvement testing")
        print(f"🔧 Server: {self.tool.valves.code_analyzer_server_url}")
        print(f"🔧 Codebase: {self.tool.valves.current_codebase}")
        print(f"🔧 Caching: {self.tool.valves.enable_caching}")
    
    async def test_server_improvements(self):
        """Test server improvements with fresh queries"""
        print("\n" + "="*80)
        print("🚀 TESTING SERVER-SIDE IMPROVEMENTS")
        print("="*80)
        
        # Clear any existing cache first
        await self.tool.clear_cache(__event_emitter__=self.emitter)
        
        # Test queries that should benefit from server improvements
        test_queries = [
            # Exact function name matching (should work with exact matching)
            "tmwmem_alloc",
            "tmwdiag_error", 
            "tmwmem_free",
            "mbmem_alloc",
            
            # Constants/macros (should work with exact matching)
            "TMWMEM_HEADER",
            "TMWMEM_POOL_STRUCT",
            
            # Error handling (should work with fallback search)
            "error handling functions",
            "diagnostic functions",
            
            # Memory management (should work with enhanced search)
            "memory allocation functions",
            "buffer management code"
        ]
        
        results = []
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n[{i}/{len(test_queries)}] Testing: '{query}'")
            print("-" * 60)
            
            # Reset event tracking
            self.emitter.events = []
            
            try:
                # Call get_code_context directly to bypass cache
                result = await self.tool.get_code_context(
                    query=query,
                    codebase_name="utils",
                    n_results=5,
                    __event_emitter__=self.emitter
                )
                
                # Analyze results
                chunks_found = 0
                if self.emitter.events:
                    final_event = self.emitter.events[-1]
                    final_description = final_event.get("data", {}).get("description", "")
                    
                    import re
                    chunk_match = re.search(r'(\d+) chunk', final_description)
                    if chunk_match:
                        chunks_found = int(chunk_match.group(1))
                
                success = chunks_found > 0 and "No relevant code context found" not in str(result)
                
                results.append({
                    "query": query,
                    "chunks_found": chunks_found,
                    "success": success,
                    "result_length": len(result) if result else 0
                })
                
                print(f"   Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
                print(f"   Chunks: {chunks_found}")
                print(f"   Length: {len(result) if result else 0} chars")
                
                if result and len(result) > 0:
                    preview = str(result)[:150].replace('\n', ' ')
                    print(f"   Preview: {preview}...")
                
            except Exception as e:
                print(f"   ❌ ERROR: {e}")
                results.append({
                    "query": query,
                    "chunks_found": 0,
                    "success": False,
                    "error": str(e)
                })
        
        # Generate summary
        self.print_improvement_summary(results)
        
        return results
    
    def print_improvement_summary(self, results):
        """Print summary of server improvement testing"""
        print(f"\n{'='*80}")
        print("📊 SERVER IMPROVEMENT TEST SUMMARY")
        print(f"{'='*80}")
        
        total_tests = len(results)
        successful_tests = sum(1 for r in results if r["success"])
        failed_tests = total_tests - successful_tests
        
        print(f"\n🎯 OVERALL RESULTS:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Successful: {successful_tests} ({successful_tests/total_tests*100:.1f}%)")
        print(f"   Failed: {failed_tests} ({failed_tests/total_tests*100:.1f}%)")
        
        # Group by query type
        exact_matches = [r for r in results if len(r["query"].split()) == 1 and r["query"].islower()]
        constants = [r for r in results if r["query"].isupper()]
        natural_language = [r for r in results if len(r["query"].split()) > 2]
        
        print(f"\n📋 RESULTS BY QUERY TYPE:")
        
        if exact_matches:
            successful = sum(1 for r in exact_matches if r["success"])
            print(f"   Exact Function Names: {successful}/{len(exact_matches)} successful")
            for r in exact_matches:
                status = "✅" if r["success"] else "❌"
                print(f"     {status} {r['query']} - {r['chunks_found']} chunks")
        
        if constants:
            successful = sum(1 for r in constants if r["success"])
            print(f"   Constants/Macros: {successful}/{len(constants)} successful")
            for r in constants:
                status = "✅" if r["success"] else "❌"
                print(f"     {status} {r['query']} - {r['chunks_found']} chunks")
        
        if natural_language:
            successful = sum(1 for r in natural_language if r["success"])
            print(f"   Natural Language: {successful}/{len(natural_language)} successful")
            for r in natural_language:
                status = "✅" if r["success"] else "❌"
                print(f"     {status} {r['query']} - {r['chunks_found']} chunks")
        
        # Show successful queries
        successful_results = [r for r in results if r["success"]]
        if successful_results:
            print(f"\n🏆 SUCCESSFUL QUERIES:")
            for result in successful_results:
                print(f"   ✅ '{result['query']}' - {result['chunks_found']} chunks")
        
        # Show failed queries
        failed_results = [r for r in results if not r["success"]]
        if failed_results:
            print(f"\n❌ FAILED QUERIES:")
            for result in failed_results:
                print(f"   ❌ '{result['query']}' - {result.get('error', 'No results found')}")
        
        print(f"\n{'='*80}")

async def main():
    """Main test execution"""
    tester = ServerImprovementTester()
    await tester.test_server_improvements()

if __name__ == "__main__":
    asyncio.run(main())
