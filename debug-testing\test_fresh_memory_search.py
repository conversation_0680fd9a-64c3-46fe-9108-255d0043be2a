#!/usr/bin/env python3
"""
Test memory search with fresh queries (no cache).
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path to import the tool
sys.path.append(str(Path(__file__).parent))

from open_webui_code_analyzer_tool import Tools

async def test_fresh_memory_search():
    """Test memory search with cache cleared"""
    print("🔍 Testing Fresh Memory Search (Cache Cleared)")
    print("=" * 60)
    
    # Initialize the tool
    tool = Tools()
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    
    # Clear cache first
    print("1. Clearing cache...")
    if tool.cache:
        await tool.clear_cache()
        print("   ✅ Cache cleared")
    else:
        print("   ⚠️ No cache to clear")
    
    # Ensure utils is selected
    print("\n2. Selecting utils codebase...")
    await tool.select_codebase("utils")
    
    # Test with a unique query to avoid cache
    unique_queries = [
        "tmwmem functions in utils",  # More specific
        "memory allocation in C code",
        "tmwmem_lowFree function",  # Very specific function we know exists
    ]
    
    for i, query in enumerate(unique_queries, 3):
        print(f"\n{i}. Testing fresh query: '{query}'")
        
        try:
            result = await tool.get_code_context(query, codebase_name="utils", n_results=15)
            
            # Count chunks using multiple methods
            chunk_count_context = result.count("## Context")
            chunk_count_found = 0
            
            import re
            match = re.search(r'Found (\d+) relevant code section', result)
            if match:
                chunk_count_found = int(match.group(1))
            
            code_blocks = result.count("```") // 2
            
            print(f"   Result length: {len(result)} characters")
            print(f"   Chunk count (## Context): {chunk_count_context}")
            print(f"   Chunk count (Found X): {chunk_count_found}")
            print(f"   Code blocks: {code_blocks}")
            
            if chunk_count_found > 1:
                print(f"   ✅ SUCCESS: Found {chunk_count_found} chunks!")
                print(f"   First 200 chars: {result[:200]}...")
                break
            elif len(result) > 100:
                print(f"   📄 Got content but chunk counting may be off")
                print(f"   First 200 chars: {result[:200]}...")
            else:
                print(f"   ⚠️ Minimal content returned")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 Fresh memory search test completed!")

if __name__ == "__main__":
    try:
        asyncio.run(test_fresh_memory_search())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
