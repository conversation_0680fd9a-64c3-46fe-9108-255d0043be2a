#!/usr/bin/env python3
"""
Verify tool installation after setup
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_after_installation():
    """Test tool after installation"""
    print("✅ Tool Installation Verification")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test with a simple status query
    print("🧪 Testing 'status' query...")
    
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "status"}],
                "stream": False,
                "max_tokens": 500
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            print(f"Response received ({len(content)} chars)")
            print("\nFull response:")
            print("-" * 40)
            print(content)
            print("-" * 40)
            
            # Check for tool indicators
            tool_indicators = [
                "🔧", "📚", "✅", "❌", "🚀",  # Emojis
                "RAG System Status", "Tool Server", "Online",
                "Available Codebases", "Current Selection"
            ]
            
            found_indicators = [ind for ind in tool_indicators if ind in content]
            
            if found_indicators:
                print(f"\n🎉 PLUGIN IS WORKING!")
                print(f"Found indicators: {found_indicators}")
            else:
                print(f"\n❌ PLUGIN STILL NOT WORKING")
                print("Response is still generic LLM text")
                
        else:
            print(f"❌ HTTP {response.status_code}: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    print("🔍 Run this AFTER installing the tool in OpenWebUI")
    print("=" * 60)
    
    input("Press Enter after you've installed the tool...")
    test_after_installation()
    
    print(f"\n📋 If tool is still not working:")
    print("1. Check OpenWebUI logs for errors")
    print("2. Verify tool is enabled for llama3:latest model")
    print("3. Try restarting OpenWebUI")
    print("4. Test with browser interface directly")

if __name__ == "__main__":
    main()
