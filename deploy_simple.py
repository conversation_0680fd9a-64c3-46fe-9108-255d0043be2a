#!/usr/bin/env python3
"""
Simplified Remote Deployment Script
Deploys enhanced framework from lynn-pc to home-ai-server
Supports password authentication and manual steps
"""

import subprocess
import sys
import time
from pathlib import Path

def run_local_command(command: str) -> bool:
    """Run a command locally and return success status"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {command}")
            return True
        else:
            print(f"❌ {command} failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {command} error: {e}")
        return False

def verify_local_files() -> bool:
    """Verify all required files are present locally"""
    print("🔍 Verifying Local Framework Files...")
    
    required_files = [
        "main.py", "framework_integration.py", "language_registry.py",
        "language_framework.py", "language_processors.py", "processing_pipeline.py",
        "chunk_system.py", "gpu_infrastructure.py", "semantic_patterns.py",
        "metta_processor.py", "vector_db_creator.py", "code_preprocessor.py",
        "requirements.txt", "Dockerfile", "docker-compose.yml"
    ]
    
    missing = []
    for file in required_files:
        if not Path(file).exists():
            missing.append(file)
        else:
            print(f"✅ {file}")
    
    if missing:
        print(f"❌ Missing files: {missing}")
        return False
    
    print("✅ All framework files verified locally")
    return True

def create_deployment_archive() -> bool:
    """Create a deployment archive"""
    print("📦 Creating Deployment Archive...")
    
    # Create tar archive with all necessary files
    files_to_include = [
        "main.py", "framework_integration.py", "language_registry.py",
        "language_framework.py", "language_processors.py", "processing_pipeline.py",
        "chunk_system.py", "gpu_infrastructure.py", "semantic_patterns.py",
        "metta_processor.py", "vector_db_creator.py", "code_preprocessor.py",
        "requirements.txt", "Dockerfile", "docker-compose.yml"
    ]
    
    # Create file list
    file_list = " ".join([f for f in files_to_include if Path(f).exists()])
    
    command = f"tar -czf enhanced_framework.tar.gz {file_list}"
    return run_local_command(command)

def print_manual_deployment_steps():
    """Print manual deployment steps"""
    print("\n🚀 MANUAL DEPLOYMENT STEPS")
    print("=" * 60)
    
    print("\n📋 Step 1: Copy Files to Home-AI-Server")
    print("Run this command from your current directory:")
    print("scp enhanced_framework.tar.gz fvaneijk@home-ai-server:/home/<USER>/")
    
    print("\n📋 Step 2: SSH to Home-AI-Server")
    print("ssh fvaneijk@home-ai-server")
    
    print("\n📋 Step 3: Extract and Setup (run on home-ai-server)")
    print("cd /home/<USER>")
    print("mkdir -p openwebui_rag_code_server")
    print("cd openwebui_rag_code_server")
    print("tar -xzf ../enhanced_framework.tar.gz")
    
    print("\n📋 Step 4: Deploy Container (run on home-ai-server)")
    print("docker-compose down code-analyzer-server")
    print("docker-compose up -d --build code-analyzer-server")
    
    print("\n📋 Step 5: Verify Deployment (run on home-ai-server)")
    print("docker ps | grep code-analyzer-server")
    print("curl http://localhost:5002/health")
    print("curl http://localhost:5002/tools/framework_status")
    
    print("\n🌐 Access URLs (from lynn-pc):")
    print("Health Check: http://home-ai-server:5002/health")
    print("Framework Status: http://home-ai-server:5002/tools/framework_status")
    print("GPU Status: http://home-ai-server:5002/tools/gpu_status")
    print("OpenWebUI: http://home-ai-server:8080")

def print_ssh_key_setup():
    """Print SSH key setup instructions"""
    print("\n🔑 SSH KEY SETUP (Optional - for automated deployment)")
    print("=" * 60)
    
    print("\n📋 On Lynn-PC (run these commands):")
    print("# Generate SSH key if you don't have one")
    print("ssh-keygen -t rsa -b 4096 -C 'lynn-pc-deployment'")
    print("")
    print("# Copy key to home-ai-server")
    print("ssh-copy-id fvaneijk@home-ai-server")
    print("")
    print("# Test passwordless connection")
    print("ssh fvaneijk@home-ai-server 'echo SSH key setup successful'")
    
    print("\n✅ After SSH key setup, you can use:")
    print("python deploy_enhanced_framework.py")

def run_framework_tests() -> bool:
    """Run local framework tests"""
    print("🧪 Running Framework Tests...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_framework.py"
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ Framework tests passed")
            return True
        else:
            print("⚠️ Framework tests had issues, but proceeding with deployment")
            return True  # Proceed anyway
            
    except subprocess.TimeoutExpired:
        print("⚠️ Framework tests timed out, proceeding with deployment")
        return True
    except Exception as e:
        print(f"⚠️ Could not run framework tests: {e}")
        return True

def main():
    """Main deployment preparation function"""
    print("🚀 Enhanced Framework Deployment Preparation")
    print("=" * 60)
    
    steps = [
        ("Verify Local Files", verify_local_files),
        ("Run Framework Tests", run_framework_tests),
        ("Create Deployment Archive", create_deployment_archive)
    ]
    
    all_passed = True
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            all_passed = False
            break
    
    if all_passed:
        print("\n🎉 Deployment preparation complete!")
        print("✅ Created: enhanced_framework.tar.gz")
        
        print_manual_deployment_steps()
        print_ssh_key_setup()
        
        print("\n🎯 RECOMMENDED NEXT STEPS:")
        print("1. Follow the manual deployment steps above")
        print("2. Or set up SSH keys and use: python deploy_enhanced_framework.py")
        
    else:
        print("\n❌ Deployment preparation failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
