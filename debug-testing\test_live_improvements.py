#!/usr/bin/env python3
"""
Test the improvements based on live user feedback.
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path to import the tool
sys.path.append(str(Path(__file__).parent))

from open_webui_code_analyzer_tool import Tools

async def test_live_improvements():
    """Test the improvements based on live results"""
    print("🔍 Testing Live Improvements Based on User Feedback")
    print("=" * 60)
    
    # Initialize the tool
    tool = Tools()
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    
    # Clear cache to test fresh
    if tool.cache:
        await tool.clear_cache()
    
    # Ensure utils is selected
    print("1. Selecting utils codebase...")
    await tool.select_codebase("utils")
    
    # Test the previously failing queries with improvements
    test_queries = [
        # These failed in live testing - should now work better
        "show me network code",
        "find socket functions", 
        
        # These worked - should still work
        "how does socket communication work",
        "show me TCP code",
        "how is memory managed",
        
        # Additional network variations to test
        "find network communication",
        "show me channel functions",
        "find link layer code",
        
        # Test other domains that might benefit
        "show me functions",
        "find implementation code"
    ]
    
    successful = 0
    failed = 0
    improved = 0
    
    for i, query in enumerate(test_queries, 2):
        print(f"\n{i}. Testing: '{query}'")
        try:
            result = await tool.get_code_context(query, codebase_name="utils", n_results=10)
            
            if "❌ Unable to retrieve code context" in result or "Context retrieval failed" in result:
                print(f"   ❌ FAILED: Context retrieval failed")
                failed += 1
            elif len(result) > 300:
                print(f"   ✅ SUCCESS: Got {len(result)} characters")
                successful += 1
                
                # Check if this was a previously failing query
                if query in ["show me network code", "find socket functions", "show me functions"]:
                    improved += 1
                    print(f"   🎉 IMPROVED: Previously failing query now works!")
                
                # Check for relevance
                query_words = query.lower().split()
                if any(word in result.lower() for word in query_words):
                    print(f"   ✅ Contains relevant content")
                else:
                    print(f"   ⚠️ Content might be generic")
            else:
                print(f"   ⚠️ SHORT: {len(result)} characters")
                failed += 1
                
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
            failed += 1
    
    print(f"\n" + "=" * 60)
    print(f"📊 LIVE IMPROVEMENT RESULTS")
    print(f"=" * 60)
    
    total_queries = successful + failed
    success_rate = successful/total_queries*100 if total_queries > 0 else 0
    
    print(f"📊 SUCCESS RATE: {success_rate:.1f}% ({successful}/{total_queries})")
    print(f"🎉 IMPROVED QUERIES: {improved} (previously failing queries that now work)")
    print(f"❌ STILL FAILING: {failed}")
    
    if improved > 0:
        print(f"\n🚀 IMPROVEMENT SUCCESS!")
        print(f"   ✅ Fixed {improved} previously failing queries")
        print(f"   ✅ Enhanced network pattern matching")
        print(f"   ✅ Improved fallback query specificity")
    
    if success_rate > 77.3:
        print(f"\n🎉 NEW RECORD! Exceeded previous 77.3% success rate!")
    elif success_rate >= 75:
        print(f"\n✅ EXCELLENT! Maintained high success rate!")
    else:
        print(f"\n👍 GOOD! Room for further improvement!")
    
    print("🎉 Live improvement test completed!")

if __name__ == "__main__":
    try:
        asyncio.run(test_live_improvements())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
