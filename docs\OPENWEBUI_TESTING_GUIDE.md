# OpenWebUI Code Analysis Tool Testing Guide

## 🎯 Quick Start

### **1. Updated Default Model**
The auto-tester now uses **`llama3:latest`** by default (the model with your tool enabled).

### **2. Run the Auto-Tester**
```bash
python debug-testing/openwebui_auto_tester.py
```

## 🔐 Authentication Setup

✅ **API Key Pre-Configured!** Your OpenWebUI API key is already integrated into the testing scripts.

### **🚀 Ready to Use**
No setup required! Just run:
```bash
python debug-testing/openwebui_auto_tester.py
# API key automatically used
```

### **🔧 Override Options (Optional)**
If you need to use a different API key:

**Option 1: Environment Variable**
```bash
export OPENWEBUI_API_KEY="your-different-key"
python debug-testing/openwebui_auto_tester.py
```

**Option 2: Interactive Override**
```bash
python debug-testing/openwebui_auto_tester.py
# When prompted: "Use a different API key? (y/N):" type 'y'
```

### **⚙️ Advanced Configuration**
For detailed API key setup, endpoint restrictions, and troubleshooting:
**📖 See: [OPENWEBUI_API_CONFIGURATION.md](OPENWEBUI_API_CONFIGURATION.md)**

### **🔍 Automatic Permission Testing**
The auto-tester now checks API permissions and provides specific guidance for configuration issues.

### **Option 2: Browser Testing (No Auth Required)**
```bash
python debug-testing/openwebui_browser_tester.py
```
This opens OpenWebUI in your browser and provides prompts to copy/paste.

### **Option 3: Manual curl Testing**
```bash
./debug-testing/curl_test_commands.sh
```
Shows curl commands you can run manually.

## 🤖 Model Selection

### **Available Models**
The script will ask which model to use:

| Model | Status | Notes |
|-------|--------|-------|
| **llama3:latest** | ✅ **RECOMMENDED** | Has tool enabled by default |
| llama3.1:latest | ⚠️ May need tool enabled | Check OpenWebUI settings |
| llama3:8b | 💡 Alternative | Smaller, faster if available |
| codellama:latest | 🔧 Specialized | For code-focused testing |

### **Enable Tool for Other Models**
If you want to test with a different model:
1. Go to OpenWebUI Settings → Models
2. Find your model
3. Enable the Code Analysis tool for that model
4. Use that model name in the tester

## 📄 Response Output Options

### **Verbose Mode (Default)**
Shows full response content for detailed analysis:
```
📄 Response content:
----------------------------------------
🔧 **Code Analysis System Status**

✅ **Tool Server**: Online
✅ **Code Analysis Service**: Online
✅ **Ollama AI**: Online
----------------------------------------
```

### **Quiet Mode**
Shows only response length for quick testing:
```
✅ Response received (762 chars)
```

**Usage:** When prompted "Show full response content? (Y/n):"
- Press Enter or 'Y' for verbose (recommended for first-time testing)
- Type 'n' for quiet mode (useful for quick verification)

## 📋 Category Selection

### **Quick Tests (5 tests)**
```
Select categories to test: 8
```
Essential functionality verification.

### **Core Features (14 tests)**
```
Select categories to test: 1,2,8
```
System Status + Management + Quick Test.

### **Preview Before Running**
```
Select categories to test: preview 1
```
See all prompts in System Status category.

### **All Categories (36 tests)**
```
Select categories to test: all
```
Comprehensive testing.

## 🧪 Testing Workflow

### **Step 1: Quick Verification**
```bash
python debug-testing/openwebui_auto_tester.py
# API key: Automatically used (no input needed)
# Select: 8 (Quick Test)
# Model: llama3:latest (default)
# Delay: 2 seconds (default)
# Verbose: Y (show full responses)
```

### **Step 2: Check Results**
- Look for 100% success rate
- **Review actual response content** (now shown in verbose mode)
- Check that responses contain tool output (not just LLM responses)
- Verify status commands show formatted health info with emojis
- Confirm codebase management works

### **Step 3: Targeted Testing**
Based on Step 1 results, run specific categories:
```bash
# If status issues:
Select categories to test: 1

# If management issues:
Select categories to test: 2

# If search issues:
Select categories to test: 3,5
```

### **Step 4: Full Testing**
Once core features work:
```bash
Select categories to test: all
```

## 🔍 Expected Results

### **✅ Tool Working Correctly**
- **Status commands** return formatted health reports with emojis
- **List codebases** shows your 5 codebases (utils, z80emu, modbus, etc.)
- **Code searches** return actual code snippets with metadata
- **AI analysis** provides insights about your actual code
- **Management queries** are automatically routed
- **Success rate** >80%

### **❌ Common Issues**

#### **403 Authentication Errors**
```
❌ HTTP 403: {"detail":"Not authenticated"}
```
**Solution:** Set up API key or use browser testing.

#### **Tool Not Responding**
```
✅ Response received but contains generic LLM response
```
**Solution:** 
- Check tool is enabled for llama3:latest
- Verify Code Analysis server is running
- Test with browser interface first

#### **Timeout Errors**
```
❌ Error: HTTPSConnectionPool... timed out
```
**Solution:**
- Increase delay between tests
- Check server performance
- Run fewer tests at once

#### **Empty Responses**
```
ℹ️ Empty result (tool let OpenWebUI handle this query)
```
**Solution:**
- Check if query should trigger tool
- Verify codebase is selected
- Test with management queries first

## 🛠️ Troubleshooting Commands

### **Test Connection**
```bash
curl http://home-ai-server.local:8080/health
```

### **Test Code Analysis Server**
```bash
curl http://home-ai-server:5002/health
```

### **Check Available Models**
```bash
curl http://home-ai-server.local:8080/api/models
```

### **Test Tool Directly**
```bash
python debug-testing/interactive_tool_test.py
```

## 📊 Results Analysis

### **JSON Results File**
Each test run creates a timestamped JSON file:
```
openwebui_test_results_20250627_143022.json
```

### **Key Metrics to Check**
- **Success Rate**: Should be >80%
- **Response Times**: Should be <30s per test
- **Tool Activation**: Look for formatted responses vs plain text
- **Error Patterns**: Identify systematic issues

### **Response Quality Indicators**
- ✅ Emojis and formatting in status responses
- ✅ Actual codebase names in listings
- ✅ Real code snippets in searches
- ✅ Specific insights in AI analysis
- ❌ Generic/fictional responses
- ❌ Plain text without formatting

## 🎯 Success Criteria

Your tool is working correctly if:
1. **Quick Test category** passes 100%
2. **Status commands** return formatted health info
3. **Codebase management** works (list, select, stats)
4. **Code searches** return relevant snippets from your actual code
5. **AI analysis** provides meaningful insights about your codebase
6. **No authentication errors** (with proper setup)
7. **Response times** are reasonable (<30s per test)

Ready to test your tool! 🚀
