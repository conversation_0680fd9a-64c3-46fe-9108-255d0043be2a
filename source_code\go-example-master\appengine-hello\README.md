This code is a starting point for your Google App Engine application in
Go.

To run the application locally you need the install the [Go Cloud
SDK](https://cloud.google.com/appengine/docs/go/#Go_tools) and execute the next
command from the directory containing this file:

    $ goapp serve app.yaml

To deploy the application you have to first create an App Engine project
and use it as the application file in all the yaml files.
