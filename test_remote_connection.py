#!/usr/bin/env python3
"""
Test remote connection to home-ai-server before deployment
"""

import subprocess
import sys
from pathlib import Path

def test_ssh_connection(remote_host="home-ai-server", remote_user="fvaneijk"):
    """Test SSH connection to remote server"""
    print(f"🔗 Testing SSH connection to {remote_user}@{remote_host}...")
    
    try:
        result = subprocess.run([
            "ssh", f"{remote_user}@{remote_host}", "echo 'SSH connection successful'"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ SSH connection successful")
            return True
        else:
            print(f"❌ SSH connection failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ SSH connection timed out")
        return False
    except Exception as e:
        print(f"❌ SSH connection error: {e}")
        return False

def test_docker_access(remote_host="home-ai-server", remote_user="fvaneijk"):
    """Test Docker access on remote server"""
    print(f"🐳 Testing Docker access on {remote_host}...")
    
    try:
        result = subprocess.run([
            "ssh", f"{remote_user}@{remote_host}", "docker --version"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✅ Docker available: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Docker not accessible: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Docker test error: {e}")
        return False

def test_docker_compose_access(remote_host="home-ai-server", remote_user="fvaneijk"):
    """Test Docker Compose access on remote server"""
    print(f"🐳 Testing Docker Compose access on {remote_host}...")
    
    try:
        result = subprocess.run([
            "ssh", f"{remote_user}@{remote_host}", "docker-compose --version"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✅ Docker Compose available: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Docker Compose not accessible: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Docker Compose test error: {e}")
        return False

def test_remote_directory(remote_host="home-ai-server", remote_user="fvaneijk",
                         remote_path="/home/<USER>/home-ai-system/code_analyzer_server"):
    """Test if remote directory exists and is accessible"""
    print(f"📁 Testing remote directory: {remote_path}...")
    
    try:
        # Test if directory exists
        result = subprocess.run([
            "ssh", f"{remote_user}@{remote_host}", f"ls -la {remote_path}"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Remote directory accessible")
            return True
        else:
            print(f"⚠️ Remote directory not found, will be created during deployment")
            return True  # This is OK, we'll create it
            
    except Exception as e:
        print(f"❌ Remote directory test error: {e}")
        return False

def main():
    """Run all connection tests"""
    print("🚀 Testing Remote Connection for Framework Deployment")
    print("=" * 60)
    
    remote_host = "home-ai-server"
    remote_user = "fvaneijk"
    remote_path = "/home/<USER>/home-ai-system/code_analyzer_server"
    
    tests = [
        ("SSH Connection", lambda: test_ssh_connection(remote_host, remote_user)),
        ("Docker Access", lambda: test_docker_access(remote_host, remote_user)),
        ("Docker Compose Access", lambda: test_docker_compose_access(remote_host, remote_user)),
        ("Remote Directory", lambda: test_remote_directory(remote_host, remote_user, remote_path))
    ]
    
    all_passed = True
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}...")
        if not test_func():
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 All connection tests passed! Ready for deployment.")
        print(f"✅ Run: python deploy_enhanced_framework.py")
        print(f"✅ Or: python deploy_enhanced_framework.py --remote-host {remote_host} --remote-user {remote_user}")
        sys.exit(0)
    else:
        print("❌ Some connection tests failed. Please fix issues before deployment.")
        print("\n💡 Common fixes:")
        print("   - Ensure SSH key authentication is set up")
        print("   - Verify Docker is running on remote server")
        print("   - Check user permissions for Docker")
        sys.exit(1)

if __name__ == "__main__":
    main()
