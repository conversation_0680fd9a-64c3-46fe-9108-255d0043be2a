#!/usr/bin/env python3
"""
Test that responses are clean and don't expose internal tool details
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_clean_responses():
    """Test that responses don't expose internal tool details"""
    print("🧪 Testing Clean Response Format")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test queries that previously showed internal details
    test_queries = [
        ("select codebase utils", "Codebase selection"),
        ("show details about the utils code base", "Codebase details"),
        ("what are some of the major data structures?", "Data structures query"),
        ("find memory allocation functions", "Code search"),
    ]
    
    issues_found = []
    
    for query, description in test_queries:
        print(f"\n🔍 Testing: {description}")
        print(f"Query: '{query}'")
        print("-" * 50)
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": query}],
                    "tool_ids": ["code_analyzer_tool"],
                    "stream": False,
                    "max_tokens": 1000
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                print(f"✅ Response received ({len(content)} chars)")
                
                # Check for internal tool details that shouldn't be exposed
                internal_indicators = [
                    "code_analyzer_tools tool",
                    "determine_optimal_context_format function",
                    "get_code_context tool",
                    "_analyze_query_for_context",
                    "_format_context_for_openwebui",
                    "required positional argument missing",
                    "Tool `code_analyzer_tools/",
                    "missing 1 required positional argument",
                    "DEBUG:",
                    "<!-- DEBUG:",
                ]
                
                found_issues = [ind for ind in internal_indicators if ind in content]
                
                if found_issues:
                    print(f"❌ INTERNAL DETAILS EXPOSED: {found_issues}")
                    issues_found.extend(found_issues)
                    
                    # Show the problematic parts
                    lines = content.split('\n')
                    problem_lines = [line for line in lines if any(issue in line for issue in found_issues)]
                    for line in problem_lines[:3]:  # Show first 3 problem lines
                        print(f"   Problem: {line.strip()}")
                else:
                    print("✅ Clean response - no internal details exposed")
                
                # Show response preview
                preview = content[:200] + "..." if len(content) > 200 else content
                print(f"Preview: {preview}")
                
            else:
                print(f"❌ HTTP {response.status_code}: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return issues_found

def test_error_handling():
    """Test that error messages are clean"""
    print(f"\n🔧 Testing Error Message Cleanliness")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test queries that might trigger errors
    error_test_queries = [
        "get_code_context without parameters",
        "_determine_optimal_context_format test",
        "call internal function directly",
    ]
    
    clean_errors = True
    
    for query in error_test_queries:
        print(f"\n🧪 Testing error for: '{query}'")
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": query}],
                    "tool_ids": ["code_analyzer_tool"],
                    "stream": False,
                    "max_tokens": 500
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                # Check for clean error messages
                if any(term in content for term in ["DEBUG:", "Payload:", "URL:", "Exception:", "Traceback"]):
                    print("❌ Error message contains internal details")
                    clean_errors = False
                else:
                    print("✅ Clean error message")
                    
        except Exception as e:
            print(f"⚠️ Test error: {e}")
    
    return clean_errors

def main():
    """Main test function"""
    print("🧪 Clean Response Testing")
    print("=" * 70)
    print("Testing that tool responses don't expose internal implementation details")
    
    # Test clean responses
    issues = test_clean_responses()
    
    # Test error handling
    clean_errors = test_error_handling()
    
    # Summary
    print(f"\n📊 SUMMARY")
    print("=" * 50)
    
    if not issues and clean_errors:
        print("🎉 EXCELLENT: All responses are clean!")
        print("✅ No internal tool details exposed")
        print("✅ Error messages are user-friendly")
        print("✅ Tool properly hides implementation details")
    elif not issues:
        print("👍 GOOD: Main responses are clean")
        print("⚠️ Some error messages could be improved")
    else:
        print("⚠️ ISSUES FOUND: Internal details being exposed")
        print(f"❌ Found {len(set(issues))} types of internal details:")
        for issue in set(issues):
            print(f"   • {issue}")
        
        print(f"\n🔧 Action needed:")
        print("1. Update tool in OpenWebUI with the fixed version")
        print("2. Restart OpenWebUI to ensure changes take effect")
        print("3. Re-test to verify fixes")
    
    print(f"\n🎯 Expected clean responses should:")
    print("• Show only user-relevant information")
    print("• Hide internal function names and errors")
    print("• Provide helpful context without technical details")
    print("• Focus on code analysis results, not tool mechanics")

if __name__ == "__main__":
    main()
