#!/usr/bin/env python3
"""
Quick test script to verify the tool fix works
"""

import requests
import json
import time

# Configuration
OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_tool_fix():
    """Test the specific issue that was causing the 'NoneType' error"""
    
    session = requests.Session()
    session.headers.update({
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    })
    
    # Test the specific query that was failing
    test_queries = [
        "How does this code work?",
        "select codebase utils",
        "find memory allocation",
        "get stats for utils"
    ]
    
    print("🧪 Testing Tool Fix")
    print("=" * 50)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🔍 Test {i}: {query}")
        
        payload = {
            "model": "llama3:latest",
            "messages": [
                {
                    "role": "user",
                    "content": query
                }
            ],
            "tool_ids": ["code_analyzer_tool"],
            "stream": False,
            "temperature": 0.7,
            "max_tokens": 1000
        }
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                # Check if we're getting the old error message
                if "'NoneType' object has no attribute 'strip'" in content:
                    print("❌ STILL GETTING THE BUG - Tool not fixed")
                elif "No codebase selected" in content and "select_codebase()" in content:
                    print("❌ STILL GETTING GENERIC ERROR - Tool not working")
                else:
                    print("✅ Response looks good")
                    if len(content) > 200:
                        print(f"📄 Response preview: {content[:200]}...")
                    else:
                        print(f"📄 Response: {content}")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Request failed: {e}")
        
        # Small delay between tests
        time.sleep(2)
    
    print("\n" + "=" * 50)
    print("🏁 Test completed")

if __name__ == "__main__":
    test_tool_fix()
