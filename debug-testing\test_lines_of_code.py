#!/usr/bin/env python3
"""
Test Code Metrics Functionality
Comprehensive testing of the code metrics analyzer and API endpoints
"""

import requests
import json
import sys
import os
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from code_metrics_analyzer import CodeMetricsAnalyzer

# Configuration
RAG_SERVER_URL = "http://home-ai-server.local:5002"
OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_metrics_analyzer_directly():
    """Test the metrics analyzer utility directly"""
    print("🧪 Testing Code Metrics Analyzer Directly")
    print("=" * 50)

    try:
        # Initialize analyzer
        analyzer = CodeMetricsAnalyzer("./source_code")

        # Test with utils codebase
        print("📊 Analyzing utils codebase...")
        metrics = analyzer.analyze_codebase("utils")

        # Display results
        print("\n📈 Results:")
        print(f"   • Total files: {metrics.total_files}")
        print(f"   • Total lines: {metrics.total_lines:,}")
        print(f"   • Code lines: {metrics.total_code_lines:,}")
        print(f"   • Quality score: {metrics.overall_quality_score:.1f}/100")
        print(f"   • Complexity risk: {metrics.complexity_risk}")
        print(f"   • Technical debt: {metrics.technical_debt_hours:.1f} hours")
        print(f"   • Languages: {list(metrics.languages.keys())}")

        # Test formatting
        print("\n📄 Formatted Output:")
        formatted = analyzer.format_metrics_for_display(metrics)
        print(formatted[:500] + "..." if len(formatted) > 500 else formatted)

        # Test JSON summary
        print("\n📋 JSON Summary:")
        summary = analyzer.get_metrics_summary(metrics)
        print(f"   • Codebase: {summary['codebase_name']}")
        print(f"   • Overall quality: {summary['quality_scores']['overall']:.1f}/100")
        print(f"   • Total files: {summary['statistics']['total_files']}")
        print(f"   • Total lines: {summary['statistics']['total_lines']:,}")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_rag_server_metrics_endpoint():
    """Test the RAG server metrics endpoints"""
    print("\n🌐 Testing RAG Server Metrics Endpoints")
    print("=" * 50)

    try:
        # Test metrics endpoint
        print("📡 Testing /tools/get_codebase_metrics...")
        response = requests.post(
            f"{RAG_SERVER_URL}/tools/get_codebase_metrics",
            json={"codebase_name": "utils"},
            timeout=60
        )

        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            result = data.get("result", "No result")
            print(f"✅ Metrics result ({len(result)} chars):")
            print(result[:300] + "..." if len(result) > 300 else result)
        else:
            print(f"❌ Error: {response.text}")
            return False

        # Test details endpoint
        print("\n📡 Testing /tools/get_codebase_details...")
        response = requests.post(
            f"{RAG_SERVER_URL}/tools/get_codebase_details",
            json={"codebase_name": "utils"},
            timeout=60
        )

        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Details result:")
            print(f"   • Codebase: {data.get('codebase_name', 'unknown')}")
            print(f"   • Overall quality: {data.get('quality_scores', {}).get('overall', 0):.1f}/100")
            print(f"   • Total files: {data.get('statistics', {}).get('total_files', 0)}")
            print(f"   • Total lines: {data.get('statistics', {}).get('total_lines', 0):,}")
            print(f"   • Languages: {list(data.get('languages', {}).keys())}")
        else:
            print(f"❌ Error: {response.text}")
            return False

        return True

    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_openwebui_metrics_integration():
    """Test metrics functionality through OpenWebUI"""
    print("\n🎯 Testing OpenWebUI Metrics Integration")
    print("=" * 50)

    try:
        headers = {
            'Authorization': f'Bearer {API_KEY}',
            'Content-Type': 'application/json'
        }

        # Test queries that should trigger metrics functionality
        test_queries = [
            "get metrics for utils",
            "show code quality analysis for utils",
            "analyze utils codebase quality",
            "utils code metrics and complexity",
            "get codebase details for utils"
        ]

        for query in test_queries:
            print(f"\n🧪 Testing query: '{query}'")

            payload = {
                'model': 'llama3:latest',
                'messages': [{'role': 'user', 'content': query}],
                'tool_ids': ['codebase_analyzer'],
                'stream': False
            }

            response = requests.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json=payload,
                headers=headers,
                timeout=90
            )

            if response.status_code == 200:
                data = response.json()
                content = data.get('choices', [{}])[0].get('message', {}).get('content', '')

                # Check if metrics data is present
                metrics_indicators = [
                    'quality score', 'complexity', 'maintainability',
                    'technical debt', 'risk assessment', 'metrics'
                ]

                has_metrics_data = any(indicator.lower() in content.lower() for indicator in metrics_indicators)

                if has_metrics_data:
                    print(f"✅ Metrics data detected ({len(content)} chars)")
                    # Show a preview
                    lines = content.split('\n')[:5]
                    print(f"   Preview: {' '.join(lines)[:100]}...")
                else:
                    print(f"❌ No metrics data detected")
                    print(f"   Response: {content[:100]}...")
            else:
                print(f"❌ HTTP Error: {response.status_code}")

        return True

    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_metrics_accuracy():
    """Test metrics calculation accuracy with known files"""
    print("\n🎯 Testing Metrics Accuracy")
    print("=" * 50)

    try:
        analyzer = CodeMetricsAnalyzer("./source_code")

        # Find a specific file to test
        utils_path = Path("./source_code/utils")
        if not utils_path.exists():
            print("❌ Utils codebase not found for accuracy testing")
            return False

        # Get all source files
        source_files = []
        for ext in ['.c', '.cpp', '.py', '.cs', '.h', '.hpp']:
            source_files.extend(utils_path.glob(f"**/*{ext}"))

        if not source_files:
            print("❌ No source files found for accuracy testing")
            return False

        # Test a few files manually
        print(f"📁 Found {len(source_files)} source files")

        for i, filepath in enumerate(source_files[:3]):  # Test first 3 files
            print(f"\n📄 Testing file {i+1}: {filepath.name}")

            # Analyze with our tool
            try:
                file_metrics = analyzer.analyze_file(filepath)

                print(f"   Lines: {file_metrics.total_lines} total, {file_metrics.code_lines} code")
                print(f"   Complexity: {file_metrics.cyclomatic_complexity} cyclomatic, {file_metrics.cognitive_complexity} cognitive")
                print(f"   Quality: {file_metrics.maintainability_index:.1f} maintainability")
                print(f"   Structure: {file_metrics.functions_count} functions, {file_metrics.classes_count} classes")
                print(f"   Code smells: {len(file_metrics.code_smells)} detected")

                if file_metrics.code_smells:
                    print(f"   Smells: {', '.join(file_metrics.code_smells)}")

                print(f"   ✅ Analysis completed successfully")

            except Exception as e:
                print(f"   ❌ Error analyzing {filepath}: {e}")

        return True

    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Run all code metrics tests"""
    print("🧪 Code Metrics Testing Suite")
    print("=" * 60)

    results = {}

    # Test 1: Direct metrics analyzer
    results['direct_analyzer'] = test_metrics_analyzer_directly()

    # Test 2: RAG server endpoints
    results['rag_server'] = test_rag_server_metrics_endpoint()

    # Test 3: OpenWebUI integration
    results['openwebui'] = test_openwebui_metrics_integration()

    # Test 4: Accuracy testing
    results['accuracy'] = test_metrics_accuracy()

    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)

    passed = sum(results.values())
    total = len(results)

    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name.replace('_', ' ').title()}")

    print(f"\n🎯 Overall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All code metrics tests passed!")
        return 0
    else:
        print("⚠️ Some code metrics tests failed")
        return 1

if __name__ == "__main__":
    exit(main())
