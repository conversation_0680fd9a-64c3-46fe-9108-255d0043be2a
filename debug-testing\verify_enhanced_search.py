#!/usr/bin/env python3
"""
Simple verification script for enhanced search fix
Run this after restarting the Code Analysis server
"""

import requests
import json

CODE_ANALYZER_SERVER_URL = "http://home-ai-server.local:5002"

def test_enhanced_search_fix():
    """Test that enhanced search works with multiple filters"""
    print("🔍 Testing Enhanced Search Fix")
    print("=" * 40)
    
    # Test cases with different filter combinations
    test_cases = [
        {
            "name": "Single filter (type)",
            "payload": {
                "query": "memory allocation",
                "codebase_name": "utils",
                "n_results": 2,
                "filter_type": "function"
            }
        },
        {
            "name": "Single filter (language)", 
            "payload": {
                "query": "memory allocation",
                "codebase_name": "utils",
                "n_results": 2,
                "filter_language": "c"
            }
        },
        {
            "name": "Multiple filters (type + language)",
            "payload": {
                "query": "memory allocation", 
                "codebase_name": "utils",
                "n_results": 2,
                "filter_type": "function",
                "filter_language": "c"
            }
        },
        {
            "name": "Triple filters (type + language + file)",
            "payload": {
                "query": "memory",
                "codebase_name": "utils", 
                "n_results": 2,
                "filter_type": "function",
                "filter_language": "c",
                "filter_file": "tmw"
            }
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n📋 {test_case['name']}")
        
        try:
            response = requests.post(
                f"{CODE_ANALYZER_SERVER_URL}/tools/enhanced_search",
                json=test_case['payload'],
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                result_text = result.get('result', '')
                
                # Check for the old error
                if "Expected where to have exactly one operator" in result_text:
                    print("❌ OLD ERROR STILL EXISTS")
                    results.append(False)
                elif "Error in enhanced search" in result_text:
                    print("❌ DIFFERENT ERROR")
                    print(f"   Error: {result_text[:100]}...")
                    results.append(False)
                else:
                    print("✅ SUCCESS")
                    # Check if we got results
                    if "Found" in result_text and "results" in result_text:
                        print("   Got search results")
                    else:
                        print("   No results but no error")
                    results.append(True)
            else:
                print(f"❌ HTTP {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Request failed: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 SUMMARY")
    print("=" * 40)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Enhanced search fix is working correctly")
        print("💡 Multiple filters can now be used together")
    elif passed > 0:
        print(f"⚠️ PARTIAL SUCCESS: {passed}/{total} tests passed")
        print("💡 Some filter combinations work, others may need investigation")
    else:
        print("❌ ALL TESTS FAILED")
        print("💡 Enhanced search fix may not be applied yet")
        print("🔧 Try restarting the Code Analysis server container")
    
    return passed == total

if __name__ == "__main__":
    test_enhanced_search_fix()
