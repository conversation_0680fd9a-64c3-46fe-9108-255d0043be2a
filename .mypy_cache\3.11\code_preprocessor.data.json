{".class": "MypyFile", "_fullname": "code_preprocessor", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Chunk": {".class": "SymbolTableNode", "cross_ref": "chunk_system.Chunk", "kind": "Gdef"}, "CodePreprocessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "code_preprocessor.CodePreprocessor", "line": 369, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "code_preprocessor.MultiLanguageCodeProcessor"}}, "CppCodeProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "code_preprocessor.CppCodeProcessor", "line": 368, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "code_preprocessor.MultiLanguageCodeProcessor"}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "IntegratedCodeAnalysisSystem": {".class": "SymbolTableNode", "cross_ref": "framework_integration.IntegratedCodeAnalysisSystem", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MultiLanguageCodeProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "code_preprocessor.MultiLanguageCodeProcessor", "name": "MultiLanguageCodeProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "code_preprocessor", "mro": ["code_preprocessor.MultiLanguageCodeProcessor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "repo_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.__init__", "name": "__init__", "type": null}}, "_convert_framework_results_to_chunks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "framework_result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._convert_framework_results_to_chunks", "name": "_convert_framework_results_to_chunks", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "framework_result"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_convert_framework_results_to_chunks of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_basic_chunk_from_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._create_basic_chunk_from_context", "name": "_create_basic_chunk_from_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_basic_chunk_from_context of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_estimate_complexity_from_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._estimate_complexity_from_content", "name": "_estimate_complexity_from_content", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "content"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_estimate_complexity_from_content of MultiLanguageCodeProcessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fallback_processing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "exclude_dirs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._fallback_processing", "name": "_fallback_processing", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "exclude_dirs"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fallback_processing of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_framework_chunk_to_old_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "framework_chunk"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._framework_chunk_to_old_format", "name": "_framework_chunk_to_old_format", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "framework_chunk"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", "chunk_system.Chunk"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_framework_chunk_to_old_format of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_language_statistics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chunks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._get_language_statistics", "name": "_get_language_statistics", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunks"], "arg_types": ["code_preprocessor.MultiLanguageCodeProcessor", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_language_statistics of MultiLanguageCodeProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_init_semantic_patterns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._init_semantic_patterns", "name": "_init_semantic_patterns", "type": null}}, "_process_repository_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "exclude_dirs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor._process_repository_async", "name": "_process_repository_async", "type": null}}, "chunk_registry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.chunk_registry", "name": "chunk_registry", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "complexity_thresholds": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.complexity_thresholds", "name": "complexity_thresholds", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "extract_functions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_code", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.extract_functions", "name": "extract_functions", "type": null}}, "framework": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.framework", "name": "framework", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_parser_for_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.get_parser_for_file", "name": "get_parser_for_file", "type": null}}, "integrated_system": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.integrated_system", "name": "integrated_system", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "process_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.process_file", "name": "process_file", "type": null}}, "process_repository": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "exclude_dirs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.process_repository", "name": "process_repository", "type": null}}, "repo_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.repo_path", "name": "repo_path", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "semantic_patterns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "code_preprocessor.MultiLanguageCodeProcessor.semantic_patterns", "name": "semantic_patterns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "code_preprocessor.MultiLanguageCodeProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "code_preprocessor.MultiLanguageCodeProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "code_preprocessor.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "code_preprocessor.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "code_preprocessor.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "code_preprocessor.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "code_preprocessor.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "code_preprocessor.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "create_default_chunk_registry": {".class": "SymbolTableNode", "cross_ref": "chunk_system.create_default_chunk_registry", "kind": "Gdef"}, "create_language_registry": {".class": "SymbolTableNode", "cross_ref": "language_registry.create_language_registry", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "code_preprocessor.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\code_preprocessor.py"}