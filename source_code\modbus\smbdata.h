/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2023 */
/*****************************************************************************/
/*                                                                           */
/* This file is the property of:                                             */
/*                                                                           */
/*                       Triangle MicroWorks, Inc.                           */
/*                      Raleigh, North Carolina USA                          */
/*                       www.TriangleMicroWorks.com                          */
/*                          (919) 870-6615                                   */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*                                                                           */
/*****************************************************************************/

/* file: smbdata.h
 * description: This file defines the interface between the TMW MB Server
 *  SCL and the target database. The user should modify the corresponding
 *  implementations in smbdata.c as required to access the target database.
 *
 * Routines are provided below to access data in a MB Server device. A set 
 *  of routines are provided for each data type based on the requirements of
 *  that data type. The following generic descriptions apply to corresponding
 *  data type specific subroutines.
 */
#ifndef SMBDATA_DEFINED
#define SMBDATA_DEFINED

#include "tmwscl/utils/tmwdefs.h"
#include "tmwscl/utils/tmwdtime.h"

/* Set support for each Function Code  */
#define SMBDATA_SUPPORT_FC_R_COILS          TMWDEFS_TRUE
#define SMBDATA_SUPPORT_FC_R_D_INPUTS       TMWDEFS_TRUE
#define SMBDATA_SUPPORT_FC_R_H_REGISTERS    TMWDEFS_TRUE
#define SMBDATA_SUPPORT_FC_R_I_REGISTERS    TMWDEFS_TRUE
#define SMBDATA_SUPPORT_FC_W_COIL           TMWDEFS_TRUE
#define SMBDATA_SUPPORT_FC_W_REGISTER       TMWDEFS_TRUE
#define SMBDATA_SUPPORT_FC_R_EXCEPTION      TMWDEFS_TRUE
#define SMBDATA_SUPPORT_FC_DIAGNOSTICS      TMWDEFS_TRUE
#define SMBDATA_SUPPORT_FC_W_MCOILS         TMWDEFS_TRUE
#define SMBDATA_SUPPORT_FC_W_MHREGISTERS    TMWDEFS_TRUE
#define SMBDATA_SUPPORT_FC_MW_REGISTER      TMWDEFS_TRUE
#define SMBDATA_SUPPORT_FC_RW_MREGISTERS    TMWDEFS_TRUE
#define SMBDATA_SUPPORT_FC_READ_DEV_ID      TMWDEFS_TRUE

#define SMBDATA_SUPPORT_ANY              \
  ( SMBDATA_SUPPORT_FC_R_COILS        == TMWDEFS_TRUE   \
  || SMBDATA_SUPPORT_FC_R_D_INPUTS    == TMWDEFS_TRUE   \
  || SMBDATA_SUPPORT_FC_R_H_REGISTERS == TMWDEFS_TRUE   \
  || SMBDATA_SUPPORT_FC_R_I_REGISTERS == TMWDEFS_TRUE   \
  || SMBDATA_SUPPORT_FC_W_COIL        == TMWDEFS_TRUE   \
  || SMBDATA_SUPPORT_FC_W_REGISTER    == TMWDEFS_TRUE   \
  || SMBDATA_SUPPORT_FC_R_EXCEPTION   == TMWDEFS_TRUE   \
  || SMBDATA_SUPPORT_FC_DIAGNOSTICS   == TMWDEFS_TRUE   \
  || SMBDATA_SUPPORT_FC_W_MCOILS      == TMWDEFS_TRUE   \
  || SMBDATA_SUPPORT_FC_W_MHREGISTERS == TMWDEFS_TRUE   \
  || SMBDATA_SUPPORT_FC_MW_REGISTER   == TMWDEFS_TRUE   \
  || SMBDATA_SUPPORT_FC_RW_MREGISTERS == TMWDEFS_TRUE   \
  || SMBDATA_SUPPORT_FC_DEVICE_IDENT  == TMWDEFS_TRUE )

/* Do this down here so the support macros as defined in smbsesn.h */
#include "tmwscl/modbus/smbsesn.h"

/* Define types for functions below */
typedef TMWTYPES_USHORT (*SMBDATA_QUANTITY_FUNC)(void *);
typedef void * (*SMBDATA_GET_POINT_FUNC)(void *, TMWTYPES_USHORT);

#ifdef __cplusplus
extern "C" {
#endif

  /* function: smbdata_init
   * purpose: Initialize MB Server database for specified session
   * arguments:  
   *  pSession - pointer to session on which to create database
   *  pUserHandle - user specified database handle passed to smbsesn_openSession
   * returns:
   *  pointer to database handle  for future database calls
   */
  void * TMWDEFS_GLOBAL smbdata_init(
    TMWSESN *pSession, 
    void *pUserHandle);

  /* function: smbdata_close
   * purpose: Close MB Server database
   * arguments: 
   *  pHandle - database handle returned from mmbdata_init
   * returns:
   *  void
   */
  void TMWDEFS_GLOBAL smbdata_close(
    void *pHandle);

  /* Discrete Inputs ________________________________________________________ */

  /* function: smbdata_DiscreteInputsValidateRange
   * purpose: Validate that a contiguous range of discrete inputs exists in the 
   *  specified database.
   * arguments:
   *  pHandle - handle to database returned from smbdata_init
   *  startAddr - 0 based addrss of first discrete input
   *  quantity - number of discrete inputs
   * returns:
   *  TMWDEFS_TRUE if range exists, otherwise TMWDEFS_FALSE
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_DiscreteInputsValidateRange(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT quantity);

  /* function: smbdata_getDiscreteInputs
   * purpose: Get the specified discrete inputs
   * arguments:
   *  pHandle - handle to database returned from smbdata_init
   *  startAddr - 0 based address of first discrete input to read
   *  quantity - number of discrete inputs to read
   *  pValueArray - pointer to array of values to hold discrete input 
   *   values, one bit per discrete input. This array will be large enough
   *   to hold quantity bits.
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_getDiscreteInputs(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT quantity,
    TMWTYPES_UCHAR *pValueArray);


  /* Coils __________________________________________________________________ */

  /* function: smbdata_CoilsValidateRange
   * purpose: Validate the range of coils in the 
   *  specified database.
   * arguments:
   *  pHandle - handle to database returned from smbdata_init
   *  startAddr - 0 based addrss of first coil
   *  quantity - number of coils
   * returns:
   *  TMWDEFS_TRUE if range exists, otherwise TMWDEFS_FALSE
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_CoilsValidateRange(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT quantity);

  /* function: smbdata_getCoils
   * purpose: Get the specified coils.
   * arguments:
   *  pHandle - handle to database returned from smbdata_init
   *  startAddr - 0 based address of first coil to read
   *  quantity - number of coils to read
   *  pValueArray - pointer to array of values to hold coil values 
   *   values, one bit per coil. This array will be large enough
   *   to hold quantity bits.
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_getCoils(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT quantity,
    TMWTYPES_UCHAR *pValueArray);

  /* function: smbdata_storeCoils
   * purpose: Store the specified coils
   * arguments:
   *  pHandle - handle to database returned from smbdata_init
   *  startAddr - 0 based address of first coil to write
   *  quantity - number of coils to write
   *  pValueArray - pointer to array of coil values to store,
   *   one bit per coil. This array will be large enough
   *   to hold quantity bits.
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_storeCoils(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT quantity,
    TMWTYPES_UCHAR *pValueArray);


  /* Input Registers ________________________________________________________ */
 
  /* function: smbdata_InputRegistersValidateRange
   * purpose: Validate the range of input registers in the 
   *  specified database.
   * arguments:
   *  pHandle - handle to database returned from smbdata_init
   *  startAddr - 0 based addrss of first input register
   *  quantity - number of input registers 
   * returns:
   *  TMWDEFS_TRUE if range exists, otherwise TMWDEFS_FALSE
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_InputRegistersValidateRange(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT quantity);

  /* function: smbdata_getInputRegisters
   * purpose: Get the specified input registers.
   * arguments:
   *  pHandle - handle to database returned from smbdata_init
   *  startAddr - 0 based address of first input register to read
   *  quantity - number of 16 bit registers to read
   *  pValueArray - pointer to array of values to hold input register
   *   values, 16 bits each.
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_getInputRegisters(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT quantity,
    TMWTYPES_USHORT *pValueArray);


  /* Holding Registers ______________________________________________________ */

  /* function: smbdata_HoldingRegistersValidateRange
   * purpose: Validate the range of holding registers in the 
   *  specified database.
   * arguments:
   *  pHandle - handle to database returned from smbdata_init
   *  startAddr - 0 based addrss of holding registers
   *  quantity - number of holding registers
   * returns:
   *  TMWDEFS_TRUE if range exists, otherwise TMWDEFS_FALSE
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_HoldingRegistersValidateRange(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT quantity);

  /* function: smbdata_getHoldingRegisters
   * purpose: Get the specified holding registers.
   * arguments:
   *  pHandle - handle to database returned from smbdata_init
   *  startAddr - 0 based address of first holding register to read
   *  quantity - number of 16 bit registers to read
   *  pValueArray - pointer to array of values to hold holding register
   *   values, 16 bits each. 
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_getHoldingRegisters(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT quantity,
    TMWTYPES_USHORT *pValueArray);

  /* function: smbdata_storeHoldingRegisters
   * purpose: Store the specified holding registers
   * arguments:
   *  pHandle - handle to database returned from smbdata_init
   *  startAddr - 0 based address of first holding register to write
   *  quantity - number of 16 bit registers to write
   *  pValueArray - pointer to array of values, 16 bits each.
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_storeHoldingRegisters(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT quantity,
    TMWTYPES_USHORT *pValueArray);

  /* function: smbdata_readExceptionStatus
   * purpose: Read the exception status 
   * arguments:
   *  pHandle - handle to database returned from smbdata_init
   *  pStatus - pointer to uchar to hold exception status bits
   *   This should contain the contents of eight Exception Status outputs
   *   packed into 1 byte with 1 bit per output. The status of the lowest
   *   output is contained in the least significant bit
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_readExceptionStatus(  
    void *pHandle,
    TMWTYPES_UCHAR *pStatus);

  /* function: smbdata_diagSubFunctionSupported
   * purpose: Is this diagnostics request sub-function supported?
   * arguments:
   *  pHandle - handle to database returned from smbdata_init 
   *  subFunction - subfunction code received.
   * returns:
   *  TMWDEFS_TRUE if supported, else TMWDEFS_FALSE
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_diagSubFunctionSupported(  
    void *pHandle,
    TMWTYPES_USHORT subFunction);

  /* function: smbdata_diagRestart
   * purpose:  Initialize and restart serial line port. Device should
   *  attempt a restart and execute its power-up confidence tests.
   * arguments:
   *  pHandle - handle to database returned from smbdata_init 
   *  requestData - request data received.
   *   MBDEFS_DIAG_RESTART_CLEAR   port's communications event log should be cleared.
   *   MBDEFS_DIAG_RESTART_NOCLEAR port's communications event log should not be cleared.
   * returns:
   *  void
   */
  void TMWDEFS_GLOBAL smbdata_diagRestart(  
    void *pHandle,
    TMWTYPES_USHORT requestData);

  /* function: smbdata_diagListenOnly
   * purpose:  Force to Listen Only Mode for Modbus communications
   *  The ready watchdog timer should be allowed to expire, locking the
   *  controls off.
   * arguments:
   *  pHandle - handle to database returned from smbdata_init 
   * returns:
   *  void
   */
  void TMWDEFS_GLOBAL smbdata_diagListenOnly(  
    void *pHandle);

  /* function: smbdata_diagClearCount 
   * purpose: Clear specified counter for diagnostic function sub-function
   *  This function will be called for the following sub-functions
   *   10 - MBDEFS_DIAG_CLEAR_BUS_COUNTERS 
   *      clear all counters and the diagnostic register.
   *   20 - MBDEFS_DIAG_CLEAR_OVERRUN_COUNT 
   *      clear overrun counter and flag.
   * arguments:
   *  pHandle - handle to database returned from smbdata_init 
   *  subFunction - subfunction code received.
   * returns:
   *  void
   */
   void TMWDEFS_GLOBAL smbdata_diagClearCount(  
     void *pHandle,
     TMWTYPES_USHORT subFunction);

  /* function: smbdata_diagGetValue 
   * purpose: Provide specified register or counter value for diagnostic 
   *   sub-function specified.
   * arguments:
   *  pHandle - handle to database returned from smbdata_init 
   *  subFunction - subfunction code received.
   *    2 - MBDEFS_DIAG_RETURN_DIAG_REGISTER
   *     The contents of the 16-bit diagnostic register should be returned
   *     The target database must provide this.
   *   11 - MBDEFS_DIAG_GET_BUS_MSG_COUNT 
   *     The quantity of messages detected by the communications system since
   *     the last restart, clear counters operation, or power-up. 
   *     This needs to be determined by the target layer.
   *   12 - MBDEFS_DIAG_GET_BUS_CRC_ERR_COUNT 
   *     The quantity of CRC errors encountered since the last restart, clear 
   *     counters operation, or power-up.
   *     If SCL is performing CRC check, the channel statistics function will be 
   *     called with 
   *       eventType == TMWCHNL_STAT_ERROR, 
   *       errorCode == TMWCHNL_ERROR_LINK_INVALID_CHECKSUM
   *     The target layer should maintain this counter.
   *   13 - MBDEFS_DIAG_GET_BUS_EXC_COUNT 
   *     The quantity of exception responses sent since
   *     the last restart, clear counters operation, or power-up.
   *     For each exception response sent the session statistics function 
   *     will be called with 
   *       eventType == TMWSESN_STAT_EXCEPTION_RESP
   *     The target layer should maintain this counter.
   *   14 - MBDEFS_DIAG_GET_SERVER_MSG_COUNT 
   *     The quantity of messages addressed to this device or broadcast that this
   *     device has processed since the last restart, clear counters operation, 
   *     or power-up.
   *     For each frame received the channel statistics function will be called with 
   *       eventType == TMWCHNL_STAT_FRAME_RECEIVED
   *     The target layer should maintain this counter.
   *   15 - MBDEFS_DIAG_GET_SERVER_NR_COUNT 
   *     The quantity of messages addressed to this device for which no response
   *     (either normal or exception response) since its last restart, clear counters
   *     operation, or power-up.
   *     For each frame received that is not responded to the session statistics function
   *     will be called with
   *       eventType == TMWSESN_STAT_NO_RESPONSE
   *     The target layer should maintain this counter.
   *   16 - MBDEFS_DIAG_GET_SERVER_NAK_COUNT  
   *     The quantity of Negative Acknowledgement exception responses sent since the 
   *     last restart, clear counters operation, or power-up.
   *     The SCL currently does not send Negative Acknowledgement exception responses
   *   17 - MBDEFS_DIAG_GET_SERVER_BUSY_COUNT 
   *     The quantity of Server Device Busy exception responses sent since its last
   *     restart, clear counters operation, or power-up.
   *     The SCL currently does not send Server Device Busy exception responses
   *   18 - MBDEFS_DIAG_GET_BUS_OVERRUN_COUNT 
   *     The quantity of messages addressed to this device that could not be handled
   *     because of a character overrun condition since the last restart, clear counters
   *     operation or power-up. An overrun is caused by data arriving faster than they 
   *     can be stored or by the loss of data due to a hardware malfunction.
   *     This needs to be determined by the target layer.
   *  pValue - pointer to 16 bit value to be filled in
   * returns:
   *  void
   */
  void TMWDEFS_GLOBAL smbdata_diagGetValue(  
    void *pHandle,
    TMWTYPES_USHORT subFunction,
    TMWTYPES_USHORT *pValue);

  /* function: smbdata_diagnostics 
   * purpose: Process diagnostics request and provide response data for
   *    subfunctions not supported by SCL.
   *  This function will not be called for the following sub-functions 
   *   since they are supported by the SCL.
   *   MBDEFS_DIAG_QUERY_DATA                        0
   *   MBDEFS_DIAG_RESTART_COMM                      1
   *   MBDEFS_DIAG_RETURN_DIAG_REGISTER              2
   *   MBDEFS_DIAG_CHANGE_ASCII_DELIM                3
   *   MBDEFS_DIAG_FORCE_LISTEN_ONLY                 4
   *   MBDEFS_DIAG_CLEAR_BUS_COUNTERS               10
   *   MBDEFS_DIAG_GET_BUS_MSG_COUNT                11
   *   MBDEFS_DIAG_GET_BUS_CRC_ERR_COUNT            12 
   *   MBDEFS_DIAG_GET_BUS_EXC_COUNT                13
   *   MBDEFS_DIAG_GET_SERVER_MSG_COUNT             14
   *   MBDEFS_DIAG_GET_SERVER_NR_COUNT              15 
   *   MBDEFS_DIAG_GET_SERVER_NAK_COUNT             16
   *   MBDEFS_DIAG_GET_SERVER_BUSY_COUNT            17
   *   MBDEFS_DIAG_GET_BUS_OVERRUN_COUNT            18   
   *   MBDEFS_DIAG_CLEAR_OVERRUN_COUNT              20
   * arguments:
   *  pHandle - handle to database returned from smbdata_init 
   *  subFunction - subfunction code received.
   *  requestSize - Number of bytes from request pointed to by pRequestData
   *  pRequestData - pointer to bytes from request
   *  pResponseSize - pointer to maximum number of bytes that may be copied into
   *   response. On return *pResponseSize should be changed to represent the number
   *   of bytes that were actually copied into response
   *  pResponseData - pointer to where response data should be copied for sub-functions
   *   not supported in SCL, but supported in this target database.
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_diagnostics(  
    void *pHandle,
    TMWTYPES_USHORT subFunction,
    TMWTYPES_UCHAR  requestSize,
    TMWTYPES_UCHAR *pRequestData,
    TMWTYPES_UCHAR *pResponseSize,
    TMWTYPES_UCHAR *pResponseData);

  /* function: smbdata_getNextDeviceId 
   * purpose: Get the object id for the next device identifier in the database.
   *   This allows object id gaps for the device identifiers that are supported.
   * arguments:
   *  pHandle - handle to database returned from smbdata_init 
   *  objectId - object id for the current device identifier 
   *  pNextObjectId - pointer to where to copy the object id for the next 
   *  device identifier if there is one.
   * returns:
   *  TMWDEFS_TRUE if next device identifier exists, else TMWDEFS_FALSE
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_getNextDeviceId( 
    void *pHandle,
    TMWTYPES_UCHAR objectId,
    TMWTYPES_UCHAR *pNextObjectId);

  /* function: smbdata_readDeviceId 
   * purpose: read the device identifier with this object id
   * arguments:
   *  pHandle - handle to database returned from smbdata_init 
   *  objectId - object id for this device identifier 
   *  pLength - When called this is the maximum length data that can be read
   *            On return this should be the length of the device id that was read
   *  pData - pointer to location to copy device identifier string
   * returns:
   *  TMWDEFS_TRUE if successful,
   *  TMWDEFS_FALSE if this device identifier object does not exist or the data 
   *   will not fit in *pLength
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_readDeviceId( 
    void *pHandle,
    TMWTYPES_UCHAR  objectId,
    TMWTYPES_UCHAR *pLength,
    TMWTYPES_UCHAR *pData);


#ifdef __cplusplus
}
#endif
#endif /* SMBDATA_DEFINED */
