#!/usr/bin/env python3
"""
Test the consistency between plugin and API test script
"""

import requests
import json

CODE_ANALYZER_SERVER_URL = "http://home-ai-server:5002"

def test_list_codebases_consistency():
    """Test both ways of calling list_codebases"""
    print("🔍 Testing list_codebases consistency...")
    
    # Method 1: Test script way (with empty payload)
    try:
        response1 = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/list_codebases",
            json={},
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        result1 = response1.status_code == 200
        print(f"✅ Method 1 (empty payload): {result1}")
    except Exception as e:
        print(f"❌ Method 1 failed: {e}")
        result1 = False
    
    # Method 2: Tool way (no payload, just headers)
    try:
        response2 = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/list_codebases",
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        result2 = response2.status_code == 200
        print(f"✅ Method 2 (no payload): {result2}")
    except Exception as e:
        print(f"❌ Method 2 failed: {e}")
        result2 = False
    
    return result1 and result2

def test_search_endpoints():
    """Test different search endpoints"""
    print("\n🔍 Testing search endpoints...")
    
    # Test /tools/search_code (should work)
    try:
        response1 = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/search_code",
            json={
                "query": "test",
                "codebase_name": "utils",
                "n_results": 1
            },
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        result1 = response1.status_code == 200
        print(f"✅ /tools/search_code: {result1}")
    except Exception as e:
        print(f"❌ /tools/search_code failed: {e}")
        result1 = False
    
    # Test /search (might not exist)
    try:
        response2 = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/search",
            json={
                "query": "test",
                "n_results": 1
            },
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        result2 = response2.status_code == 200
        print(f"✅ /search: {result2}")
    except Exception as e:
        print(f"❌ /search failed: {e}")
        result2 = False
    
    return result1, result2

def test_timeout_differences():
    """Test if different timeouts affect results"""
    print("\n⏱️ Testing timeout differences...")
    
    # Test with test script timeout (60s)
    try:
        response1 = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/ask_about_code",
            json={
                "question": "What functions are available?",
                "codebase_name": "utils"
            },
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        result1 = response1.status_code == 200
        print(f"✅ 60s timeout: {result1}")
    except Exception as e:
        print(f"❌ 60s timeout failed: {e}")
        result1 = False
    
    return result1

def main():
    print("🚀 Tool Consistency Test")
    print("=" * 50)
    
    # Test list_codebases consistency
    list_result = test_list_codebases_consistency()
    
    # Test search endpoints
    search_tools, search_raw = test_search_endpoints()
    
    # Test timeout differences
    timeout_result = test_timeout_differences()
    
    print("\n" + "=" * 50)
    print("📊 CONSISTENCY REPORT:")
    print("=" * 50)
    
    print(f"📚 List codebases: {'✅ Consistent' if list_result else '❌ Inconsistent'}")
    print(f"🔍 Search tools endpoint: {'✅ Working' if search_tools else '❌ Broken'}")
    print(f"🔍 Raw search endpoint: {'✅ Working' if search_raw else '❌ Missing (expected)'}")
    print(f"⏱️ Timeout handling: {'✅ Working' if timeout_result else '❌ Issues'}")
    
    if search_tools and not search_raw:
        print("\n💡 RECOMMENDATION:")
        print("The /search endpoint doesn't exist - plugin should use /tools/search_code")
        print("✅ This has been fixed in the plugin code")
    
    if list_result and search_tools and timeout_result:
        print("\n🎉 OVERALL: Tool and API test script are now consistent!")
    else:
        print("\n⚠️ OVERALL: Some inconsistencies remain")

if __name__ == "__main__":
    main()
