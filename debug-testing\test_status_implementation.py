#!/usr/bin/env python3
"""
Test the new /status endpoint implementation in the tool
"""

import asyncio
import sys
import os

# Add the parent directory to the path so we can import the tool
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the tool
from open_webui_code_analyzer_tool import Tools

async def test_status_functions():
    """Test all the new status-related functions"""
    print("🚀 Testing Status Endpoint Implementation")
    print("=" * 50)
    
    # Initialize the tool
    tools = Tools()
    
    # Test 1: Basic health check (original functionality)
    print("\n🔍 Test 1: Basic Health Check")
    print("-" * 30)
    try:
        result = await tools.check_system_status()
        print("✅ check_system_status() - Basic health check:")
        print(result[:200] + "..." if len(result) > 200 else result)
    except Exception as e:
        print(f"❌ check_system_status() failed: {e}")
    
    # Test 2: Detailed status using parameter
    print("\n🔍 Test 2: Detailed Status (via parameter)")
    print("-" * 30)
    try:
        result = await tools.check_system_status(detailed=True)
        print("✅ check_system_status(detailed=True) - Comprehensive status:")
        print(result[:200] + "..." if len(result) > 200 else result)
    except Exception as e:
        print(f"❌ check_system_status(detailed=True) failed: {e}")
    
    # Test 3: Direct server status call
    print("\n🔍 Test 3: Direct Server Status")
    print("-" * 30)
    try:
        result = await tools.get_server_status()
        print("✅ get_server_status() - Direct /status endpoint:")
        print(result[:200] + "..." if len(result) > 200 else result)
    except Exception as e:
        print(f"❌ get_server_status() failed: {e}")
    
    # Test 4: Convenience wrapper
    print("\n🔍 Test 4: Convenience Wrapper")
    print("-" * 30)
    try:
        result = await tools.get_detailed_status()
        print("✅ get_detailed_status() - Convenience wrapper:")
        print(result[:200] + "..." if len(result) > 200 else result)
    except Exception as e:
        print(f"❌ get_detailed_status() failed: {e}")

async def test_auto_routing():
    """Test the automatic routing for status queries"""
    print("\n\n🤖 Testing Automatic Query Routing")
    print("=" * 50)
    
    tools = Tools()
    
    # Test queries that should trigger status functions
    test_queries = [
        ("status", "Basic status query"),
        ("system status", "System status query"),
        ("detailed status", "Detailed status query"),
        ("comprehensive status", "Comprehensive status query"),
        ("full status", "Full status query"),
        ("show status", "Show status query")
    ]
    
    for query, description in test_queries:
        print(f"\n🔍 Testing: '{query}' ({description})")
        print("-" * 40)
        try:
            # This would normally be called by OpenWebUI
            result = await tools._handle_management_query(query)
            if result:
                print(f"✅ Routed successfully:")
                print(result[:150] + "..." if len(result) > 150 else result)
            else:
                print("❌ No routing match found")
        except Exception as e:
            print(f"❌ Routing failed: {e}")

async def test_endpoint_comparison():
    """Compare /health vs /status endpoint responses"""
    print("\n\n📊 Comparing /health vs /status Endpoints")
    print("=" * 50)
    
    tools = Tools()
    
    print("\n🏥 /health endpoint (basic):")
    print("-" * 30)
    try:
        health_result = await tools.check_system_status(detailed=False)
        print(health_result[:300] + "..." if len(health_result) > 300 else health_result)
    except Exception as e:
        print(f"❌ Health check failed: {e}")
    
    print("\n📊 /status endpoint (detailed):")
    print("-" * 30)
    try:
        status_result = await tools.get_server_status()
        print(status_result[:300] + "..." if len(status_result) > 300 else status_result)
    except Exception as e:
        print(f"❌ Status check failed: {e}")

def main():
    """Main test function"""
    print("🧪 Status Endpoint Implementation Test Suite")
    print("=" * 60)
    
    # Run all tests
    asyncio.run(test_status_functions())
    asyncio.run(test_auto_routing())
    asyncio.run(test_endpoint_comparison())
    
    print("\n" + "=" * 60)
    print("📋 SUMMARY")
    print("=" * 60)
    print("✅ Implemented /status endpoint support in tool")
    print("✅ Added 4 new status-related functions:")
    print("   • get_server_status() - Direct /status endpoint access")
    print("   • check_system_status(detailed=True) - Enhanced with /status option")
    print("   • get_detailed_status() - Convenience wrapper")
    print("   • Auto-routing for 'detailed status' queries")
    print("\n💡 Usage Examples:")
    print("   • 'status' → Basic health check (/health)")
    print("   • 'detailed status' → Comprehensive info (/status)")
    print("   • 'system status' → Basic health check (/health)")
    print("   • 'comprehensive status' → Detailed info (/status)")

if __name__ == "__main__":
    main()
