#!/usr/bin/env python3
"""
Test script to verify help intent detection is working
"""

import requests
import json

# Configuration
OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_help_queries():
    """Test various help-related queries to see if they trigger get_code_analyzer_help"""

    # Reduced set for faster testing
    help_queries = [
        "help",
        "what can you do",
        "available tools"
    ]
    
    session = requests.Session()
    session.headers.update({
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    })
    
    print("🧪 Testing Help Intent Detection")
    print("=" * 50)
    
    for i, query in enumerate(help_queries, 1):
        print(f"\n📋 Test {i}: '{query}'")
        
        payload = {
            "model": "llama3:latest",
            "messages": [
                {
                    "role": "user",
                    "content": query
                }
            ],
            "tool_ids": ["code_analyzer_tool"],
            "stream": False,
            "temperature": 0.7,
            "max_tokens": 500
        }
        
        try:
            print("   Sending request... (timeout: 60s)")
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json=payload,
                timeout=60  # Increased timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                # Check if we got the help documentation
                if "Enhanced Multi-Language Code Analysis System" in content:
                    print("✅ HELP INTENT DETECTED - Got help documentation")
                elif "🚀 **Enhanced Multi-Language Code Analysis System" in content:
                    print("✅ HELP INTENT DETECTED - Got help documentation (formatted)")
                elif len(content) > 1000 and ("automatic" in content.lower() or "optimization" in content.lower()):
                    print("✅ HELP INTENT DETECTED - Got comprehensive help")
                else:
                    print("❌ HELP INTENT NOT DETECTED - Got generic response")
                    if len(content) < 200:
                        print(f"   Response: {content}")
                    else:
                        print(f"   Response preview: {content[:200]}...")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print("❌ REQUEST TIMED OUT - Server taking too long to respond")
            print("   This might indicate the tool is not routing help queries correctly")
        except requests.exceptions.ConnectionError:
            print("❌ CONNECTION ERROR - Cannot reach OpenWebUI server")
        except Exception as e:
            print(f"❌ Request failed: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 Help Intent Testing Complete")

if __name__ == "__main__":
    test_help_queries()
