# TODO: Hardware & Infrastructure Strategy

## 🖥️ Current Hardware Inventory

### Existing Infrastructure
```yaml
Home AI Server:
  GPU: Tesla M40 24GB (Maxwell 2015)
  Role: RAG server, ChromaDB, coordination
  Performance: 0.5-1 chunks/min (baseline)
  Cost: $0 (already owned)
  Availability: 24/7 dedicated

Revenue-Generating RTX Fleet:
  RTX 3070 8GB: Windows machine, Salad workloads
  RTX 3080 10GB: Windows machine, Salad workloads  
  RTX 3090 #1 24GB: Windows machine, Salad workloads
  RTX 3090 #2 24GB: Windows machine, Salad workloads
  
  Total Salad Revenue: ~$160/month ($40/card average)
  Hourly Opportunity Cost: ~$0.22/hour average
```

### Potential Hardware Additions
```yaml
Tesla P40 24GB:
  Cost: ~$200-400 (used market)
  Performance: 2.5-3x faster than M40
  Architecture: Pascal (2016) vs Maxwell (2015)
  Role: Dedicated LLM processing independence
  Placement: Separate machine (home-ai-server hardware limits)
```

---

## 🚀 Performance Analysis

### GPU Performance Comparison for LLM Inference

| GPU | VRAM | Architecture | Relative Performance | Processing Speed | Best Models |
|-----|------|--------------|---------------------|------------------|-------------|
| **Tesla M40** | 24GB | Maxwell (2015) | 1x (baseline) | 0.5-1 chunks/min | smollm2:1.7b |
| **Tesla P40** | 24GB | Pascal (2016) | 2.5-3x | 1.5-2.5 chunks/min | codellama:7b |
| **RTX 3070** | 8GB | Ampere (2020) | 4-5x | 3-5 chunks/min | codellama:7b |
| **RTX 3080** | 10GB | Ampere (2020) | 6-7x | 4-6 chunks/min | deepseek-coder:6.7b |
| **RTX 3090** | 24GB | Ampere (2020) | 8-10x | 8-12 chunks/min | deepseek-coder:33b |

### Processing Time Scenarios (2,283 chunks)

| Configuration | Processing Time | Opportunity Cost | Total Cost | Recommendation |
|---------------|----------------|------------------|------------|----------------|
| **Tesla M40 only** | 2-4 hours | $0 | $0 | Testing only |
| **Tesla P40 only** | 60-90 min | $0 | $300 (one-time) | Good independence |
| **Single RTX 3090** | 15-20 min | ~$0.07 | ~$0.07 | ⭐⭐⭐⭐⭐ **Excellent** |
| **Dual RTX 3090** | 10-15 min | ~$0.11 | ~$0.11 | ⭐⭐⭐⭐⭐ **Outstanding** |
| **All RTX cards** | 8-12 min | ~$0.18 | ~$0.18 | ⭐⭐⭐⭐⭐ **Maximum speed** |

---

## � Embedding Model Recommendations

### Available Ollama Embedding Models Analysis

| Model | Size | Strengths | Best For | Recommendation |
|-------|------|-----------|----------|----------------|
| **nomic-embed-text** | 31.5M | Large context window, code-optimized | **Current choice** | ⭐⭐⭐⭐ **Keep current** |
| **mxbai-embed-large** | 335M | State-of-the-art performance | High-quality embeddings | ⭐⭐⭐⭐⭐ **Upgrade option** |
| **snowflake-arctic-embed2** | 568M | Multilingual, latest generation | Advanced use cases | ⭐⭐⭐⭐ **Premium option** |
| **bge-m3** | 567M | Multi-functional, versatile | Complex queries | ⭐⭐⭐ **Specialized** |
| **snowflake-arctic-embed** | 22M-753M | Multiple sizes, performance-optimized | Flexible deployment | ⭐⭐⭐ **Size options** |
| **all-minilm** | 22M-33M | Lightweight, fast | Resource-constrained | ⭐⭐ **Fallback** |

### Embedding Strategy Recommendations

#### Option 1: Keep Current Setup (Recommended for Now)
```yaml
Model: nomic-embed-text (31.5M)
Rationale:
  - Already configured and working well
  - Optimized for code embeddings
  - Large context window
  - Good performance/resource balance
  - No migration needed

Performance: Currently achieving ~0.254 similarity scores
Cost: $0 (no change needed)
Risk: Low (proven working)
```

#### Option 2: Upgrade to mxbai-embed-large (Future Enhancement)
```yaml
Model: mxbai-embed-large (335M)
Rationale:
  - State-of-the-art embedding performance
  - Likely to improve similarity scores significantly
  - Better semantic understanding
  - Worth testing after LLM summary implementation

Expected Benefits:
  - Similarity scores: 0.254 → 0.4-0.6 (estimated)
  - Better semantic matching for "how does X work?" queries
  - Improved cross-reference detection

Migration Strategy:
  1. Test on subset of codebase first
  2. Compare performance metrics
  3. Full migration if results are significantly better
  4. Requires re-embedding entire codebase (~2-4 hours)
```

#### Option 3: Specialized Use Cases
```yaml
snowflake-arctic-embed2 (568M):
  - Best for: Multilingual codebases
  - Use if: Adding non-English code documentation
  - Performance: Excellent but resource-intensive

bge-m3 (567M):
  - Best for: Multi-granular search (function, class, file level)
  - Use if: Need different embedding strategies per chunk type
  - Performance: Very good for complex queries
```

### Hardware Requirements for Embedding Models

| Model | VRAM Usage | Processing Speed | Best Hardware |
|-------|------------|------------------|---------------|
| **nomic-embed-text** | ~1-2 GB | Fast | Any GPU |
| **mxbai-embed-large** | ~3-4 GB | Moderate | RTX 3070+ |
| **snowflake-arctic-embed2** | ~5-6 GB | Slower | RTX 3080+ |
| **bge-m3** | ~5-6 GB | Slower | RTX 3080+ |

### Implementation Strategy

#### Phase 1: Baseline Optimization (Current)
- **Keep nomic-embed-text** for stability
- **Focus on LLM summaries** for immediate RAG improvement
- **Measure current performance** as baseline

#### Phase 2: Embedding Upgrade Evaluation (After LLM Summaries)
- **Test mxbai-embed-large** on subset of chunks
- **Compare similarity scores** and retrieval quality
- **A/B test** with existing query suite
- **Measure combined impact** of summaries + better embeddings

#### Phase 3: Production Deployment (If Beneficial)
- **Full re-embedding** with optimal model
- **Performance monitoring** and optimization
- **Consider specialized models** for specific use cases

### Recommended Action Plan

1. **Immediate**: Continue with nomic-embed-text (working well)
2. **After LLM Summaries**: Test mxbai-embed-large upgrade
3. **Future**: Consider snowflake-arctic-embed2 for advanced features

**Rationale**: The current embedding model is performing adequately. The bigger performance gains will come from LLM-generated summaries. Once that's implemented, upgrading to mxbai-embed-large could provide additional improvements.

---

## �💰 Cost-Benefit Analysis

### Revenue Impact Assessment
```yaml
Salad Revenue Reality:
  Total Monthly: $160 across all RTX cards
  Per Card: ~$40/month average
  Daily: ~$5.33/day
  Hourly: ~$0.22/hour (when workloads available)
  
Opportunity Cost for LLM Processing:
  20-minute RTX 3090 session: ~$0.07
  15-minute dual RTX 3090 session: ~$0.11
  12-minute all-RTX session: ~$0.18
  
Conclusion: Essentially FREE high-performance processing!
```

### Tesla P40 Investment Analysis
```yaml
Investment: $200-400 (used market)
Benefits:
  - 2.5-3x performance improvement over M40
  - Complete independence from RTX revenue fleet
  - 60-90 minute processing vs 2-4 hours
  - No dependency on other machines
  - 24/7 availability guarantee

ROI Calculation:
  - Time saved per run: 1-3 hours
  - Independence value: High
  - Break-even: 5-10 processing runs
  - Recommendation: Good investment for regular use
```

---

## 🌐 Network Architecture Strategy

### Ollama v0.9.5 Distributed Setup

#### Network Topology
```yaml
Coordinator Node:
  home-ai-server (Tesla M40):
    - Ollama: http://home-ai-server:11434
    - Role: Orchestration, embeddings, fallback
    - Models: nomic-embed-text (current), smollm2:1.7b
    - Availability: 24/7

Processing Nodes:
  Tesla P40 Machine (Optional):
    - Ollama: http://tesla-p40:11434
    - Role: Dedicated LLM processing
    - Models: codellama:7b, deepseek-coder:6.7b
    - Cost: $300 one-time, $0 ongoing

  RTX Fleet (Windows):
    RTX 3070: http://rtx3070-win:11434
    RTX 3080: http://rtx3080-win:11434  
    RTX 3090-1: http://rtx3090-win-1:11434
    RTX 3090-2: http://rtx3090-win-2:11434
    - Role: High-performance processing
    - Models: deepseek-coder:33b, codellama:7b
    - Cost: ~$0.05-0.20 per session
```

#### Windows Configuration
```powershell
# Ollama v0.9.5 Windows setup
# Download from: https://ollama.ai/download/windows

# Enable network exposure
$env:OLLAMA_HOST = "0.0.0.0:11434"
ollama serve

# Pull optimal models
ollama pull deepseek-coder:33b    # RTX 3090s
ollama pull deepseek-coder:6.7b   # RTX 3080
ollama pull codellama:7b-instruct # RTX 3070
```

---

## 🎯 Implementation Strategies

### Strategy 1: RTX-First Approach (Recommended)
```yaml
Primary: Use RTX cards for regular processing
  - Dual RTX 3090s: 10-15 minutes, ~$0.11 cost
  - Single RTX 3090: 15-20 minutes, ~$0.07 cost
  - Excellent performance at minimal cost

Backup: Tesla M40 fallback
  - When all RTX cards busy
  - 2-4 hours processing time
  - $0 cost but much slower

Optional: Tesla P40 for independence
  - If regular processing becomes frequent
  - 60-90 minutes, $300 investment
  - Complete independence from RTX fleet
```

### Strategy 2: Hybrid Independence
```yaml
Primary: Tesla P40 investment
  - Dedicated LLM processing capability
  - 60-90 minutes processing time
  - $300 one-time cost, $0 ongoing
  - No dependency on revenue-generating hardware

Peak Performance: RTX cards when needed
  - For urgent/time-critical processing
  - 10-20 minutes processing time
  - ~$0.07-0.18 opportunity cost
  - Best of both worlds
```

### Strategy 3: Distributed Load Balancing
```yaml
Intelligent Scheduling:
  - Check RTX card availability and Salad rates
  - Route to fastest available GPU
  - Automatic failover between machines
  - Cost-aware decision making

Implementation:
  - Ollama v0.9.5 network exposure
  - HTTP API coordination
  - Revenue-aware scheduling logic
  - Performance monitoring and optimization
```

---

## 🔧 Hardware Requirements

### Minimum Viable Setup
```yaml
Current Tesla M40:
  - Sufficient for testing and fallback
  - 2-4 hour processing time
  - No additional investment required

Network Coordination:
  - Ollama v0.9.5 on all machines
  - Network connectivity between machines
  - HTTP API access (port 11434)
```

### Optimal Setup Options

#### Option 1: RTX Fleet Utilization
```yaml
Investment: $0 (use existing hardware)
Performance: 8-20 minutes processing
Cost per run: $0.07-0.18
Benefits: Immediate high performance
Considerations: Minimal revenue impact
```

#### Option 2: Tesla P40 Addition
```yaml
Investment: $200-400 (used Tesla P40)
Performance: 60-90 minutes processing  
Cost per run: $0 ongoing
Benefits: Complete independence
Considerations: Requires separate machine
```

#### Option 3: Hybrid Approach
```yaml
Investment: $200-400 (Tesla P40)
Performance: 10-90 minutes (depending on choice)
Cost per run: $0-0.18
Benefits: Flexibility and independence
Considerations: Most complex setup
```

---

## 📋 Implementation Roadmap

### Phase 1: Network Setup (Immediate)
- [ ] Upgrade all machines to Ollama v0.9.5
- [ ] Configure network exposure on Windows RTX machines
- [ ] Test distributed coordination from home-ai-server
- [ ] Implement basic load balancing logic

### Phase 2: Performance Optimization (1-2 weeks)
- [ ] Benchmark processing speeds across different GPUs
- [ ] Implement intelligent GPU selection
- [ ] Add revenue-aware scheduling
- [ ] Optimize model selection per GPU

### Phase 3: Infrastructure Decision (1 month)
- [ ] Evaluate Tesla P40 investment based on usage patterns
- [ ] Consider dedicated LLM processing machine
- [ ] Implement failover and redundancy
- [ ] Monitor and optimize performance vs cost

---

## 💡 Strategic Recommendations

### Immediate Action (No Investment)
1. **Setup RTX distributed processing** using Ollama v0.9.5
2. **Use RTX 3090s as primary option** (10-20 min, ~$0.07-0.11 cost)
3. **Implement revenue-aware scheduling** to minimize Salad impact

### Medium-term Consideration (Optional $300)
1. **Tesla P40 investment** for processing independence
2. **Dedicated LLM machine** if processing becomes regular
3. **Hybrid approach** for optimal flexibility

### Result
**World-class distributed LLM processing** at essentially zero ongoing cost, with optional independence investment for guaranteed availability.

---

*Last Updated: 2025-07-03*
*Based on realistic Salad revenue of $160/month and available hardware analysis*
