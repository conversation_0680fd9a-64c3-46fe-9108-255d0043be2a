/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2023 */
/*****************************************************************************/
/*                                                                           */
/* This file is the property of:                                             */
/*                                                                           */
/*                       Triangle MicroWorks, Inc.                           */
/*                      Raleigh, North Carolina USA                          */
/*                       www.TriangleMicroWorks.com                          */
/*                          (919) 870-6615                                   */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*                                                                           */
/*****************************************************************************/

/* file: smbsesn.c
 * description: Implement a Modbus Server session
 */
#include "tmwscl/modbus/mbdiag.h"
#include "tmwscl/modbus/mbdefs.h"
#include "tmwscl/modbus/mbchnl.h"
#include "tmwscl/modbus/mbsesn.h"
#include "tmwscl/modbus/smbsesn.h"
#include "tmwscl/modbus/smbdata.h"
#include "tmwscl/modbus/smbmem.h"
#include "tmwscl/modbus/mbmem.h"
#include "tmwscl/modbus/mbdefs.h"

#include "tmwscl/utils/tmwtarg.h"

/* Forward declarations - private to this module */


/* function: _sendResponse */
static void TMWDEFS_LOCAL _sendResponse(
  TMWSESN_TX_DATA *pTxData)
{
  pTxData->txFlags = TMWSESN_TXFLAGS_NO_RESPONSE;
  if (!mbchnl_sendMessage(pTxData))
  {
    mbchnl_freeTxData(pTxData);
  }
}

static void _sendResponseFailure(
  TMWSESN_RX_DATA *pRequest,
  TMWSESN_TX_DATA *pTxData,
  TMWTYPES_UCHAR exceptionCode)
{
  if(pTxData == TMWDEFS_NULL)
    return;

  /* Update statistics */
  TMWSESN_STAT_CALLBACK_FUNC(pTxData->pSession,
    TMWSESN_STAT_EXCEPTION_RESP, &exceptionCode);

  /* Reply with Exception Response (FC & 0x80) and Exception Code) */
  pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[0] | 0x80;
  pTxData->pMsgBuf[pTxData->msgLength++] = exceptionCode;
  _sendResponse(pTxData);
 
  MBDIAG_RESPONSE_ERROR(pRequest, exceptionCode);
}

#if SMBDATA_SUPPORT_FC_R_COILS
/* function: _processReadCoils */
static TMWTYPES_BOOL TMWDEFS_LOCAL _processReadCoils(
  TMWSESN *pSession,
  TMWSESN_RX_DATA *pRequest)
{
  TMWTYPES_USHORT byteCount;
  TMWTYPES_USHORT i;

  SMBSESN *pSMBSession = (SMBSESN *)pSession;

  TMWTYPES_USHORT start = MBDEFS_MAKEWORD(pRequest->pMsgBuf[1], pRequest->pMsgBuf[2]);
  TMWTYPES_USHORT quantity = MBDEFS_MAKEWORD(pRequest->pMsgBuf[3], pRequest->pMsgBuf[4]);
  TMWTYPES_UCHAR tmpData[MBDEFS_SMB_MAX_COIL_READ/8 +1];

  TMWSESN_TX_DATA *pTxData = mbchnl_newTxData(pSession->pChannel, pSession, 256);
  if(pTxData == TMWDEFS_NULL)
    return(TMWDEFS_FALSE);

  /* Validate quantity requested */
  if((quantity == 0)
    || (quantity > MBDEFS_SMB_MAX_COIL_READ))
  {
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_VALUE);
    return(TMWDEFS_FALSE);
  }

  /* Validate address range */
  if(   (quantity > pSMBSession->maxCoil_Read)
     || (!smbdata_CoilsValidateRange(pSMBSession->mb.pDbHandle, start, quantity)))
  {
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_ADDRESS);
    return(TMWDEFS_FALSE);
  }

  /* set last data byte to zero, to make sure unused bits are zero */
  tmpData[quantity/8] = 0;
  if (!smbdata_getCoils(pSMBSession->mb.pDbHandle, start, quantity, tmpData))
  {
    /* If we can't read from DB then return a Server Device Failure EC */
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_SERVER_DEVICE_FAILURE);
    return(TMWDEFS_FALSE);
  }

  /* Send Good Response */
  byteCount = (quantity/8) + ((quantity%8)?1:0);

  for(i = 0; i < quantity; i++)
  {
    TMWTYPES_BOOL bVal;
    bVal = (tmpData[i/8] >> i%8) & 0x01;
    MBDIAG_SHOW_COIL(pSession, (TMWTYPES_USHORT)(start + i), bVal);
  }

  pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[0]; /* MB FC */
  pTxData->pMsgBuf[pTxData->msgLength++] = (TMWTYPES_UCHAR) byteCount;

  memcpy(&(pTxData->pMsgBuf[pTxData->msgLength]), tmpData, byteCount);
  pTxData->msgLength += byteCount;

  _sendResponse(pTxData);

  return(TMWDEFS_TRUE);
}
#endif

#if SMBDATA_SUPPORT_FC_R_D_INPUTS
/* function: _processReadDiscreteInputs */
static TMWTYPES_BOOL TMWDEFS_LOCAL _processReadDiscreteInputs(
  TMWSESN *pSession,
  TMWSESN_RX_DATA *pRequest)
{
  TMWTYPES_USHORT byteCount;
  TMWTYPES_USHORT i;

  SMBSESN *pSMBSession = (SMBSESN *)pSession;

  TMWTYPES_USHORT start = MBDEFS_MAKEWORD(pRequest->pMsgBuf[1], pRequest->pMsgBuf[2]);
  TMWTYPES_USHORT quantity = MBDEFS_MAKEWORD(pRequest->pMsgBuf[3], pRequest->pMsgBuf[4]);
  TMWTYPES_UCHAR tmpData[MBDEFS_SMB_MAX_DI_READ/8 +1];

  TMWSESN_TX_DATA *pTxData = mbchnl_newTxData(pSession->pChannel, pSession, 256);
  if(pTxData == TMWDEFS_NULL)
    return(TMWDEFS_FALSE);

  /* Validate quantity requested */
  if((quantity == 0)
    || (quantity > MBDEFS_SMB_MAX_DI_READ))
  {
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_VALUE);
    return(TMWDEFS_FALSE);
  }

  /* Validate address range */
  if(   (quantity > pSMBSession->maxDI_Read)
     || (!smbdata_DiscreteInputsValidateRange(pSMBSession->mb.pDbHandle, start, quantity)))
  {
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_ADDRESS);
    return(TMWDEFS_FALSE);
  }

  /* set last data byte to zero, to make sure unused bits are zero */
  tmpData[quantity/8] = 0;
  if (!smbdata_getDiscreteInputs(pSMBSession->mb.pDbHandle, start, quantity, tmpData))
  {
    /* If we can't read from DB then return a Server Device Failure EC */
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_SERVER_DEVICE_FAILURE);
    return(TMWDEFS_FALSE);
  }

  /* Send Good Response */
  byteCount = (quantity/8) + ((quantity%8)?1:0);

  for(i = 0; i < quantity; i++)
  {
    TMWTYPES_BOOL bVal;
    bVal = (tmpData[i/8] >> i%8) & 0x01;
    MBDIAG_SHOW_DISCRETE_INPUT(pSession, (TMWTYPES_USHORT)(start + i), bVal);
  }

  pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[0]; /* MB FC */
  pTxData->pMsgBuf[pTxData->msgLength++] = (TMWTYPES_UCHAR) byteCount;

  memcpy(&(pTxData->pMsgBuf[pTxData->msgLength]), tmpData, byteCount);
  pTxData->msgLength += byteCount;

  _sendResponse(pTxData);

  return(TMWDEFS_TRUE);
}
#endif

#if SMBDATA_SUPPORT_FC_R_H_REGISTERS
/* function: _processReadHoldingRegisters */
static TMWTYPES_BOOL TMWDEFS_LOCAL _processReadHoldingRegisters(
  TMWSESN *pSession,
  TMWSESN_RX_DATA *pRequest)
{
  TMWTYPES_USHORT i;
  SMBSESN *pSMBSession = (SMBSESN *)pSession;

  TMWTYPES_USHORT start = MBDEFS_MAKEWORD(pRequest->pMsgBuf[1], pRequest->pMsgBuf[2]);
  TMWTYPES_USHORT quantity = MBDEFS_MAKEWORD(pRequest->pMsgBuf[3], pRequest->pMsgBuf[4]);
  TMWTYPES_USHORT tmpData[MBDEFS_SMB_MAX_HREG_READ];

  TMWSESN_TX_DATA *pTxData = mbchnl_newTxData(pSession->pChannel, pSession, 256);
  if(pTxData == TMWDEFS_NULL)
    return(TMWDEFS_FALSE);

  /* Validate quantity requested */
  if((quantity == 0)
    || (quantity > MBDEFS_SMB_MAX_HREG_READ))
  {
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_VALUE);
    return(TMWDEFS_FALSE);
  }

  /* Validate address range */
  if(   (quantity > pSMBSession->maxHReg_Read)
     || (!smbdata_HoldingRegistersValidateRange(pSMBSession->mb.pDbHandle, start, quantity)))
  {
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_ADDRESS);
    return(TMWDEFS_FALSE);
  }

  if (!smbdata_getHoldingRegisters(pSMBSession->mb.pDbHandle, start, quantity, tmpData))
  {
    /* If we can't read from DB then return a Server Device Failure EC */
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_SERVER_DEVICE_FAILURE);
    return(TMWDEFS_FALSE);
  }

  /* Send Good Response */
  pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[0]; /* MB FC */
  pTxData->pMsgBuf[pTxData->msgLength++] = (TMWTYPES_UCHAR)(quantity * 2);

  for(i = 0; i < quantity; i++)
  {
    pTxData->pMsgBuf[pTxData->msgLength++] = MBDEFS_HIBYTE(tmpData[i]);
    pTxData->pMsgBuf[pTxData->msgLength++] = MBDEFS_LOBYTE(tmpData[i]);

    MBDIAG_SHOW_HOLDING_REGISTER(pSession, (TMWTYPES_USHORT)(start + i), tmpData[i]);
  }

  _sendResponse(pTxData);

  return(TMWDEFS_TRUE);
}
#endif

#if SMBDATA_SUPPORT_FC_R_I_REGISTERS
/* function: _processReadInputRegisters */
static TMWTYPES_BOOL TMWDEFS_LOCAL _processReadInputRegisters(
  TMWSESN *pSession,
  TMWSESN_RX_DATA *pRequest)
{
  TMWTYPES_USHORT i;
  TMWTYPES_USHORT tmpData[MBDEFS_SMB_MAX_IREG_READ];

  SMBSESN *pSMBSession = (SMBSESN *)pSession;

  TMWTYPES_USHORT start = MBDEFS_MAKEWORD(pRequest->pMsgBuf[1], pRequest->pMsgBuf[2]);
  TMWTYPES_USHORT quantity = MBDEFS_MAKEWORD(pRequest->pMsgBuf[3], pRequest->pMsgBuf[4]);

  TMWSESN_TX_DATA *pTxData = mbchnl_newTxData(pSession->pChannel, pSession, 256);
  if(pTxData == TMWDEFS_NULL)
    return(TMWDEFS_FALSE);

  /* Validate quantity requested */
  if((quantity == 0)
    || (quantity > MBDEFS_SMB_MAX_IREG_READ))
  {
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_VALUE);
    return(TMWDEFS_FALSE);
  }

  /* Validate address range */
  if(   (quantity > pSMBSession->maxIReg_Read)
     || (!smbdata_InputRegistersValidateRange(pSMBSession->mb.pDbHandle, start, quantity)))
  {
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_ADDRESS);
    return(TMWDEFS_FALSE);
  }

  if (!smbdata_getInputRegisters(pSMBSession->mb.pDbHandle, start, quantity, tmpData))
  {
    /* If we can't read from DB then return a Server Device Failure EC */
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_SERVER_DEVICE_FAILURE);
    return(TMWDEFS_FALSE);
  }

  /* Send Good Response */
  pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[0]; /* MB FC */
  pTxData->pMsgBuf[pTxData->msgLength++] = (TMWTYPES_UCHAR)(quantity * 2);

  for(i = 0; i < quantity; i++)
  {
    pTxData->pMsgBuf[pTxData->msgLength++] = MBDEFS_HIBYTE(tmpData[i]);
    pTxData->pMsgBuf[pTxData->msgLength++] = MBDEFS_LOBYTE(tmpData[i]);

    MBDIAG_SHOW_INPUT_REGISTER(pSession, (TMWTYPES_USHORT)(start + i), tmpData[i]);
  }

  _sendResponse(pTxData);

  return(TMWDEFS_TRUE);
}
#endif

#if SMBDATA_SUPPORT_FC_W_COIL
/* function: _processWriteSingleCoil */
static TMWTYPES_BOOL TMWDEFS_LOCAL _processWriteSingleCoil(
  TMWSESN *pSession,
  TMWSESN_RX_DATA *pRequest)
{
  SMBSESN *pSMBSession = (SMBSESN *)pSession;
  TMWSESN_TX_DATA *pTxData = TMWDEFS_NULL;
  TMWTYPES_USHORT start = MBDEFS_MAKEWORD(pRequest->pMsgBuf[1], pRequest->pMsgBuf[2]);
  TMWTYPES_USHORT value = MBDEFS_MAKEWORD(pRequest->pMsgBuf[3], pRequest->pMsgBuf[4]);
  TMWTYPES_UCHAR tmpData[1];

  if(!pRequest->isBroadcast)
  {
    pTxData = mbchnl_newTxData(pSession->pChannel, pSession, 256);
    if(pTxData == TMWDEFS_NULL)
      return(TMWDEFS_FALSE);
  }

  /* Verify Coil value */
  if ((value != 0x0000) && (value != 0xFF00))
  {
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_VALUE);
    return(TMWDEFS_FALSE);
  }

  /* Validate start address */
  if(!smbdata_CoilsValidateRange(pSMBSession->mb.pDbHandle, start, 1))
  {
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_ADDRESS);
    return(TMWDEFS_FALSE);
  }

  /* Try to store data to target database for this point */
  tmpData[0] = (value == 0x0000) ? 0 : 1;
  if (!smbdata_storeCoils(pSMBSession->mb.pDbHandle, start, 1, tmpData))
  {
    /* If we can't store to DB then return a Server Device Failure EC */
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_SERVER_DEVICE_FAILURE);
    return(TMWDEFS_FALSE);
  }

  /* If not broadcast send Good Response */
  if(!pRequest->isBroadcast)
  {
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[0]; /* MB FC */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[1]; /* start HB */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[2]; /* satrt LB */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[3]; /* value HB */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[4]; /* value LB */

    _sendResponse(pTxData);
  }

  return(TMWDEFS_TRUE);
}
#endif

#if SMBDATA_SUPPORT_FC_W_REGISTER
/* function: _processWriteSingleRegister */
static TMWTYPES_BOOL TMWDEFS_LOCAL _processWriteSingleRegister(
  TMWSESN *pSession,
  TMWSESN_RX_DATA *pRequest)
{
  SMBSESN *pSMBSession = (SMBSESN *)pSession;
  TMWSESN_TX_DATA *pTxData = TMWDEFS_NULL;
  TMWTYPES_USHORT start = MBDEFS_MAKEWORD(pRequest->pMsgBuf[1], pRequest->pMsgBuf[2]);
  TMWTYPES_USHORT value = MBDEFS_MAKEWORD(pRequest->pMsgBuf[3], pRequest->pMsgBuf[4]);
  TMWTYPES_USHORT tmpData[1];

  if(!pRequest->isBroadcast)
  {
    pTxData = mbchnl_newTxData(pSession->pChannel, pSession, 256);
    if(pTxData == TMWDEFS_NULL)
      return(TMWDEFS_FALSE);
  }
 
  /* Validate start address */
  if(!smbdata_HoldingRegistersValidateRange(pSMBSession->mb.pDbHandle, start, 1))
  {
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_ADDRESS);
    return(TMWDEFS_FALSE);
  }

  /* Try to store data to target database for this point */
  tmpData[0] = value;
  if (!smbdata_storeHoldingRegisters(pSMBSession->mb.pDbHandle, start, 1, tmpData))
  {
    /* If we can't store to DB then return a Server Device Failure EC */
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_SERVER_DEVICE_FAILURE);
    return(TMWDEFS_FALSE);
  }

  /* If not broadcast send Good Response */
  if(!pRequest->isBroadcast)
  {
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[0]; /* MB FC */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[1]; /* start HB */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[2]; /* start LB */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[3]; /* value HB */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[4]; /* value LB */

    _sendResponse(pTxData);
  }

  return(TMWDEFS_TRUE);
}
#endif

#if SMBDATA_SUPPORT_FC_MW_REGISTER
/* function: _processMaskWriteRegister */
static TMWTYPES_BOOL TMWDEFS_LOCAL _processMaskWriteRegister(
  TMWSESN *pSession,
  TMWSESN_RX_DATA *pRequest)
{
  SMBSESN *pSMBSession = (SMBSESN *)pSession;
  TMWSESN_TX_DATA *pTxData = TMWDEFS_NULL;
  TMWTYPES_USHORT start = MBDEFS_MAKEWORD(pRequest->pMsgBuf[1], pRequest->pMsgBuf[2]);
  TMWTYPES_USHORT andMask = MBDEFS_MAKEWORD(pRequest->pMsgBuf[3], pRequest->pMsgBuf[4]);
  TMWTYPES_USHORT orMask = MBDEFS_MAKEWORD(pRequest->pMsgBuf[5], pRequest->pMsgBuf[6]);
  TMWTYPES_USHORT tmpData;

  if(!pRequest->isBroadcast)
  {
    pTxData = mbchnl_newTxData(pSession->pChannel, pSession, 256);
    if(pTxData == TMWDEFS_NULL)
      return(TMWDEFS_FALSE);
  }
 
  /* Validate start address */
  if(!smbdata_HoldingRegistersValidateRange(pSMBSession->mb.pDbHandle, start, 1))
  {
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_ADDRESS);
    return(TMWDEFS_FALSE);
  }

  if (!smbdata_getHoldingRegisters(pSMBSession->mb.pDbHandle, start, 1, &tmpData))
  {
    /* If we can't read from DB then return a Server Device Failure EC */
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_SERVER_DEVICE_FAILURE);
    return(TMWDEFS_FALSE);
  }

  tmpData &= andMask;
  tmpData |= (orMask & ~andMask);

  if (!smbdata_storeHoldingRegisters(pSMBSession->mb.pDbHandle, start, 1, &tmpData))
  {
    /* If we can't store to DB then return a Server Device Failure EC */
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_SERVER_DEVICE_FAILURE);
    return(TMWDEFS_FALSE);
  }

  /* If not broadcast send Good Response */
  if(!pRequest->isBroadcast)
  {
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[0]; /* MB FC */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[1]; /* start HB */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[2]; /* start LB */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[3]; /* andMask HB */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[4]; /* andMask LB */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[5]; /* orMask HB */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[6]; /* orMask LB */

    _sendResponse(pTxData);
  }

  return(TMWDEFS_TRUE);
}
#endif

#if SMBDATA_SUPPORT_FC_R_EXCEPTION
/* function: _processReadExceptionStatus */
static TMWTYPES_BOOL TMWDEFS_LOCAL _processReadExceptionStatus(
  TMWSESN *pSession,
  TMWSESN_RX_DATA *pRequest)
{
  TMWTYPES_UCHAR status;
  SMBSESN *pSMBSession = (SMBSESN *)pSession;

  TMWSESN_TX_DATA *pTxData = mbchnl_newTxData(pSession->pChannel, pSession, 16); 
  if(pTxData == TMWDEFS_NULL)
    return(TMWDEFS_FALSE);

  if (!smbdata_readExceptionStatus(pSMBSession->mb.pDbHandle, &status))
  {
    /* If we can't read the DB then return a Server Device Failure EC */
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest, pTxData, MBDEFS_MBE_SERVER_DEVICE_FAILURE);
    return(TMWDEFS_FALSE);
  }

  /* Send Good Response */
  pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[0]; /* MB FC */ 
  pTxData->pMsgBuf[pTxData->msgLength++] = status; /* status bits */

  _sendResponse(pTxData);

  return(TMWDEFS_TRUE);
}
#endif

#if SMBDATA_SUPPORT_FC_DIAGNOSTICS
/* function: _processDiagnostics */
static TMWTYPES_BOOL TMWDEFS_LOCAL _processDiagnostics(
  TMWSESN *pSession,
  TMWSESN_RX_DATA *pRequest)
{
  SMBSESN *pSMBSession;
  TMWTYPES_USHORT requestData;
  TMWTYPES_USHORT subFunction; 
  TMWTYPES_UCHAR  exceptionCode;
  TMWTYPES_UCHAR  responseLength;

  TMWSESN_TX_DATA *pTxData = mbchnl_newTxData(pSession->pChannel, pSession, 256);
  if(pTxData == TMWDEFS_NULL)
    return(TMWDEFS_FALSE);

  pSMBSession = (SMBSESN *)pSession;
  subFunction = MBDEFS_MAKEWORD(pRequest->pMsgBuf[1], pRequest->pMsgBuf[2]); 

  if (!smbdata_diagSubFunctionSupported(pSMBSession->mb.pDbHandle, subFunction))
  {
    /* If sub-function is not supported  
     * Reply with Exception Response (FC & 0x80) and Exception Code) 
     */
    _sendResponseFailure(pRequest, pTxData, MBDEFS_MBE_ILLEGAL_FUNCTION);
    return(TMWDEFS_FALSE);
  }

  pTxData->pMsgBuf[0] = pRequest->pMsgBuf[0]; /* MB FC */
  pTxData->pMsgBuf[1] = pRequest->pMsgBuf[1]; /* subFunction HB */
  pTxData->pMsgBuf[2] = pRequest->pMsgBuf[2]; /* subFunction LB */

   
  /* 0 means no exception code to send */
  exceptionCode = 0;
  /* Implement the subFunctions that the SCL can support here */
  switch(subFunction)
  {
  case MBDEFS_DIAG_QUERY_DATA: 
    if((pRequest->msgLength > 3)
      && (pRequest->msgLength <= pTxData->maxLength))
    {
      responseLength = pRequest->msgLength-3;
      memcpy(&pTxData->pMsgBuf[3], &pRequest->pMsgBuf[3], responseLength);
    }
    else
    {
      exceptionCode = MBDEFS_MBE_ILLEGAL_DATA_VALUE;
    }
    break;

  case MBDEFS_DIAG_RESTART_COMM:
    if(pRequest->msgLength > 4)
    {
      requestData = MBDEFS_MAKEWORD(pRequest->pMsgBuf[3], pRequest->pMsgBuf[4]); 
      smbdata_diagRestart(pSMBSession->mb.pDbHandle, requestData);
   
      pSMBSession->listenOnlyMode = TMWDEFS_FALSE;
    
      pTxData->pMsgBuf[3] = pRequest->pMsgBuf[3];
      pTxData->pMsgBuf[4] = pRequest->pMsgBuf[4];
      responseLength = 2; 
    }
    else
    {
      exceptionCode = MBDEFS_MBE_ILLEGAL_DATA_VALUE;
    }
    break;

  case MBDEFS_DIAG_CHANGE_ASCII_DELIM:
    if(pRequest->msgLength > 4)
    {
      MBLINK_CONTEXT *pLinkContext = (MBLINK_CONTEXT *)pSession->pChannel->pLinkContext;
      pLinkContext->delim2 = pRequest->pMsgBuf[3];
      pTxData->pMsgBuf[3]  = pRequest->pMsgBuf[3];
      pTxData->pMsgBuf[4]  = pRequest->pMsgBuf[4];
      responseLength = 2;
    }
    else
    {
      exceptionCode = MBDEFS_MBE_ILLEGAL_DATA_VALUE;
    }
    break;

  case MBDEFS_DIAG_FORCE_LISTEN_ONLY:
    smbdata_diagListenOnly(pSMBSession->mb.pDbHandle);
    pSMBSession->listenOnlyMode = TMWDEFS_TRUE;
    responseLength = 0;
    break;

  case MBDEFS_DIAG_CLEAR_BUS_COUNTERS:
  case MBDEFS_DIAG_CLEAR_OVERRUN_COUNT:
    if(pRequest->msgLength > 4)
    {
      smbdata_diagClearCount(pSMBSession->mb.pDbHandle, subFunction);
      pTxData->pMsgBuf[3] = pRequest->pMsgBuf[3];
      pTxData->pMsgBuf[4] = pRequest->pMsgBuf[4];
      responseLength = 2;
    }
    else
    {
      exceptionCode = MBDEFS_MBE_ILLEGAL_DATA_VALUE;
    }
    break;

  case MBDEFS_DIAG_RETURN_DIAG_REGISTER:
  case MBDEFS_DIAG_GET_BUS_MSG_COUNT:
  case MBDEFS_DIAG_GET_BUS_CRC_ERR_COUNT:
  case MBDEFS_DIAG_GET_BUS_EXC_COUNT:
  case MBDEFS_DIAG_GET_SERVER_MSG_COUNT:
  case MBDEFS_DIAG_GET_SERVER_NR_COUNT:
  case MBDEFS_DIAG_GET_SERVER_NAK_COUNT:
  case MBDEFS_DIAG_GET_SERVER_BUSY_COUNT:
  case MBDEFS_DIAG_GET_BUS_OVERRUN_COUNT:
    {
      TMWTYPES_USHORT value;
      smbdata_diagGetValue(pSMBSession->mb.pDbHandle, subFunction, &value);
      pTxData->pMsgBuf[3] = MBDEFS_HIBYTE(value);
      pTxData->pMsgBuf[4] = MBDEFS_LOBYTE(value);
      responseLength = 2;
    }
    break;

  default:
    if(pRequest->msgLength > 3)
    {
      /* Pass other diagnostic subfunctions to database */
      responseLength = 253; /* bytes available in buffer */
      if (!smbdata_diagnostics(pSMBSession->mb.pDbHandle, subFunction, (TMWTYPES_UCHAR)(pRequest->msgLength-3), &pRequest->pMsgBuf[3], 
        &responseLength, &pTxData->pMsgBuf[3]))
      {
        /* If we can't read the DB then return a Server Device Failure EC */
        /* Reply with Exception Response (FC & 0x80) and Exception Code) */
        exceptionCode = MBDEFS_MBE_SERVER_DEVICE_FAILURE;
      }
    }
    else
    {
      exceptionCode = MBDEFS_MBE_ILLEGAL_DATA_VALUE;
    }
  }  

  if(exceptionCode != 0)
  {
     /* Reply with Exception Response (FC & 0x80) and Exception Code) */
     _sendResponseFailure(pRequest, pTxData, exceptionCode);
     return(TMWDEFS_FALSE);
  }

  if(responseLength > 0)
  {
    /* Send Good Response */
    /* Response data was copied in above */ 
    pTxData->msgLength = responseLength +3;
    _sendResponse(pTxData);
  }
  else
  {
    TMWSESN_STAT_CALLBACK_FUNC(pTxData->pSession,
      TMWSESN_STAT_NO_RESPONSE, TMWDEFS_NULL);

    mbchnl_freeTxData(pTxData);
  }

  return(TMWDEFS_TRUE);
}
#endif

#if SMBDATA_SUPPORT_FC_W_MCOILS
/* function: _processWriteMultipleCoils */
static TMWTYPES_BOOL TMWDEFS_LOCAL _processWriteMultipleCoils(
  TMWSESN *pSession,
  TMWSESN_RX_DATA *pRequest)
{
  SMBSESN *pSMBSession = (SMBSESN *)pSession;
  TMWSESN_TX_DATA *pTxData = TMWDEFS_NULL;
  TMWTYPES_USHORT start = MBDEFS_MAKEWORD(pRequest->pMsgBuf[1], pRequest->pMsgBuf[2]);
  TMWTYPES_USHORT quantity = MBDEFS_MAKEWORD(pRequest->pMsgBuf[3], pRequest->pMsgBuf[4]);
  TMWTYPES_UCHAR tmpData[MBDEFS_SMB_MAX_COIL_WRITE/8 +1];
  TMWTYPES_USHORT byteCount;

  if(!pRequest->isBroadcast)
  {
    pTxData = mbchnl_newTxData(pSession->pChannel, pSession, 256);
    if(pTxData == TMWDEFS_NULL)
      return(TMWDEFS_FALSE);
  }

  byteCount = pRequest->pMsgBuf[5];

  /* Validate quantity requested to be written */
  if((quantity < 1) 
     || (quantity > MBDEFS_SMB_MAX_COIL_WRITE) 
     || (byteCount != (quantity/8) + ((quantity%8)?1:0))
     || ((byteCount + 6) > pRequest->msgLength))
  {
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_VALUE);
    return(TMWDEFS_FALSE);
  }

  /* Validate address range */
  if((quantity > pSMBSession->maxCoil_Write)
     || (!smbdata_CoilsValidateRange(pSMBSession->mb.pDbHandle, start, quantity)))
  {
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_ADDRESS);
    return(TMWDEFS_FALSE);
  }

  /* Try to store data to target database for this request */
  memcpy(tmpData, &(pRequest->pMsgBuf[6]), byteCount);
  if (!smbdata_storeCoils(pSMBSession->mb.pDbHandle, start, quantity, tmpData))
  {
    /* If we can't store to DB then return a Server Device Failure EC */
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_SERVER_DEVICE_FAILURE);
    return(TMWDEFS_FALSE);
  }

  /* If not broadcast send Good Response */
  if(!pRequest->isBroadcast)
  {
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[0]; /* MB FC */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[1]; /* start HB */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[2]; /* satrt LB */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[3]; /* quantity HB */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[4]; /* quantity LB */

    _sendResponse(pTxData);
  }

  return(TMWDEFS_TRUE);
}
#endif

#if SMBDATA_SUPPORT_FC_W_MHREGISTERS
/* function: _processWriteMultipleHoldingRegisters */
static TMWTYPES_BOOL TMWDEFS_LOCAL _processWriteMultipleHoldingRegisters(
  TMWSESN *pSession,
  TMWSESN_RX_DATA *pRequest)
{
  SMBSESN *pSMBSession = (SMBSESN *)pSession;
  TMWSESN_TX_DATA *pTxData = TMWDEFS_NULL;
  TMWTYPES_USHORT start = MBDEFS_MAKEWORD(pRequest->pMsgBuf[1], pRequest->pMsgBuf[2]);
  TMWTYPES_USHORT quantity = MBDEFS_MAKEWORD(pRequest->pMsgBuf[3], pRequest->pMsgBuf[4]);
  TMWTYPES_USHORT byteCount;
  TMWTYPES_USHORT tmpData[MBDEFS_SMB_MAX_HREG_WRITE];
  TMWTYPES_USHORT i;
  
  if(!pRequest->isBroadcast)
  {
    pTxData = mbchnl_newTxData(pSession->pChannel, pSession, 256);
    if(pTxData == TMWDEFS_NULL)
      return(TMWDEFS_FALSE);
  }

  byteCount = pRequest->pMsgBuf[5];
  /* Validate quantity requested to write */
  if((quantity < 1) 
    || (quantity > MBDEFS_SMB_MAX_HREG_WRITE) 
    || (byteCount != quantity * 2)
    || ((byteCount + 6) > pRequest->msgLength))
  {
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_VALUE);
    return(TMWDEFS_FALSE);
  }

  /* Validate address range */
  if(   (quantity > pSMBSession->maxHreg_Write)
     || (!smbdata_HoldingRegistersValidateRange(pSMBSession->mb.pDbHandle, start, quantity)))
  {
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_ADDRESS);
    return(TMWDEFS_FALSE);
  }

  /* Try to store data to target database for this request */
  for(i = 0; i < quantity; i++)
    tmpData[i] = MBDEFS_MAKEWORD(pRequest->pMsgBuf[i*2+6], pRequest->pMsgBuf[i*2+7]);

  if (!smbdata_storeHoldingRegisters(pSMBSession->mb.pDbHandle, start, quantity, tmpData))
  {
    /* If we can't store to DB then return a Server Device Failure EC */
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_SERVER_DEVICE_FAILURE);
    return(TMWDEFS_FALSE);
  }

  /* If not broadcast send Good Response */
  if(!pRequest->isBroadcast)
  {
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[0]; /* MB FC */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[1]; /* start HB */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[2]; /* satrt LB */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[3]; /* quantity HB */
    pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[4]; /* quantity LB */

    _sendResponse(pTxData);
  }

  return(TMWDEFS_TRUE);
}
#endif

#if SMBDATA_SUPPORT_FC_RW_MREGISTERS
/* function: _processReadWriteMultipleRegisters */
static TMWTYPES_BOOL TMWDEFS_LOCAL _processReadWriteMultipleRegisters(
  TMWSESN *pSession,
  TMWSESN_RX_DATA *pRequest)
{
  SMBSESN *pSMBSession = (SMBSESN *)pSession;
  TMWSESN_TX_DATA *pTxData;

  TMWTYPES_USHORT readStart = MBDEFS_MAKEWORD(pRequest->pMsgBuf[1], pRequest->pMsgBuf[2]);
  TMWTYPES_USHORT quantityToRead = MBDEFS_MAKEWORD(pRequest->pMsgBuf[3], pRequest->pMsgBuf[4]);
  TMWTYPES_USHORT writeStart = MBDEFS_MAKEWORD(pRequest->pMsgBuf[5], pRequest->pMsgBuf[6]);
  TMWTYPES_USHORT quantityToWrite = MBDEFS_MAKEWORD(pRequest->pMsgBuf[7], pRequest->pMsgBuf[8]);
  TMWTYPES_USHORT writeByteCount = pRequest->pMsgBuf[9];
  TMWTYPES_USHORT tmpData[MBDEFS_SMB_MAX_HREG_READ_FC23];
  TMWTYPES_USHORT i;
  
  pTxData = mbchnl_newTxData(pSession->pChannel, pSession, 256);
  if(pTxData == TMWDEFS_NULL)
    return(TMWDEFS_FALSE);

  /* Validate quantity requested to read/write */
  if( (quantityToRead < 1)
    || (quantityToRead > MBDEFS_SMB_MAX_HREG_READ_FC23)
    || (quantityToWrite > MBDEFS_SMB_MAX_HREG_WRITE_FC23)
    || (writeByteCount != quantityToWrite * 2)  
    || ((writeByteCount + 10 ) > pRequest->msgLength))
  {
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_VALUE);
    return(TMWDEFS_FALSE);
  }

  /* Validate address range */
  if(   (quantityToRead > pSMBSession->maxHReg_Read_FC23)
     || (quantityToWrite > pSMBSession->maxHreg_Write_FC23)
     || (!smbdata_HoldingRegistersValidateRange(pSMBSession->mb.pDbHandle, readStart, quantityToRead))
     || (!smbdata_HoldingRegistersValidateRange(pSMBSession->mb.pDbHandle, writeStart, quantityToWrite)))
  {
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_ADDRESS);
    return(TMWDEFS_FALSE);
  }

  /* Try to store/get data to/from target database for this request */
  for(i = 0; i < quantityToWrite; i++)
    tmpData[i] = MBDEFS_MAKEWORD(pRequest->pMsgBuf[i*2+10], pRequest->pMsgBuf[i*2+11]);

  if ( !smbdata_storeHoldingRegisters(pSMBSession->mb.pDbHandle, writeStart, quantityToWrite, tmpData)
    || !smbdata_getHoldingRegisters(pSMBSession->mb.pDbHandle, readStart, quantityToRead, tmpData) )
  {
    /* If we can't store to DB then return a Server Device Failure EC */
    /* Reply with Exception Response (FC & 0x80) and Exception Code) */
    _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_SERVER_DEVICE_FAILURE);
    return(TMWDEFS_FALSE);
  }

  /* Send Good Response */
  pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[0]; /* MB FC */
  pTxData->pMsgBuf[pTxData->msgLength++] = (TMWTYPES_UCHAR)(quantityToRead * 2);

  for(i = 0; i < quantityToRead; i++)
  {
    pTxData->pMsgBuf[pTxData->msgLength++] = MBDEFS_HIBYTE(tmpData[i]);
    pTxData->pMsgBuf[pTxData->msgLength++] = MBDEFS_LOBYTE(tmpData[i]);
  }

  _sendResponse(pTxData);

  return(TMWDEFS_TRUE);
}
#endif

#if SMBDATA_SUPPORT_FC_READ_DEV_ID
/* function: _processReadDeviceId */
static TMWTYPES_BOOL TMWDEFS_LOCAL _processReadDeviceId(
  TMWSESN *pSession,
  TMWSESN_RX_DATA *pRequest)
{
  TMWTYPES_UCHAR readDeviceIdCode;
  TMWTYPES_UCHAR objectId;
  TMWTYPES_UCHAR length;
  TMWTYPES_UCHAR numberOfObjects;
  TMWTYPES_BOOL  moreToDo;
  TMWTYPES_BOOL  restart;
  TMWTYPES_UCHAR exception;
  SMBSESN *pSMBSession = (SMBSESN *)pSession; 
  TMWSESN_TX_DATA *pTxData = mbchnl_newTxData(pSession->pChannel, pSession, 256);

  if(pTxData == TMWDEFS_NULL)
    return(TMWDEFS_FALSE);
  
  /* Currently only MEI type 14 Device Identification is supported */
  if(pRequest->pMsgBuf[1] != 14)
  { 
    _sendResponseFailure(pRequest, pTxData, MBDEFS_MBE_ILLEGAL_FUNCTION);
    return(TMWDEFS_FALSE);
  }
  
  /* validate received length */
  if(pRequest->msgLength < 4) 
  {
    _sendResponseFailure(pRequest, pTxData, MBDEFS_MBE_ILLEGAL_DATA_VALUE);
    return(TMWDEFS_FALSE);
  }
  
  moreToDo = TMWDEFS_TRUE;
  restart = TMWDEFS_TRUE;
  exception = 0;
  readDeviceIdCode = pRequest->pMsgBuf[2];
  objectId = pRequest->pMsgBuf[3];

  /* check conformity level and set object id if necessary */ 
  switch(readDeviceIdCode)
  {
    case 1: /* basic stream */ 
      if(objectId > 2)
      {
        exception = MBDEFS_MBE_ILLEGAL_DATA_ADDRESS;
      }
      break;
    case 2: /* regular stream */
      if((pSMBSession->conformityLevel != 2)
        && (pSMBSession->conformityLevel != 3)
        && (pSMBSession->conformityLevel != 0x82)
        && (pSMBSession->conformityLevel != 0x83))
      {
        exception = MBDEFS_MBE_ILLEGAL_DATA_VALUE;
      }
      if(objectId > 6)
      {
        exception = MBDEFS_MBE_ILLEGAL_DATA_ADDRESS;
      }
      break;
    case 3: /* extended stream */  
      if((pSMBSession->conformityLevel != 3)
        && (pSMBSession->conformityLevel != 0x83))
      {
        exception = MBDEFS_MBE_ILLEGAL_DATA_VALUE;
      }
      break;
    case 4: /* individual access */
      if((pSMBSession->conformityLevel == 0x81) 
        && (objectId < 3))
      {
        /* object id should be the specific item to read */
        moreToDo = TMWDEFS_FALSE;
      }
      else if((pSMBSession->conformityLevel == 0x82) 
        && (objectId < 0x7f))
      {
        /* object id should be the specific item to read */
        moreToDo = TMWDEFS_FALSE;
      }
      else if(pSMBSession->conformityLevel == 0x83) 
      {
        /* object id should be the specific item to read */
        moreToDo = TMWDEFS_FALSE;
      }
      else
        exception = MBDEFS_MBE_ILLEGAL_DATA_VALUE;
      break;
    default: 
        exception = MBDEFS_MBE_ILLEGAL_DATA_VALUE;
      break;
  }

  if(exception)
  {
    _sendResponseFailure(pRequest,pTxData,exception);
    return(TMWDEFS_FALSE);
  }

  numberOfObjects = 1;

  /* Send Good Response */
  pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[0]; /* MB FC */
  pTxData->pMsgBuf[pTxData->msgLength++] = pRequest->pMsgBuf[1]; /* MEI Type */ 
  pTxData->pMsgBuf[pTxData->msgLength++] = readDeviceIdCode;
  pTxData->pMsgBuf[pTxData->msgLength++] = pSMBSession->conformityLevel;
 
  pTxData->pMsgBuf[pTxData->msgLength++] = 0; /* assume no more follows */
  pTxData->pMsgBuf[pTxData->msgLength++] = 0; /* next object id */
  pTxData->pMsgBuf[pTxData->msgLength++] = numberOfObjects; 
 
  while(restart)
  {
    restart = TMWDEFS_FALSE;

    /* How much room is left in message */
    length = (TMWTYPES_UCHAR)pTxData->maxLength - (pTxData->msgLength+2);
    if(smbdata_readDeviceId(pSMBSession->mb.pDbHandle, objectId, &length, &pTxData->pMsgBuf[pTxData->msgLength+2]))
    { 
      pTxData->pMsgBuf[pTxData->msgLength++] = objectId; /* Object Id */
      pTxData->pMsgBuf[pTxData->msgLength++] = length;   /* Object Length */ 
           
      MBDIAG_SHOW_DEVICEID(pSession, objectId, length, &pTxData->pMsgBuf[pTxData->msgLength]);

      /* data was copied by smbdata_readDeviceId */
      pTxData->msgLength += length;

      while(moreToDo)
      { 
        if((readDeviceIdCode == 1)
          && (objectId >1))
        {
          /* stop after basic */
          moreToDo = TMWDEFS_FALSE;
        } 
        else if(smbdata_getNextDeviceId(pSMBSession->mb.pDbHandle, objectId, &objectId))  
        {
          /* How much room is left in message */
          length = (TMWTYPES_UCHAR)pTxData->maxLength - (pTxData->msgLength+2);
          if(smbdata_readDeviceId(pSMBSession->mb.pDbHandle, objectId, &length, &pTxData->pMsgBuf[pTxData->msgLength+2]))
          { 
            pTxData->pMsgBuf[pTxData->msgLength++] = objectId; /* Object Id */
            pTxData->pMsgBuf[pTxData->msgLength++] = length;   /* Object Length */ 

            MBDIAG_SHOW_DEVICEID(pSession, objectId, length, &pTxData->pMsgBuf[pTxData->msgLength]);

            /* data was copied by smbdata_readDeviceId */
            pTxData->msgLength += length;
            
            pTxData->pMsgBuf[6] = ++numberOfObjects;
          }
          else
          {  
            /* Did not fit in response */
            pTxData->pMsgBuf[4] = 0xff;     /* more follows */
            pTxData->pMsgBuf[5] = objectId; /* next Object Id */
            moreToDo = TMWDEFS_FALSE;
          }  
        }
        else
        { 
          moreToDo = TMWDEFS_FALSE;
        }
      }
    }
    else
    {
      /* if ReadDevId is individual access, return 02 illegal data address */
      if(readDeviceIdCode == 4)
      { 
        pTxData->msgLength = 0;
        _sendResponseFailure(pRequest,pTxData,MBDEFS_MBE_ILLEGAL_DATA_ADDRESS);
        return(TMWDEFS_TRUE);
      }   

      /* if object id does not match any known object the server responds as if object id 0 is read (restart at the beginning) */
      objectId = 0;
      restart = TMWDEFS_TRUE;
    }
  }
  
  MBDIAG_SHOW_DEVICEIDDATA(pSession, pTxData->pMsgBuf[4], pTxData->pMsgBuf[5], pSMBSession->conformityLevel);
  _sendResponse(pTxData);

  return(TMWDEFS_TRUE);
}
#endif

/* Global Functions */

/* function: smbsesn_initConfig */
void TMWDEFS_GLOBAL smbsesn_initConfig(
  SMBSESN_CONFIG *pConfig)
{

  /* Set default Server address */
  /* For Modbus TCP this is now called Unit Identifier and should default to 0ff or 0 */
  pConfig->serverAddress = 1;

  /* Set Session Active */
  pConfig->active = TMWDEFS_TRUE;

  pConfig->maxDI_Read         = MBDEFS_SMB_MAX_DI_READ;
  pConfig->maxCoil_Read       = MBDEFS_SMB_MAX_COIL_READ;
  pConfig->maxCoil_Write      = MBDEFS_SMB_MAX_COIL_WRITE;
  pConfig->maxIReg_Read       = MBDEFS_SMB_MAX_IREG_READ;
  pConfig->maxHReg_Read       = MBDEFS_SMB_MAX_HREG_READ;
  pConfig->maxHreg_Write      = MBDEFS_SMB_MAX_HREG_WRITE;
  pConfig->maxHReg_Read_FC23  = MBDEFS_SMB_MAX_HREG_READ_FC23;
  pConfig->maxHreg_Write_FC23 = MBDEFS_SMB_MAX_HREG_WRITE_FC23;
  pConfig->sesnDiagMask       = TMWDIAG_ID_DEF_MASK;
  
  /*FC 43 MEI Type 15 Read Device Idenfication Conformity Level */
  pConfig->conformityLevel    = MBDEFS_DEVID_REGULARSTREAMIND;

  /* User provided statistics callback function */
  pConfig->pStatCallback = TMWDEFS_NULL;
  pConfig->pStatCallbackParam = TMWDEFS_NULL;
}

/* function: smbsesn_openSession */
TMWSESN * TMWDEFS_GLOBAL smbsesn_openSession(
  TMWCHNL *pChannel,
  const SMBSESN_CONFIG *pConfig,
  void *pUserHandle)
{
  TMWSESN *pSession;
  SMBSESN *pSMBSession;

  /* a session requires a channel */
  if (pChannel == TMWDEFS_NULL)
  {
    return TMWDEFS_NULL;
  }

  /* Initialize memory management if not yet done */
  if(!tmwappl_getInitialized(TMWAPPL_INIT_SMB))
  {
    if(!smbmem_init(TMWDEFS_NULL))
      return(TMWDEFS_NULL);

    tmwappl_setInitialized(TMWAPPL_INIT_SMB);
  }

  /* Allocate space for session context */
  pSMBSession =  (SMBSESN *)smbmem_alloc(SMBMEM_SMBSESN_TYPE);

  if(pSMBSession == TMWDEFS_NULL)
  {
    return(TMWDEFS_NULL);
  }

  pSMBSession->mb.pProcessFrameFunc = smbsesn_processFrame;

  /* Configuration */
  pSession = (TMWSESN *)pSMBSession;
  pSession->active = pConfig->active;
  pSession->destAddress = pConfig->serverAddress;
  pSession->sesnDiagMask = pConfig->sesnDiagMask;

  /* Server configuration */
  pSMBSession->maxDI_Read = pConfig->maxDI_Read;
  pSMBSession->maxCoil_Read = pConfig->maxCoil_Read;
  pSMBSession->maxCoil_Write = pConfig->maxCoil_Write;
  pSMBSession->maxIReg_Read = pConfig->maxIReg_Read;
  pSMBSession->maxHReg_Read = pConfig->maxHReg_Read;
  pSMBSession->maxHreg_Write = pConfig->maxHreg_Write;
  pSMBSession->maxHReg_Read_FC23 = pConfig->maxHReg_Read_FC23;
  pSMBSession->maxHreg_Write_FC23 = pConfig->maxHreg_Write_FC23;
  pSMBSession->conformityLevel = pConfig->conformityLevel;

  pSMBSession->listenOnlyMode = TMWDEFS_FALSE;

  /* Lock channel */
  TMWTARG_LOCK_SECTION(&pChannel->lock);

  /* Initialize Server database */
  pSMBSession->mb.pDbHandle = smbdata_init(pSession, pUserHandle);
  if(pSMBSession->mb.pDbHandle == TMWDEFS_NULL)
  {
    /* Log error */
    smbmem_free(pSMBSession);

    /* Unlock channel */
    TMWTARG_UNLOCK_SECTION(&pChannel->lock);
    return(TMWDEFS_NULL);
  }

  /* Initialize generic Modbus session */
  mbsesn_openSession(pChannel, pSession, pConfig->pStatCallback,
    pConfig->pStatCallbackParam, TMWTYPES_PROTOCOL_MB, TMWTYPES_SESSION_TYPE_SERVER);

  /* Unlock channel */
  TMWTARG_UNLOCK_SECTION(&pChannel->lock);
  return(pSession);
}

/* function: smbsesn_getSessionConfig */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsesn_getSessionConfig(
  TMWSESN *pSession,
  SMBSESN_CONFIG *pConfig)
{
  SMBSESN *pSMBSession = (SMBSESN*)pSession;
  
  pConfig->active              = pSession->active;
  pConfig->serverAddress       = pSession->destAddress;
  pConfig->sesnDiagMask        = pSession->sesnDiagMask;
  pConfig->pStatCallback       = pSession->pStatCallbackFunc;
  pConfig->pStatCallbackParam  = pSession->pStatCallbackParam;

  pConfig->maxDI_Read          = pSMBSession->maxDI_Read;
  pConfig->maxCoil_Read        = pSMBSession->maxCoil_Read;
  pConfig->maxCoil_Write       = pSMBSession->maxCoil_Write;
  pConfig->maxIReg_Read        = pSMBSession->maxIReg_Read;
  pConfig->maxHReg_Read        = pSMBSession->maxHReg_Read;
  pConfig->maxHreg_Write       = pSMBSession->maxHreg_Write;
  pConfig->maxHReg_Read_FC23   = pSMBSession->maxHReg_Read_FC23;
  pConfig->maxHreg_Write_FC23  = pSMBSession->maxHreg_Write_FC23;

  pConfig->conformityLevel     = pSMBSession->conformityLevel;

  return(TMWDEFS_TRUE);
}

/* function: smbsesn_setSessionConfig */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsesn_setSessionConfig(
  TMWSESN *pSession,
  const SMBSESN_CONFIG *pConfig)
{
  SMBSESN *pSMBSession = (SMBSESN*)pSession;
  
  pSession->active                = pConfig->active;
  pSession->destAddress           = pConfig->serverAddress;
  pSession->sesnDiagMask          = pConfig->sesnDiagMask;
  pSession->pStatCallbackFunc     = pConfig->pStatCallback;
  pSession->pStatCallbackParam    = pConfig->pStatCallbackParam;

  pSMBSession->maxDI_Read         = pConfig->maxDI_Read;
  pSMBSession->maxCoil_Read       = pConfig->maxCoil_Read;
  pSMBSession->maxCoil_Write      = pConfig->maxCoil_Write;
  pSMBSession->maxIReg_Read       = pConfig->maxIReg_Read;
  pSMBSession->maxHReg_Read       = pConfig->maxHReg_Read;
  pSMBSession->maxHreg_Write      = pConfig->maxHreg_Write;
  pSMBSession->maxHReg_Read_FC23  = pConfig->maxHReg_Read_FC23;
  pSMBSession->maxHreg_Write_FC23 = pConfig->maxHreg_Write_FC23;

  pSMBSession->conformityLevel    = pConfig->conformityLevel;

  return(TMWDEFS_TRUE);
}

/* function: smbsesn_modifySession */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsesn_modifySession(
  TMWSESN *pSession,
  const SMBSESN_CONFIG *pConfig,
  TMWTYPES_ULONG configMask)
{
  if((configMask & SMBSESN_CONFIG_SERVER) != 0)
  {
    pSession->destAddress = pConfig->serverAddress;
  }

  if((configMask & SMBSESN_CONFIG_ACTIVE) != 0)
  {
    pSession->active = pConfig->active;
  }

  return(TMWDEFS_TRUE);
}

/* function: smbsesn_closeSession */
void TMWDEFS_GLOBAL smbsesn_closeSession(
  TMWSESN *pSession)
{
  TMWCHNL *pChannel;
  SMBSESN *pSMBSession = (SMBSESN *)pSession;

  /* Check for NULL since this would be a common error */
  if(pSession == TMWDEFS_NULL)
  {
    return;
  }

  pChannel = (TMWCHNL *)pSession->pChannel;

  /* Lock channel */
  TMWTARG_LOCK_SECTION(&pChannel->lock);

  /* Close generic Modbus session */
  mbsesn_closeSession(pSession);

  smbdata_close(pSMBSession->mb.pDbHandle);

  /* Free memory */
  smbmem_free(pSMBSession);

  /* Unlock channel */
  TMWTARG_UNLOCK_SECTION(&pChannel->lock);
}

static TMWTYPES_BOOL TMWDEFS_LOCAL _validateRcvdLength(TMWSESN_RX_DATA *pRxFrame)
{
  TMWTYPES_USHORT msgLength = pRxFrame->msgLength;

  if(msgLength < 1)
    return TMWDEFS_FALSE;

  switch(pRxFrame->pMsgBuf[0])
    {
#if SMBDATA_SUPPORT_FC_DIAGNOSTICS
    /* diagnostics will be further checked when processed */
    case MBDEFS_FC_DIAGNOSTICS:
      if(msgLength < 3)
        return TMWDEFS_FALSE; 
      break;
#endif

    case MBDEFS_FC_READ_COILS:
    case MBDEFS_FC_READ_DISCRETE_INPUTS:
    case MBDEFS_FC_READ_HOLDING_REGISTERS:
    case MBDEFS_FC_READ_READ_INPUT_REGISTERS:
    case MBDEFS_FC_WRITE_SINGLE_COIL:
    case MBDEFS_FC_WRITE_SINGLE_REGISTER:
      if(msgLength < 5)
        return TMWDEFS_FALSE;
      break;

    case MBDEFS_FC_WRITE_MULTIPLE_COILS:
    case MBDEFS_FC_WRITE_MULTIPLE_HOLDING_REGISTERS:
      if(msgLength < 6)
        return TMWDEFS_FALSE;
      break;
      
#if SMBDATA_SUPPORT_FC_MW_REGISTER
    case MBDEFS_FC_MASK_WRITE_REGISTER:
      if(msgLength < 7)
        return TMWDEFS_FALSE;
      break;
#endif
       
#if SMBDATA_SUPPORT_FC_RW_MREGISTERS
    case MBDEFS_FC_READ_WRITE_MULTIPLE_REGISTERS:
      if(msgLength < 10)
        return TMWDEFS_FALSE;
      break;
#endif
      
#if SMBDATA_SUPPORT_FC_READ_DEV_ID
    case MBDEFS_FC_43_ENCAPSULATED:
      if(msgLength < 2)
        return TMWDEFS_FALSE;
      break;
#endif
      
    /* MBDEFS_FC_READ_EXCEPTION_STATUS has a length of 1 
     * so it was checked at the start of this function
     */
    default:
      return TMWDEFS_TRUE;
    } 
   
  return TMWDEFS_TRUE;
}

/* function: smbsesn_processFrame */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsesn_processFrame(
  TMWSESN *pSession,
  TMWSESN_RX_DATA *pRxFrame)
{
  SMBSESN *pSMBSession = (SMBSESN *)pSession;
  TMWTYPES_BOOL rtnVal = TMWDEFS_FALSE;

  /* if there is not even a function code then just return */
  if(pRxFrame->msgLength == 0)
  {
    mbchnl_discardInvLen(pSession);
    return(TMWDEFS_FALSE);
  }

  /* If length is set to MBLINK_INVALID_LENGTH, or if the length rcvd is too short
   * for the function code) then return illegal data value Exception Code. 
   * Special code in mbtlink sets length to MBLINK_INVALID_LENGTH 
   * Reply with Exception Response (FC & 0x80) and Exception Code           
   */
  if((pRxFrame->msgLength == MBLINK_INVALID_LENGTH)
     || (!_validateRcvdLength(pRxFrame)))
  {
    mbchnl_discardInvLen(pSession);
    if(!pRxFrame->isBroadcast && !pSMBSession->listenOnlyMode)
    {
      TMWSESN_TX_DATA *pTxData;
      pTxData = mbchnl_newTxData(pSession->pChannel, pSession, 256);
      _sendResponseFailure(pRxFrame, pTxData, MBDEFS_MBE_ILLEGAL_DATA_VALUE);
    }
    return(TMWDEFS_FALSE);
  }

  /* When in listen only mode, don't process and don't respond to any request 
   * other than Diagnostic sub-function 1
   */
  if((pSMBSession->listenOnlyMode)
    &&((pRxFrame->pMsgBuf[0] != MBDEFS_FC_DIAGNOSTICS) 
    ||(MBDEFS_MAKEWORD(pRxFrame->pMsgBuf[1], pRxFrame->pMsgBuf[2]) != 1)))
  {
    TMWSESN_STAT_CALLBACK_FUNC(pRxFrame->pSession,
      TMWSESN_STAT_NO_RESPONSE, TMWDEFS_NULL);

    return(TMWDEFS_TRUE);
  }

  
#ifdef TMW_SUPPORT_MONITOR
  /* If in analyzer or monitor only mode, don't process function code */
  if(pSession->pChannel->pPhysContext->monitorMode)
  {
    return(TMWDEFS_TRUE);
  }
#endif

  /* Switch based on Function Code */
  /* Only writes are allowed for broadcast requests */
  if(pRxFrame->isBroadcast)
  {
    switch(pRxFrame->pMsgBuf[0])
    {
#if SMBDATA_SUPPORT_FC_W_COIL
    case MBDEFS_FC_WRITE_SINGLE_COIL:
      rtnVal = _processWriteSingleCoil(pSession, pRxFrame);
      break;
#endif
#if SMBDATA_SUPPORT_FC_W_REGISTER
    case MBDEFS_FC_WRITE_SINGLE_REGISTER:
      rtnVal = _processWriteSingleRegister(pSession, pRxFrame);
      break;
#endif  
#if SMBDATA_SUPPORT_FC_W_MCOILS
    case MBDEFS_FC_WRITE_MULTIPLE_COILS:
      rtnVal = _processWriteMultipleCoils(pSession, pRxFrame);
      break;
#endif
#if SMBDATA_SUPPORT_FC_W_MHREGISTERS
    case MBDEFS_FC_WRITE_MULTIPLE_HOLDING_REGISTERS:
      rtnVal = _processWriteMultipleHoldingRegisters(pSession, pRxFrame);
      break;
#endif
#if SMBDATA_SUPPORT_FC_MW_REGISTER
    case MBDEFS_FC_MASK_WRITE_REGISTER:
      rtnVal = _processMaskWriteRegister(pSession, pRxFrame);
      break;
#endif
    default:
      break;
      /* FC not supported for broadcast */
    }
  }
  else /* Not broadcast */
  {
    switch(pRxFrame->pMsgBuf[0])
    {
#if SMBDATA_SUPPORT_FC_R_COILS
    case MBDEFS_FC_READ_COILS:
      rtnVal = _processReadCoils(pSession, pRxFrame);
      break;
#endif
#if SMBDATA_SUPPORT_FC_R_D_INPUTS
    case MBDEFS_FC_READ_DISCRETE_INPUTS:
      rtnVal = _processReadDiscreteInputs(pSession, pRxFrame);
      break;
#endif
#if SMBDATA_SUPPORT_FC_R_H_REGISTERS
    case MBDEFS_FC_READ_HOLDING_REGISTERS:
      rtnVal = _processReadHoldingRegisters(pSession, pRxFrame);
      break;
#endif
#if SMBDATA_SUPPORT_FC_R_I_REGISTERS
    case MBDEFS_FC_READ_READ_INPUT_REGISTERS:
      rtnVal = _processReadInputRegisters(pSession, pRxFrame);
      break;
#endif
#if SMBDATA_SUPPORT_FC_W_COIL
    case MBDEFS_FC_WRITE_SINGLE_COIL:
      rtnVal = _processWriteSingleCoil(pSession, pRxFrame);
      break;
#endif
#if SMBDATA_SUPPORT_FC_W_REGISTER
    case MBDEFS_FC_WRITE_SINGLE_REGISTER:
      rtnVal = _processWriteSingleRegister(pSession, pRxFrame);
      break;
#endif
#if SMBDATA_SUPPORT_FC_R_EXCEPTION
    case MBDEFS_FC_READ_EXCEPTION_STATUS:
      rtnVal = _processReadExceptionStatus(pSession, pRxFrame);
      break;
#endif
#if SMBDATA_SUPPORT_FC_DIAGNOSTICS
    case MBDEFS_FC_DIAGNOSTICS:
      rtnVal = _processDiagnostics(pSession, pRxFrame);
      break;
#endif
#if SMBDATA_SUPPORT_FC_W_MCOILS
    case MBDEFS_FC_WRITE_MULTIPLE_COILS:
      rtnVal = _processWriteMultipleCoils(pSession, pRxFrame);
      break;
#endif
#if SMBDATA_SUPPORT_FC_MW_REGISTER
    case MBDEFS_FC_MASK_WRITE_REGISTER:
      rtnVal = _processMaskWriteRegister(pSession, pRxFrame);
      break;
#endif

#if SMBDATA_SUPPORT_FC_W_MHREGISTERS
    case MBDEFS_FC_WRITE_MULTIPLE_HOLDING_REGISTERS:
      rtnVal = _processWriteMultipleHoldingRegisters(pSession, pRxFrame);
      break;
#endif
#if SMBDATA_SUPPORT_FC_RW_MREGISTERS
    case MBDEFS_FC_READ_WRITE_MULTIPLE_REGISTERS:
      rtnVal = _processReadWriteMultipleRegisters(pSession, pRxFrame);
      break;
#endif
#if SMBDATA_SUPPORT_FC_READ_DEV_ID
    case MBDEFS_FC_43_ENCAPSULATED:
      /* Currently only MEI type 14 Device Identification is supported */
      rtnVal = _processReadDeviceId(pSession, pRxFrame); 
      break;
#endif 
    default:
      {
      TMWSESN_TX_DATA *pTxData;

      /* If FC is not supported then return Illegal Function Exception Code */
      /* Reply with Exception Response (FC & 0x80) and Exception Code)      */
      pTxData = mbchnl_newTxData(pSession->pChannel, pSession, 256);
      _sendResponseFailure(pRxFrame,pTxData,MBDEFS_MBE_ILLEGAL_FUNCTION);
      return(rtnVal);
      } 
    } 
  }
#if SMBDATA_SUPPORT_ANY
  return(rtnVal);
#endif
}


