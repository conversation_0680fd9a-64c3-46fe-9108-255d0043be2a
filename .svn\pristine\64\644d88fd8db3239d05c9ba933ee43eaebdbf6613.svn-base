#!/usr/bin/env python3
"""
Test script for the delete_codebase functionality
"""

import requests
import json

# Configuration
CODE_ANALYZER_SERVER_URL = "http://home-ai-server:5002"

def test_delete_codebase_api():
    """Test the delete codebase API endpoint"""
    print("🧪 Testing delete_codebase API endpoint...")
    
    # First, list available codebases
    print("\n📚 Listing available codebases:")
    try:
        response = requests.post(f"{CODE_ANALYZER_SERVER_URL}/tools/list_codebases", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ Available codebases:")
            print(data.get("result", "No result"))
        else:
            print(f"❌ Failed to list codebases: HTTP {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Error listing codebases: {e}")
        return
    
    # Ask user which codebase to delete (for safety)
    codebase_name = input("\n🗑️ Enter the name of the codebase to delete (or 'cancel' to abort): ").strip()
    
    if codebase_name.lower() == 'cancel' or not codebase_name:
        print("❌ Delete operation cancelled")
        return
    
    # Confirm deletion
    confirm = input(f"⚠️ Are you sure you want to delete '{codebase_name}'? This will remove all indexed data. (yes/no): ").strip().lower()
    
    if confirm != 'yes':
        print("❌ Delete operation cancelled")
        return
    
    # Perform deletion
    print(f"\n🗑️ Deleting codebase: {codebase_name}")
    try:
        payload = {"codebase_name": codebase_name}
        response = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/delete_codebase",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            result = data.get("result", "No result")
            print("✅ Delete operation completed:")
            print(result)
        else:
            print(f"❌ Delete failed: HTTP {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Error deleting codebase: {e}")
    
    # List codebases again to confirm deletion
    print("\n📚 Listing codebases after deletion:")
    try:
        response = requests.post(f"{CODE_ANALYZER_SERVER_URL}/tools/list_codebases", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ Current codebases:")
            print(data.get("result", "No result"))
        else:
            print(f"❌ Failed to list codebases: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ Error listing codebases: {e}")

def test_health_check():
    """Test server health before running delete tests"""
    print("🏥 Checking server health...")
    try:
        response = requests.get(f"{CODE_ANALYZER_SERVER_URL}/health", timeout=10)
        if response.status_code == 200:
            health = response.json()
            print("✅ Server is healthy")
            print(f"   Collections: {health.get('chromadb_collections', 'unknown')}")
            return True
        else:
            print(f"❌ Server health check failed: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Code Analysis Server Delete Codebase Test")
    print("=" * 50)
    
    # Check server health first
    if not test_health_check():
        print("❌ Server is not available. Please ensure the Code Analysis server is running.")
        exit(1)
    
    # Run delete test
    test_delete_codebase_api()
    
    print("\n✅ Test completed!")
