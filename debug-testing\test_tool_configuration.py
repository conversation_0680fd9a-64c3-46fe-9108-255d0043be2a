#!/usr/bin/env python3
"""
Test what URL the tool is actually using and fix configuration issues
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_tool_server_urls():
    """Test which server URL the tool is actually trying to use"""
    print("🔍 Testing Tool Server Configuration")
    print("=" * 50)
    
    # Test the working server URL
    print("\n✅ Testing current working server:")
    try:
        response = requests.get("http://home-ai-server:5002/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ http://home-ai-server:5002 - Version: {data.get('version', 'unknown')}")
        else:
            print(f"   ❌ http://home-ai-server:5002 - Status: {response.status_code}")
    except Exception as e:
        print(f"   ❌ http://home-ai-server:5002 - Error: {e}")
    
    # Test the old server URL
    print("\n❌ Testing old server URL:")
    try:
        response = requests.get("http://openwebui-rag-server:5002/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ http://openwebui-rag-server:5002 - Version: {data.get('version', 'unknown')}")
        else:
            print(f"   ❌ http://openwebui-rag-server:5002 - Status: {response.status_code}")
    except Exception as e:
        print(f"   ❌ http://openwebui-rag-server:5002 - Error: {str(e)[:100]}...")

def test_api_with_debug():
    """Test API call and see what error we get"""
    print("\n🔍 Testing API Call with Debug Info")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "status"}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 200
            },
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
            
            print(f"Response Length: {len(content)}")
            print(f"Response Preview: {content[:200]}...")
            
            # Check for server connection errors in the response
            if any(indicator in content.lower() for indicator in [
                "connection", "timeout", "server", "error", "failed", "unreachable"
            ]):
                print("🔍 Response contains connection-related terms - possible server issue")
            
            # Check for tool execution indicators
            if any(indicator in content for indicator in [
                "server status", "version", "health", "codebase", "Documents:"
            ]):
                print("✅ Response contains tool-specific content")
            else:
                print("❌ Response is generic - tool not executing")
                
        else:
            print(f"Request failed: {response.status_code}")
            print(f"Error: {response.text[:200]}...")
            
    except Exception as e:
        print(f"API call error: {e}")

def create_fixed_tool_config():
    """Create a tool configuration that should work"""
    print("\n🔧 Creating Fixed Tool Configuration")
    print("=" * 50)
    
    print("The tool needs to be updated in OpenWebUI with:")
    print("1. Correct server URL: http://home-ai-server:5002")
    print("2. Correct field name: code_analyzer_server_url")
    print("3. Updated tool code with all fixes")
    
    print("\n📋 Steps to fix:")
    print("1. Go to OpenWebUI → Workspace → Tools")
    print("2. Edit the Code Analysis Tool")
    print("3. Replace with the current open_webui_code_analyzer_tool.py content")
    print("4. Ensure the Valves configuration uses:")
    print("   - code_analyzer_server_url: http://home-ai-server:5002")
    print("5. Save and test")

def main():
    """Main testing function"""
    print("🔧 Tool Configuration Diagnosis")
    print("=" * 70)
    
    test_tool_server_urls()
    test_api_with_debug()
    create_fixed_tool_config()
    
    print("\n📊 DIAGNOSIS SUMMARY")
    print("=" * 40)
    print("✅ Working server: http://home-ai-server:5002")
    print("❌ Old server: http://openwebui-rag-server:5002 (doesn't exist)")
    print("🔧 Solution: Update tool in OpenWebUI with correct server URL")
    print("📋 The tool configuration in OpenWebUI needs to be updated!")

if __name__ == "__main__":
    main()
