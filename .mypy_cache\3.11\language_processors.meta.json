{"data_mtime": 1752112769, "dep_lines": [6, 7, 8, 9, 10, 11, 12, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 10, 5, 30, 30], "dependencies": ["re", "os", "pathlib", "typing", "language_framework", "semantic_patterns", "logging", "builtins", "_frozen_importlib", "abc"], "hash": "8bc8be859b6f8f1750411f9bc1895d9c99091a5c", "id": "language_processors", "ignore_all": true, "interface_hash": "7751d9ae8b8c2fd451b06a73c9af126889c35235", "mtime": 1752112478, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\language_processors.py", "plugin_data": null, "size": 26400, "suppressed": [], "version_id": "1.15.0"}