#!/usr/bin/env python3
"""
Test script for the new automatic context format selection feature
"""

import sys
import os

# Add the parent directory to the path to import the tool
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from open_webui_code_analyzer_tool import Tools

def test_format_selection():
    """Test the automatic format selection logic"""
    print("🧪 Testing Automatic Context Format Selection")
    print("=" * 60)
    
    # Initialize the tool
    tools = Tools()
    
    # Test queries that should get detailed format
    detailed_queries = [
        "How does memory management work in the utils codebase?",
        "Explain the error handling approach",
        "Compare the TCP and UDP implementations",
        "Analyze the security implementation",
        "Why does the algorithm use this approach?",
        "What is the architecture of the system?",
        "How are database transactions handled?",
        "Explain the workflow for user authentication"
    ]
    
    # Test queries that should get clean format
    clean_queries = [
        "Find the malloc function",
        "Show me all classes",
        "List database functions", 
        "What is the User class?",
        "Get the connection methods",
        "Locate error constants",
        "What functions are in utils.h?",
        "Is there a logging function?"
    ]
    
    print("\n📋 TESTING DETAILED FORMAT SELECTION:")
    print("-" * 40)
    for query in detailed_queries:
        format_choice = tools._determine_optimal_context_format(query.lower())
        status = "✅" if format_choice == "detailed" else "❌"
        print(f"{status} {format_choice.upper()}: {query}")
    
    print("\n🎯 TESTING CLEAN FORMAT SELECTION:")
    print("-" * 40)
    for query in clean_queries:
        format_choice = tools._determine_optimal_context_format(query.lower())
        status = "✅" if format_choice == "clean" else "❌"
        print(f"{status} {format_choice.upper()}: {query}")
    
    # Test edge cases
    print("\n🔍 TESTING EDGE CASES:")
    print("-" * 40)
    edge_cases = [
        ("", "clean"),  # Empty query
        ("hello", "clean"),  # Non-code query
        ("what is malloc", "clean"),  # Simple definition
        ("how to use malloc", "detailed"),  # Usage explanation
    ]
    
    for query, expected in edge_cases:
        format_choice = tools._determine_optimal_context_format(query.lower())
        status = "✅" if format_choice == expected else "❌"
        print(f"{status} {format_choice.upper()} (expected {expected.upper()}): '{query}'")
    
    print("\n🎉 Format selection testing complete!")

if __name__ == "__main__":
    test_format_selection()
