#!/usr/bin/env python3
"""
Quick test for help intent - single query only
"""

import requests
import json
import time

# Configuration
OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_single_help_query():
    """Test just one help query to see if it works"""
    
    session = requests.Session()
    session.headers.update({
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    })
    
    print("🧪 Quick Help Intent Test")
    print("=" * 30)
    
    query = "help"
    print(f"📤 Testing: '{query}'")
    
    payload = {
        "model": "llama3:latest",
        "messages": [
            {
                "role": "user",
                "content": query
            }
        ],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False,
        "temperature": 0.7,
        "max_tokens": 1000
    }
    
    print("⏳ Sending request...")
    start_time = time.time()
    
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json=payload,
            timeout=90  # Long timeout
        )
        
        elapsed = time.time() - start_time
        print(f"⏱️ Response received in {elapsed:.1f} seconds")
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            # Check if we got help documentation
            if "Enhanced Multi-Language Code Analysis System" in content:
                print("✅ SUCCESS - Help intent detected and routed correctly!")
                print("📄 Got comprehensive help documentation")
            elif len(content) > 1000 and ("automatic" in content.lower() or "optimization" in content.lower()):
                print("✅ SUCCESS - Help intent detected (alternative format)")
                print("📄 Got detailed help content")
            else:
                print("❌ FAILED - Help intent not detected")
                print(f"📄 Response length: {len(content)} chars")
                print(f"📄 Response preview: {content[:300]}...")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except requests.exceptions.Timeout:
        elapsed = time.time() - start_time
        print(f"❌ TIMEOUT after {elapsed:.1f} seconds")
        print("💡 This suggests the help intent is not being detected")
        print("💡 The query is likely falling through to general LLM processing")
    except requests.exceptions.ConnectionError:
        print("❌ CONNECTION ERROR - Cannot reach OpenWebUI server")
        print("💡 Check if the server is running at http://home-ai-server.local:8080")
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ ERROR after {elapsed:.1f} seconds: {e}")

if __name__ == "__main__":
    test_single_help_query()
