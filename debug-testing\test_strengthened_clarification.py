#!/usr/bin/env python3
"""
Test the strengthened chunk clarification fix
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_strengthened_clarification():
    """Test that the strengthened clarification works consistently"""
    print("🧪 Testing Strengthened Chunk Clarification")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test the same queries that had issues before
    test_queries = [
        ("show details about the utils code base", "Should now have clarification"),
        ("what are the metrics for utils?", "Should now have clarification"),
        ("get statistics for utils codebase", "Should have clarification"),
        ("show me utils codebase stats", "Should maintain clarification"),
    ]
    
    results = []
    
    for query, expectation in test_queries:
        print(f"\n🔍 Testing: '{query}'")
        print(f"Expected: {expectation}")
        print("-" * 50)
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": f"select codebase utils\n{query}"}],
                    "tool_ids": ["code_analyzer_tool"],
                    "stream": False,
                    "max_tokens": 1200
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                print(f"✅ Response received ({len(content)} chars)")
                
                # Check for the strengthened clarification
                strong_clarification_indicators = [
                    "text segments created by splitting source files",
                    "vector search and embedding",
                    "chunk count (479) represents text segments",
                    "not the number of actual files",
                    "divided into multiple chunks",
                    "search granularity"
                ]
                
                found_strong = [ind for ind in strong_clarification_indicators if ind.lower() in content.lower()]
                
                # Check for any clarification
                any_clarification = [
                    "chunks are text segments",
                    "splitting source files", 
                    "vector search",
                    "one file may contain multiple chunks",
                    "text segments",
                    "search granularity"
                ]
                
                found_any = [ind for ind in any_clarification if ind.lower() in content.lower()]
                
                # Check for problematic interpretations
                problematic = [
                    "chunks.*indicates.*files",
                    "chunks.*number of.*files",
                    "479.*files"
                ]
                
                import re
                issues = []
                for phrase in problematic:
                    if re.search(phrase, content, re.IGNORECASE):
                        issues.append(phrase)
                
                # Evaluate result
                if found_strong:
                    print(f"🎉 STRONG CLARIFICATION: {found_strong[:2]}")
                    status = "excellent"
                elif found_any:
                    print(f"✅ SOME CLARIFICATION: {found_any[:2]}")
                    status = "good"
                else:
                    print("❌ NO CLARIFICATION FOUND")
                    status = "poor"
                
                if issues:
                    print(f"⚠️ Still has issues: {issues}")
                    if status == "excellent":
                        status = "good"
                    elif status == "good":
                        status = "fair"
                else:
                    print("✅ No problematic interpretations")
                
                results.append({
                    "query": query,
                    "status": status,
                    "has_clarification": len(found_any) > 0,
                    "has_strong_clarification": len(found_strong) > 0,
                    "has_issues": len(issues) > 0
                })
                
                # Show key lines
                lines = content.split('\n')
                chunk_lines = [line for line in lines if 'chunk' in line.lower()]
                if chunk_lines:
                    print(f"Key lines:")
                    for line in chunk_lines[:2]:
                        print(f"  → {line.strip()}")
                
            else:
                print(f"❌ HTTP {response.status_code}")
                results.append({"query": query, "status": "error"})
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append({"query": query, "status": "error"})
    
    return results

def analyze_results(results):
    """Analyze the test results"""
    print(f"\n📊 DETAILED ANALYSIS")
    print("=" * 50)
    
    total = len([r for r in results if r.get("status") != "error"])
    excellent = len([r for r in results if r.get("status") == "excellent"])
    good = len([r for r in results if r.get("status") == "good"])
    fair = len([r for r in results if r.get("status") == "fair"])
    poor = len([r for r in results if r.get("status") == "poor"])
    
    has_clarification = len([r for r in results if r.get("has_clarification")])
    has_strong = len([r for r in results if r.get("has_strong_clarification")])
    has_issues = len([r for r in results if r.get("has_issues")])
    
    print(f"📈 Quality Distribution:")
    print(f"  🎉 Excellent: {excellent}/{total}")
    print(f"  ✅ Good: {good}/{total}")
    print(f"  ⚠️ Fair: {fair}/{total}")
    print(f"  ❌ Poor: {poor}/{total}")
    
    print(f"\n📋 Clarification Analysis:")
    print(f"  ✅ Has any clarification: {has_clarification}/{total}")
    print(f"  🎯 Has strong clarification: {has_strong}/{total}")
    print(f"  ⚠️ Still has issues: {has_issues}/{total}")
    
    # Overall assessment
    success_rate = (excellent + good) / total * 100 if total > 0 else 0
    clarification_rate = has_clarification / total * 100 if total > 0 else 0
    
    print(f"\n🎯 OVERALL ASSESSMENT:")
    print(f"Success rate: {success_rate:.1f}%")
    print(f"Clarification rate: {clarification_rate:.1f}%")
    
    if success_rate >= 75 and clarification_rate >= 75:
        print("🎉 EXCELLENT: Fix is working very well!")
        print("✅ Consistent chunk clarification")
        print("✅ Reduced problematic interpretations")
    elif success_rate >= 50 and clarification_rate >= 50:
        print("👍 GOOD: Significant improvement")
        print("✅ Most responses have clarification")
        print("⚠️ Some inconsistency remains")
    else:
        print("⚠️ NEEDS WORK: Fix needs strengthening")
        print("🔧 Consider additional improvements")

def main():
    """Main test function"""
    print("🔧 Strengthened Chunk Clarification Test")
    print("=" * 70)
    print("Testing improved consistency of chunk vs files clarification")
    
    # Test the strengthened fix
    results = test_strengthened_clarification()
    
    # Analyze results
    analyze_results(results)
    
    print(f"\n🎯 Next Steps:")
    print("1. Update tool in OpenWebUI if not done yet")
    print("2. Monitor user interactions for remaining issues")
    print("3. Consider additional clarification strategies if needed")

if __name__ == "__main__":
    main()
