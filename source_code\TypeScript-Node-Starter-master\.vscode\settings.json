// Place your settings in this file to overwrite default and user settings.
{
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "eslint.validate": ["javascript", "typescript"],
  "editor.formatOnSave": true,
  "[javascript]": {
    "editor.formatOnSave": false
  },
  "[typescript]": {
    "editor.formatOnSave": false
  },
  "[markdown]": {
    "editor.formatOnSave": false
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/bower_components": true,
    "**/dist": true,
    "**/coverage": true
  },
  "typescript.referencesCodeLens.enabled": true,
  "appService.zipIgnorePattern": [".vscode{,/**}"],
  "appService.deploySubpath": ""
}
