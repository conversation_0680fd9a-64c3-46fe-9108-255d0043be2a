#!/usr/bin/env python3
"""
Test stats vs statistics keyword routing
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_keyword_variations():
    """Test different keyword variations for stats"""
    print("🧪 Testing Stats vs Statistics Keyword Routing")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test different keyword variations
    test_queries = [
        ("get stats for utils", "Should trigger stats routing"),
        ("get statistics for utils", "Should trigger statistics routing"),
        ("show stats utils", "Alternative stats format"),
        ("show statistics utils", "Alternative statistics format"),
        ("utils stats", "Short stats format"),
        ("utils statistics", "Short statistics format"),
        ("codebase stats utils", "Different word order"),
        ("codebase statistics utils", "Different word order"),
    ]
    
    results = []
    
    for query, description in test_queries:
        print(f"\n🔍 Testing: '{query}'")
        print(f"Expected: {description}")
        print("-" * 50)
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": query}],
                    "tool_ids": ["code_analyzer_tool"],
                    "stream": False,
                    "max_tokens": 1000
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                print(f"✅ Response received ({len(content)} chars)")
                
                # Check for real codebase data
                real_indicators = [
                    "Total chunks: 479",
                    "Unique files: 43",
                    "Basic Codebase Statistics",
                    "Languages:",
                    "C: 460",
                    "Last updated: 2025-06-27"
                ]
                
                # Check for generic/fake data
                generic_indicators = [
                    "_add_chunk_clarification",
                    "Number of runs",
                    "Average run time",
                    "Memory usage",
                    "Total Number of Calls"
                ]
                
                # Check for LLM fallback responses
                fallback_indicators = [
                    "I'll need to know which type",
                    "Here are some options:",
                    "GitHub statistics",
                    "blast from the past",
                    "emulator of the Zilog Z80"
                ]
                
                found_real = [ind for ind in real_indicators if ind in content]
                found_generic = [ind for ind in generic_indicators if ind in content]
                found_fallback = [ind for ind in fallback_indicators if ind in content]
                
                if found_real:
                    print(f"🎉 REAL CODEBASE DATA: {found_real[:2]}")
                    status = "real_data"
                elif found_generic:
                    print(f"❌ GENERIC TOOL DATA: {found_generic[:2]}")
                    status = "generic_data"
                elif found_fallback:
                    print(f"⚠️ LLM FALLBACK: {found_fallback[:2]}")
                    status = "llm_fallback"
                else:
                    print("❓ UNKNOWN RESPONSE TYPE")
                    status = "unknown"
                
                results.append({
                    "query": query,
                    "keyword": "stats" if "stats" in query and "statistics" not in query else "statistics" if "statistics" in query else "both",
                    "status": status,
                    "length": len(content)
                })
                
                # Show preview
                preview = content[:200] + "..." if len(content) > 200 else content
                print(f"Preview: {preview}")
                
            else:
                print(f"❌ HTTP {response.status_code}")
                results.append({"query": query, "status": "error"})
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append({"query": query, "status": "error"})
    
    return results

def analyze_keyword_results(results):
    """Analyze the keyword routing results"""
    print(f"\n📊 KEYWORD ROUTING ANALYSIS")
    print("=" * 60)
    
    valid_results = [r for r in results if r.get("status") != "error"]
    
    # Group by keyword
    stats_results = [r for r in valid_results if r.get("keyword") == "stats"]
    statistics_results = [r for r in valid_results if r.get("keyword") == "statistics"]
    
    # Count success types
    real_data_count = len([r for r in valid_results if r.get("status") == "real_data"])
    generic_count = len([r for r in valid_results if r.get("status") == "generic_data"])
    fallback_count = len([r for r in valid_results if r.get("status") == "llm_fallback"])
    
    print(f"📈 Overall Results:")
    print(f"  🎉 Real codebase data: {real_data_count}/{len(valid_results)}")
    print(f"  ❌ Generic tool data: {generic_count}/{len(valid_results)}")
    print(f"  ⚠️ LLM fallback: {fallback_count}/{len(valid_results)}")
    
    print(f"\n📋 Keyword Comparison:")
    print(f"  'stats' queries: {len(stats_results)}")
    print(f"  'statistics' queries: {len(statistics_results)}")
    
    # Check if there's a pattern
    stats_success = len([r for r in stats_results if r.get("status") == "real_data"])
    statistics_success = len([r for r in statistics_results if r.get("status") == "real_data"])
    
    print(f"  'stats' success rate: {stats_success}/{len(stats_results)} ({stats_success/len(stats_results)*100:.1f}%)" if stats_results else "  'stats' success rate: N/A")
    print(f"  'statistics' success rate: {statistics_success}/{len(statistics_results)} ({statistics_success/len(statistics_results)*100:.1f}%)" if statistics_results else "  'statistics' success rate: N/A")
    
    # Detailed breakdown
    print(f"\n📋 Query-by-Query Results:")
    for result in valid_results:
        query = result.get("query", "")[:30] + "..." if len(result.get("query", "")) > 30 else result.get("query", "")
        keyword = result.get("keyword", "unknown")
        status = result.get("status", "unknown")
        status_emoji = {"real_data": "🎉", "generic_data": "❌", "llm_fallback": "⚠️", "unknown": "❓"}.get(status, "❓")
        print(f"  {status_emoji} {query} ({keyword})")

def main():
    """Main test function"""
    print("🔧 Stats vs Statistics Keyword Test")
    print("=" * 70)
    print("Testing if keyword choice affects routing to real codebase data")
    
    # Test keyword variations
    results = test_keyword_variations()
    
    # Analyze results
    analyze_keyword_results(results)
    
    print(f"\n🎯 Hypothesis:")
    print("If routing is working correctly, both 'stats' and 'statistics' should")
    print("return real codebase data, not generic tool data or LLM fallbacks.")

if __name__ == "__main__":
    main()
