/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2023 */
/*****************************************************************************/
/*                                                                           */
/* This file is the property of:                                             */
/*                                                                           */
/*                       Triangle MicroWorks, Inc.                           */
/*                      Raleigh, North Carolina USA                          */
/*                       www.TriangleMicroWorks.com                          */
/*                          (919) 870-6615                                   */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*                                                                           */
/*****************************************************************************/

/* file: smbsesn.h
 * description: Defines structures and functions to support Server Modbus 
 *  sessions.
 */
#ifndef SMBSESN_DEFINED
#define SMBSESN_DEFINED

#include "tmwscl/utils/tmwdefs.h"
#include "tmwscl/utils/tmwchnl.h"

/* Server Modbus Session Configuration Info */
typedef struct SMBSessionConfigStruct {

  /* Source address for this session 
   * (applicable to MB ASCII and MB RTU) 
   * For Modbus TCP this is now called Unit Identifier and is typically 0xff (or 0)
   * unless used by bridges for intra-system routing. For TCP, if this is 0xff
   * a request to any address will be accepted and the received UnitIdentifier
   * will be echoed in the response.
   */
  TMWTYPES_USHORT serverAddress;

  /* Is this session 'active'. Inactive sessions will not transmit
   * frames or process received frames.
   */
  TMWTYPES_BOOL active;

  /* Note that for Modbus Plus, only 1 session is allowed
   * per channel (client or server channel), and that the 
   * route path is at the channel level (i.e. no Server Address)                             
   */

  /* The following variables may be set to limit the number of elements allowed to read
   * or write.  These are set to the maximum allowed by the 
   * protocol (see mbdefs.h) at initialization, but may be changed prior to opening a
   * session in order to limit the amount of data exchanged for particular data types and
   * function codes.  The protocol for example is capable of transfering 2000 Coils in one
   * transaction, but this may present performance problems for legacy or process bound 
   * target sytems.  Older Modicon PLCs for example, may limit coils read/written to 800.
   * In the case that these are limited, user documentation should clearly specify this.
  */
  TMWTYPES_USHORT maxDI_Read;          /* Max num Discrete Inputs allowed to be read      */
  TMWTYPES_USHORT maxCoil_Read;        /* Max num Coils allowed to be read                */
  TMWTYPES_USHORT maxCoil_Write;       /* Max num Coils allowed to be written             */
  TMWTYPES_USHORT maxIReg_Read;        /* Max num Input registers allowed to be read      */
  TMWTYPES_USHORT maxHReg_Read;        /* Max num Holding Registers allowed to be read    */
  TMWTYPES_USHORT maxHreg_Write;       /* Max num Holding Registers allowed to be written */
  
  /* following only apply to MB Function Code 23 
   */
  TMWTYPES_USHORT maxHReg_Read_FC23;   /* Max num Holding Registers allowed to be read    */
  TMWTYPES_USHORT maxHreg_Write_FC23;  /* Max num Holding Registers allowed to be written */

  /* For FC 43 MEI Type 15 Read Device Identification  
    Identification conformity level of the device and type of supported access
    0x01: basic identification (stream access only)
    0x02: regular identification (stream access only)
    0x03: extended identification (stream access only)
    0x81: basic identification (stream access and individual access)
    0x82: regular identification (stream access and individual access)
    0x83: extended identification(stream access and individual access)
  */
  TMWTYPES_UCHAR conformityLevel;

  /* User registered statistics callback function and parameter */
  TMWSESN_STAT_CALLBACK pStatCallback;
  void *pStatCallbackParam;

  /* Diagnostic mask */
  TMWTYPES_ULONG sesnDiagMask;

} SMBSESN_CONFIG;

/* Define bit masks used to specify which configuration parameters
 * should be modified in a call to smbsesn_modifySession.
 */
#define SMBSESN_CONFIG_SERVER           0x00000001L
#define SMBSESN_CONFIG_ACTIVE           0x00000002L

/* Deprecated, use SMBSESN_CONFIG_SERVER */
#define SMBSESN_CONFIG_SLAVE 0x00000001L /*deprecated*/

/* Include Server Modbus 'private' structures and functions */
#include "tmwscl/modbus/smbsesp.h"

#ifdef __cplusplus
extern "C" {
#endif

  /* function: smbsesn_initConfig 
   * purpose: Initialize modbus Server session configuration data structure.
   *  This routine should be called to initialize all the members of the
   *  data structure. Then the user should modify the data members they
   *  need in user code. Then pass the resulting structure to 
   *  smbsesn_openSession.
   * arguments:
   *  pConfig - pointer to configuration data structure to initialize
   * returns:
   *  void
   */
  TMWDEFS_SCL_API void TMWDEFS_GLOBAL smbsesn_initConfig(
    SMBSESN_CONFIG *pConfig);

  /* function: smbsesn_openSession  
   * purpose: Open a modbus Server session
   * arguments:
   *  pChannel - channel to open session on
   *  pConfig - modbus Server configuration data structure
   * returns:
   *  Pointer to new session or TMWDEFS_NULL.
   */
  TMWDEFS_SCL_API TMWSESN * TMWDEFS_GLOBAL smbsesn_openSession(
    TMWCHNL *pChannel,
    const SMBSESN_CONFIG *pConfig, 
    void *pUserHandle);
  
  /* function: smbsesn_getSessionConfig  
   * purpose:  Get current configuration from an open session
   * arguments:
   *  pSession - session to get configuration from
   *  pConfig - modbus Server configuration data structure to be filled in
   * returns:
   *  TMWDEFS_TRUE if successful
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsesn_getSessionConfig(
    TMWSESN *pSession,
    SMBSESN_CONFIG *pConfig);

  /* function: smbsesn_setSessionConfig 
   * purpose: Modify a currently open session
   *  NOTE: normally smbsesn_getSessionConfig() will be called
   *   to get the current config, some values will be changed 
   *   and this function will be called to set the values.
   * arguments:
   *  pSession - session to modify
   *  pConfig - modbus Server configuration data structure
   * returns:
   *  TMWDEFS_TRUE if successful
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsesn_setSessionConfig(
    TMWSESN *pSession,
    const SMBSESN_CONFIG *pConfig);

  /* function: smbsesn_modifySession  
   *  DEPRECATED FUNCTION, SHOULD USE sdnpsesn_setSessionConfig()
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL smbsesn_modifySession(
    TMWSESN *pSession,
    const SMBSESN_CONFIG *pConfig, 
    TMWTYPES_ULONG configMask);

  /* function: smbsesn_closeSession  
   * purpose: Close a currently open session
   * arguments:
   *  pSession - session to close
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API void TMWDEFS_GLOBAL smbsesn_closeSession(
    TMWSESN *pSession);

#ifdef __cplusplus
}
#endif
#endif /* SMBSESN_DEFINED */
