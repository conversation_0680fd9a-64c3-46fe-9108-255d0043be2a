/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2023 */
/*****************************************************************************/
/*                                                                           */
/* This file is the property of:                                             */
/*                                                                           */
/*                       Triangle MicroWorks, Inc.                           */
/*                      Raleigh, North Carolina USA                          */
/*                       www.TriangleMicroWorks.com                          */
/*                          (919) 870-6615                                   */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*                                                                           */
/*****************************************************************************/

/* file: tmwtree.h
 * description: Definition of a red-black balanced binary tree
 */

#ifndef TMWTREE_DEFINED
#define TMWTREE_DEFINED

#include "tmwscl/utils/tmwcnfg.h"
#include "tmwscl/utils/tmwtypes.h"
#include "tmwscl/utils/tmwdefs.h"
/*#include"tmwstk.h" */

typedef struct _tmwtreeNode {
  TMWTYPES_ULONG key;
  void* info;
  TMWTYPES_BOOL red; /* if red=0 then the node is black */
  struct _tmwtreeNode* m_pLeftNode;
  struct _tmwtreeNode* m_pRightNode;
  struct _tmwtreeNode* m_pParentNode;
} TMWTREE_NODE;


#ifdef __cplusplus
extern "C" {
#endif

/* Compare(a,b) should return 1 if *a > *b, -1 if *a < *b, and 0 otherwise */
/* Destroy(a) takes a pointer to whatever key might be and frees it accordingly */
typedef struct _tmwTree {
  void (*DestroyInfo)(void* a);
 
  /*  A sentinel is used for root and for nil.  These sentinels are */
  /*  created when tmwtree_create is caled.  root->left should always */
  /*  point to the node which is the root of the tree.  nil points to a */
  /*  node which should always be black but has aribtrary children and */
  /*  parent and no key or info.  The point of using these sentinels is so */
  /*  that the root and nil nodes do not require special cases in the code */
  TMWTREE_NODE *m_pTreeNodeRoot;             
  TMWTREE_NODE *m_pTreeNodeNil;     


  /* Index of previous item returned, useful for repeated tmwsim_tableFindPointByIndex calls */
  TMWTYPES_ULONG currentIndex;

  /* Pointer to previous node returned */
  TMWTREE_NODE *pCurrentNode;

  /* Number of items in the tree, does not include Nil or root */
  TMWTYPES_USHORT quantity;

  TMWDEFS_RESOURCE_LOCK lock;
} TMWTREE_TREE;


/***********************************************************************/
/*  function :  tmwtree_create */
/**/
/*  INPUTS:  */
/**/
/*  OUTPUT:  This function returns a pointer to the newly created */
/*  red - black tree. */
/**/
/*  Modifies Input: none */
/***********************************************************************/

TMWTREE_TREE * TMWDEFS_GLOBAL tmwtree_create(
  void (*DestroyInfo)(void* a));

/***********************************************************************/
/*  function :  tmwtree_insert */
/**/
/*  INPUTS:  tree is the red - black tree to insert a node which has a key */
/*           pointed to by key and info pointed to by info.  */
/**/
/*  OUTPUT:  This function returns a pointer to the newly inserted node */
/*           which is guarunteed to be valid until this node is deleted. */
/*           What this means is if another data structure stores this */
/*           pointer then the tree does not need to be searched when this */
/*           is to be deleted. */
/**/
/*  Modifies Input: tree */
/**/
/*  EFFECTS:  Creates a node node which contains the appropriate key and */
/*            info pointers and inserts it into the tree. */
/*  Before calling Insert RBTree the node x should have its key set */
/***********************************************************************/
TMWTREE_NODE * TMWDEFS_GLOBAL tmwtree_insert(TMWTREE_TREE *pTree, TMWTYPES_ULONG key, void* info);

TMWTREE_NODE * TMWDEFS_GLOBAL tmwtree_getFirst(TMWTREE_TREE *pTree);

TMWTREE_NODE * TMWDEFS_GLOBAL tmwtree_getLast(TMWTREE_TREE *pTree);

/***********************************************************************/
/*  function :  tmwtree_successor  */
/**/
/*    INPUTS:  tree is the tree in question, and x is the node we want the */
/*             the successor of. */
/**/
/*    OUTPUT:  This function returns the successor of x or NULL if no */
/*             successor exists. */
/**/
/*    Modifies Input: none */
/**/
/*    Note:  uses the algorithm in _Introduction_To_Algorithms_ */
/***********************************************************************/
TMWTREE_NODE * TMWDEFS_GLOBAL tmwtree_successor(TMWTREE_TREE *pTree,TMWTREE_NODE *pTreeNode);
/***********************************************************************/
/*  function :  tmwtree_predecessor  */
/**/
/*    INPUTS:  tree is the tree in question, and x is the node we want the */
/*             the predecessor of. */
/**/
/*    OUTPUT:  This function returns the predecessor of x or NULL if no */
/*             predecessor exists. */
/**/
/*    Modifies Input: none */
/**/
/*    Note:  uses the algorithm in _Introduction_To_Algorithms_ */
/***********************************************************************/
TMWTREE_NODE * TMWDEFS_GLOBAL tmwtree_predecessor(TMWTREE_TREE *pTree,TMWTREE_NODE *pTreeNode);
/***********************************************************************/
/*  function :  tmwtree_destroy */
/**/
/*    INPUTS:  tree is the tree to destroy */
/**/
/*    OUTPUT:  none */
/**/
/*    EFFECT:  Destroys the key and frees memory */
/**/
/*    Modifies Input: tree */
/**/
/***********************************************************************/
void TMWDEFS_GLOBAL tmwtree_destroy(TMWTREE_TREE *pTree);
/***********************************************************************/
/*  function :  tmwtree_print */
/**/
/*    INPUTS:  tree is the tree to print */
/**/
/*    OUTPUT:  none */
/**/
/*    EFFECT:  This function recursively prints the nodes of the tree */
/*             inorder using the PrintKey and PrintInfo functions. */
/**/
/*    Modifies Input: none */
/**/
/***********************************************************************/
void TMWDEFS_GLOBAL tmwtree_print(TMWTREE_TREE *pTree);
/***********************************************************************/
/*  function :  tmwtree_exactQuery */
/**/
/*    INPUTS:  tree is the tree to Query and q is a pointer to the key */
/*             we are searching for */
/**/
/*    OUTPUT:  returns the a node with key equal to q.  If there are */
/*             multiple nodes with key equal to q this function returns */
/*             the one highest in the tree */
/**/
/*    Modifies Input: none */
/**/
/***********************************************************************/
TMWTREE_NODE * TMWDEFS_GLOBAL tmwtree_exactQuery(TMWTREE_TREE *pTree, TMWTYPES_ULONG q);
/***********************************************************************/
/*  function :  tmwtree_delete */
/**/
/*    INPUTS:  tree is the tree to delete node z from */
/**/
/*    OUTPUT:  none */
/**/
/*    EFFECT:  Deletes z from tree and frees the key and info of z */
/*             using DestoryKey and DestoryInfo.  Then calls */
/*             _deleteFixUp to restore red - black properties */
/**/
/*    Modifies Input: tree, z */
/**/
/*    The algorithm from this function is from _Introduction_To_Algorithms_ */
/***********************************************************************/
void TMWDEFS_GLOBAL tmwtree_delete(TMWTREE_TREE *pTree , TMWTREE_NODE *pTreeNode );

void NullFunction(void*);

#ifdef __cplusplus
}
#endif
#endif /* TMWTREE_DEFINED */