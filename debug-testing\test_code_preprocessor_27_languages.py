#!/usr/bin/env python3
"""
Comprehensive test to verify that code_preprocessor.py properly handles all 27 supported languages.
Tests language detection, parser assignment, and semantic pattern availability.
"""

import tempfile
import shutil
from pathlib import Path
from code_preprocessor import MultiLanguageCodeProcessor

def test_all_27_languages():
    """Test that code_preprocessor.py handles all 27 languages correctly"""
    
    # All 27 supported languages with their file extensions and sample code
    language_tests = [
        # Core languages (10)
        ('c', '.c', 'int main() { malloc(100); return 0; }'),
        ('cpp', '.cpp', 'class Test { public: Test() { new int[10]; } };'),
        ('python', '.py', 'def hello(): import os; print("hello")'),
        ('csharp', '.cs', 'public class Test { public async Task Method() {} }'),
        ('javascript', '.js', 'async function test() { const x = await fetch(); }'),
        ('typescript', '.ts', 'interface User { name: string; } async function test() {}'),
        ('rust', '.rs', 'fn main() { unsafe { let ptr = std::ptr::null(); } }'),
        ('java', '.java', 'public class Test { @Override public void method() {} }'),
        ('go', '.go', 'func main() { ch := make(chan int); go routine() }'),
        ('sql', '.sql', 'SELECT * FROM users JOIN orders WHERE id = 1'),
        
        # Additional languages (17)
        ('tcl', '.tcl', 'proc test_proc {} { string match pattern text }'),
        ('verilog', '.v', 'module test(input clk); always @(posedge clk) begin end endmodule'),
        ('bash', '.sh', 'function test() { if [ -f file ]; then grep pattern file; fi }'),
        ('commonlisp', '.lisp', '(defun test () (lambda (x) x)) (defmacro test-macro () `(list))'),
        ('elisp', '.el', '(defun test () (current-buffer)) (add-hook \'test-hook #\'test-function)'),
        ('scheme', '.scm', '(define test (lambda (x) x)) (call/cc (lambda (k) (k 42)))'),
        ('lua', '.lua', 'function test() local t = {}; setmetatable(t, {}); coroutine.yield() end'),
        ('make', 'Makefile', 'target: dependency\n\tcommand\n.PHONY: clean\nifdef DEBUG\nendif'),
        ('json', '.json', '{"object": {"array": [1, 2, 3]}, "schema": {"type": "object"}}'),
        ('yaml', '.yaml', 'mapping: value\nsequence:\n  - item1\nanchor: &ref\nalias: *ref'),
        ('xml', '.xml', '<?xml version="1.0"?><root xmlns:ns="uri"><element>text</element></root>'),
        ('php', '.php', '<?php class Test extends Base { $_GET[\'param\']; } ?>'),
        ('perl', '.pl', 'sub test { my $var = shift; $var =~ s/pattern/replacement/g; }'),
        ('markdown', '.md', '# Header 1\n[link](url) ![image](src) `code`'),
        ('html', '.html', '<html><body><form><input><button>Submit</button></form></body></html>'),
        ('fortran', '.f90', 'program test\n  use module_name\n  real, dimension(100) :: array\nend program'),
        ('vhdl', '.vhd', 'entity test is port(clk: in std_logic); architecture rtl of test is begin end rtl;'),
    ]
    
    # Create temporary directory and files
    temp_dir = tempfile.mkdtemp()
    test_files = {}
    
    try:
        # Create test files
        for lang, ext, code in language_tests:
            if ext == 'Makefile':
                filepath = Path(temp_dir) / 'Makefile'
            else:
                filepath = Path(temp_dir) / f'test{ext}'
            filepath.write_text(code)
            test_files[lang] = filepath
        
        # Initialize processor
        processor = MultiLanguageCodeProcessor(temp_dir)
        
        print("🧪 Testing code_preprocessor.py with all 27 languages...")
        print("=" * 80)
        
        # Test results
        results = {
            'language_detection': 0,
            'parser_assignment': 0,
            'semantic_patterns': 0,
            'total_languages': len(language_tests)
        }
        
        for lang, ext, code in language_tests:
            filepath = test_files[lang]
            
            # Test 1: Language detection
            parser, language, detected_lang = processor.get_parser_for_file(filepath)
            
            if detected_lang == lang:
                results['language_detection'] += 1
                lang_status = "✅"
            else:
                lang_status = "❌"
                print(f"  ❌ {lang.upper()}: Expected {lang}, got {detected_lang}")
            
            # Test 2: Parser assignment
            if parser is not None:
                results['parser_assignment'] += 1
                parser_status = "✅"
            else:
                parser_status = "⚠️"
            
            # Test 3: Semantic patterns availability
            if lang in processor.semantic_patterns:
                results['semantic_patterns'] += 1
                patterns_count = len(processor.semantic_patterns[lang])
                semantic_status = f"✅({patterns_count})"
            else:
                semantic_status = "❌"
            
            print(f"  {lang_status} {lang.upper():12} | Parser: {parser_status} | Patterns: {semantic_status}")
        
        print("\n" + "=" * 80)
        print("📊 SUMMARY RESULTS:")
        print(f"  Language Detection: {results['language_detection']}/{results['total_languages']} ({'✅' if results['language_detection'] == results['total_languages'] else '❌'})")
        print(f"  Parser Assignment:  {results['parser_assignment']}/{results['total_languages']} ({'✅' if results['parser_assignment'] >= results['total_languages'] - 3 else '❌'})")  # Allow some parsers to be missing
        print(f"  Semantic Patterns:  {results['semantic_patterns']}/{results['total_languages']} ({'✅' if results['semantic_patterns'] == results['total_languages'] else '❌'})")
        
        # Overall assessment
        if (results['language_detection'] == results['total_languages'] and 
            results['semantic_patterns'] == results['total_languages']):
            print("\n🎉 code_preprocessor.py FULLY SUPPORTS ALL 27 LANGUAGES!")
            return True
        else:
            print("\n⚠️  code_preprocessor.py needs attention for some languages")
            return False
            
    finally:
        # Cleanup
        shutil.rmtree(temp_dir)

def test_semantic_patterns_completeness():
    """Test that semantic patterns are comprehensive for all languages"""
    print("\n🔍 Testing semantic patterns completeness...")
    
    processor = MultiLanguageCodeProcessor(".")
    patterns = processor.semantic_patterns
    
    expected_languages = [
        'c', 'cpp', 'python', 'csharp', 'javascript', 'typescript', 'rust', 'java', 'go', 'sql',
        'tcl', 'verilog', 'bash', 'commonlisp', 'elisp', 'scheme', 'lua', 'make', 'json', 'yaml',
        'xml', 'php', 'perl', 'markdown', 'html', 'fortran', 'vhdl'
    ]
    
    missing_patterns = []
    for lang in expected_languages:
        if lang not in patterns:
            missing_patterns.append(lang)
        else:
            categories = len(patterns[lang])
            total_keywords = sum(len(keywords) for keywords in patterns[lang].values())
            print(f"  ✅ {lang.upper():12}: {categories} categories, {total_keywords} keywords")
    
    if missing_patterns:
        print(f"\n❌ Missing semantic patterns for: {missing_patterns}")
        return False
    else:
        print(f"\n✅ All {len(expected_languages)} languages have semantic patterns!")
        return True

if __name__ == "__main__":
    print("🚀 Comprehensive Code Preprocessor Test (27 Languages)")
    print("=" * 80)
    
    success1 = test_all_27_languages()
    success2 = test_semantic_patterns_completeness()
    
    if success1 and success2:
        print("\n🎉 ALL TESTS PASSED! code_preprocessor.py fully supports 27 languages!")
    else:
        print("\n⚠️  Some tests failed. Please review the results above.")
