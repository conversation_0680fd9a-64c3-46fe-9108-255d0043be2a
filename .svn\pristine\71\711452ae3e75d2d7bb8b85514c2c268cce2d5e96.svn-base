"""
title: Enhanced Multi-Language Code Analysis RAG
author: Your Name
author_url: https://github.com/yourusername
description: Multi-codebase RAG system for intelligent code analysis with preprocessing, vector search, and codebase management capabilities
version: 2.1.0
license: MIT
requirements: requests
"""

import requests
from typing import Optional
from pydantic import BaseModel, Field


class Tools:
    """
    Enhanced OpenWebUI Tools for C/C++ Code Analysis with Multi-Codebase Support
    """
    
    class Valves(BaseModel):
        rag_server_url: str = Field(
            default="http://openwebui-rag-server:5002",
            description="URL of the RAG server"
        )
        request_timeout: int = Field(
            default=60,
            description="Request timeout in seconds"
        )
        max_results: int = Field(
            default=10,
            description="Maximum number of search results"
        )
    
    def __init__(self):
        self.valves = self.Valves()
        self.citation = False  # We'll handle citations manually
        
    # --- Codebase Management Tools ---
    
    async def list_codebases(
        self,
        __event_emitter__=None
    ) -> str:
        """
        📚 List all available codebases for analysis.
        
        Shows all codebases found in the source code directory, along with their
        indexing status, document counts, and last update times.
        """
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": "Retrieving available codebases...", "done": False}
            })
        
        try:
            response = requests.post(
                f"{self.valves.rag_server_url}/tools/list_codebases",
                headers={"Content-Type": "application/json"},
                timeout=self.valves.request_timeout
            )
            
            response.raise_for_status()
            data = response.json()
            result = data.get("result", "Unable to retrieve codebase list")
            
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": "Codebases retrieved successfully", "done": True}
                })
            
            return result
            
        except Exception as e:
            error_msg = f"❌ Failed to list codebases: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Error: {str(e)}", "done": True}
                })
            return error_msg
    
    async def select_codebase(
        self,
        codebase_name: str,
        __event_emitter__=None
    ) -> str:
        """
        🎯 Select a specific codebase for subsequent analysis operations.
        
        After selecting a codebase, all search and analysis tools will operate
        on that codebase until a different one is selected.
        
        :param codebase_name: Name of the codebase to select for analysis
        """
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": f"Selecting codebase: {codebase_name}", "done": False}
            })
        
        try:
            payload = {"codebase_name": codebase_name.strip()}
            
            response = requests.post(
                f"{self.valves.rag_server_url}/tools/select_codebase",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=self.valves.request_timeout
            )
            
            response.raise_for_status()
            data = response.json()
            result = data.get("result", "Unable to select codebase")
            
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Codebase {codebase_name} selected", "done": True}
                })
            
            return result
            
        except Exception as e:
            error_msg = f"❌ Failed to select codebase: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Selection failed: {str(e)}", "done": True}
                })
            return error_msg
    
    async def process_codebase(
        self,
        codebase_name: str,
        exclude_dirs: Optional[str] = None,
        __event_emitter__=None
    ) -> str:
        """
        ⚙️ Process a codebase: analyze source code and create searchable database.
        
        This tool performs the complete pipeline:
        1. Code Preprocessing: Parse C/C++ files using tree-sitter
        2. Chunk Extraction: Extract functions, classes, methods, structs, etc.
        3. Vector Database Creation: Generate embeddings and index in ChromaDB
        
        :param codebase_name: Name of the codebase to process
        :param exclude_dirs: Comma-separated list of directories to exclude (e.g., "build,test,docs")
        """
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": f"Starting processing of {codebase_name}...", "done": False}
            })
        
        try:
            # Parse exclude_dirs string into list
            exclude_list = None
            if exclude_dirs:
                exclude_list = [d.strip() for d in exclude_dirs.split(",") if d.strip()]
            
            payload = {
                "codebase_name": codebase_name.strip(),
                "exclude_dirs": exclude_list
            }
            
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": "Preprocessing source code...", "done": False}
                })
            
            response = requests.post(
                f"{self.valves.rag_server_url}/tools/process_codebase",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=600  # 10 minutes for processing
            )
            
            response.raise_for_status()
            data = response.json()
            result = data.get("result", "Processing completed but no result returned")
            
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": "Processing completed successfully", "done": True}
                })
            
            return result
            
        except Exception as e:
            error_msg = f"❌ Failed to process codebase: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Processing failed: {str(e)}", "done": True}
                })
            return error_msg
    
    # --- Enhanced Code Analysis Tools ---
    
    async def search_code(
        self,
        query: str,
        codebase_name: str,
        n_results: int = 5,
        filter_type: Optional[str] = None,
        filter_language: Optional[str] = None,
        filter_file: Optional[str] = None,
        __event_emitter__=None
    ) -> str:
        """
        🔍 Search through a specific codebase for relevant code snippets.
        
        This tool performs semantic search across the indexed codebase to find
        code snippets that match your query, even if they don't contain exact keywords.
        
        :param query: Search query for finding relevant code snippets
        :param codebase_name: Name of the codebase to search
        :param n_results: Number of results to return (1-10)
        :param filter_type: Filter by code type (function, class, method, etc.)
        :param filter_language: Filter by language (c, cpp)
        :param filter_file: Filter by file pattern (e.g., 'tcp', 'socket', 'net')
        """
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": f"Searching {codebase_name} for: {query}", "done": False}
            })
        
        try:
            payload = {
                "query": query.strip(),
                "codebase_name": codebase_name.strip(),
                "n_results": min(max(n_results, 1), self.valves.max_results)
            }
            
            # Add optional filters
            if filter_type:
                payload["filter_type"] = filter_type.strip().lower()
            if filter_language:
                payload["filter_language"] = filter_language.strip().lower()
            if filter_file:
                payload["filter_file"] = filter_file.strip()
                
            response = requests.post(
                f"{self.valves.rag_server_url}/tools/search_code",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=self.valves.request_timeout
            )
            
            response.raise_for_status()
            data = response.json()
            result = data.get("result", "No code search results returned")
            
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": "Search completed", "done": True}
                })
            
            return result
            
        except Exception as e:
            error_msg = f"❌ Code search failed: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Search failed: {str(e)}", "done": True}
                })
            return error_msg
    
    async def ask_about_code(
        self,
        question: str,
        codebase_name: str,
        n_results: int = 5,
        filter_type: Optional[str] = None,
        filter_language: Optional[str] = None,
        filter_file: Optional[str] = None,
        __event_emitter__=None
    ) -> str:
        """
        🤖 Ask detailed questions about a codebase and get AI-generated answers.
        
        This tool uses RAG (Retrieval-Augmented Generation) to provide intelligent
        answers by analyzing relevant code sections and generating comprehensive responses.
        
        :param question: Question about the codebase functionality, design, or implementation
        :param codebase_name: Name of the codebase to analyze
        :param n_results: Number of context chunks to analyze (1-10)
        :param filter_type: Filter by code type
        :param filter_language: Filter by language (c, cpp)
        :param filter_file: Filter by file pattern
        """
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": f"Analyzing {codebase_name} for: {question}", "done": False}
            })
        
        try:
            payload = {
                "question": question.strip(),
                "codebase_name": codebase_name.strip(),
                "n_results": min(max(n_results, 1), self.valves.max_results)
            }
            
            # Add optional filters
            if filter_type:
                payload["filter_type"] = filter_type.strip().lower()
            if filter_language:
                payload["filter_language"] = filter_language.strip().lower()
            if filter_file:
                payload["filter_file"] = filter_file.strip()
            
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": "Generating AI analysis...", "done": False}
                })
                
            response = requests.post(
                f"{self.valves.rag_server_url}/tools/ask_about_code",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=90  # Longer timeout for AI generation
            )
            
            response.raise_for_status()
            data = response.json()
            result = data.get("result", "No answer generated for your question")
            
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": "Analysis completed", "done": True}
                })
            
            return result
            
        except Exception as e:
            error_msg = f"❌ Code analysis failed: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Analysis failed: {str(e)}", "done": True}
                })
            return error_msg
    
    async def get_codebase_stats(
        self,
        codebase_name: str,
        __event_emitter__=None
    ) -> str:
        """
        📊 Get comprehensive statistics about a specific codebase.
        
        Returns detailed metrics about the indexed codebase including:
        • Scale metrics: Total chunks, files, and last update time
        • Code structure breakdown: Distribution of functions, classes, methods, etc.
        • Language analysis: C vs C++ code distribution
        
        :param codebase_name: Name of the codebase to analyze
        """
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": f"Retrieving statistics for {codebase_name}", "done": False}
            })
        
        try:
            payload = {"codebase_name": codebase_name.strip()}
                
            response = requests.post(
                f"{self.valves.rag_server_url}/tools/get_code_stats",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=self.valves.request_timeout
            )
            
            response.raise_for_status()
            data = response.json()
            result = data.get("result", "Unable to retrieve codebase statistics")
            
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": "Statistics retrieved", "done": True}
                })
            
            return result
            
        except Exception as e:
            error_msg = f"❌ Failed to get codebase stats: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Stats retrieval failed: {str(e)}", "done": True}
                })
            return error_msg

    async def delete_codebase(
        self,
        codebase_name: str,
        __event_emitter__=None
    ) -> str:
        """
        🗑️ Delete a specific codebase and its associated data.

        This will permanently remove the indexed data for the specified codebase
        from the vector database. The source code files remain unchanged.

        ⚠️ **Important**: After deletion, you must use process_codebase() before
        you can select_codebase() again, as the ChromaDB collection is removed.

        Args:
            codebase_name: Name of the codebase to delete
        """
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": f"Deleting codebase: {codebase_name}...", "done": False}
            })

        try:
            payload = {"codebase_name": codebase_name}

            response = requests.post(
                f"{self.valves.rag_server_url}/tools/delete_codebase",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=self.valves.request_timeout
            )

            response.raise_for_status()
            data = response.json()
            result = data.get("result", "Unable to delete codebase")

            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Codebase {codebase_name} deleted successfully", "done": True}
                })

            return result

        except Exception as e:
            error_msg = f"❌ Failed to delete codebase: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Deletion failed: {str(e)}", "done": True}
                })
            return error_msg

    # --- Utility Tools ---
    
    async def get_rag_help(
        self,
        __event_emitter__=None
    ) -> str:
        """
        ❓ Get comprehensive help and guidance for using the enhanced RAG system.
        
        Returns detailed information about available tools, workflows, and best practices
        for multi-codebase C/C++ code analysis.
        """
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": "Loading help documentation...", "done": False}
            })
        
        help_text = """
🚀 **Enhanced C/C++ Code Analysis System - Quick Guide**

## 📋 **Basic Workflow**
1. **list_codebases()** - See available projects
2. **select_codebase("name")** - Choose target codebase
3. **get_codebase_stats("name")** - View project metrics
4. **search_code("query", "name")** - Find specific code
5. **ask_about_code("question", "name")** - Get AI explanations

## 🗑️ **After Deleting a Codebase**
If you delete a codebase, you must **process_codebase("name")** before you can **select_codebase("name")** again.

## 🛠️ **Available Tools**

### **Codebase Management**
• **list_codebases()** - View all available codebases and status
• **select_codebase(name)** - Choose codebase for analysis
• **process_codebase(name)** - Index new source code
• **delete_codebase(name)** - Remove indexed data (source files preserved)

### **Code Analysis**
• **search_code(query, codebase)** - Semantic code search
• **ask_about_code(question, codebase)** - AI-powered explanations
• **get_codebase_stats(codebase)** - Project statistics

## 🔍 **Search Tips**
• Use technical terms: "TCP socket", "buffer management"
• Add filters: filter_type="function", filter_language="c"
• Start broad, then narrow with specific terms

## 💡 **Examples**
```
search_code("error handling", "my_project", filter_type="function")
ask_about_code("How is memory managed?", "my_project")
get_codebase_stats("my_project")
```

## 📁 **Supported Code Types**
Functions, Classes, Methods, Structs, Enums, Templates, Namespaces, Headers

Ready to analyze code? Start with **list_codebases()** to see what's available!
"""
        
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": "Help loaded", "done": True}
            })
        
        return help_text
    
    async def check_system_status(
        self,
        __event_emitter__=None
    ) -> str:
        """
        🔧 Check the health and status of the RAG analysis system.
        
        Verifies connectivity and operational status of all system components.
        """
        if __event_emitter__:
            await __event_emitter__({
                "type": "status",
                "data": {"description": "Checking system status...", "done": False}
            })
        
        try:
            response = requests.get(
                f"{self.valves.rag_server_url}/health", 
                timeout=self.valves.request_timeout
            )
            response.raise_for_status()
            
            health_data = response.json()
            
            # Format status report
            status_parts = ["🔧 **RAG System Status**\n"]
            
            components = {
                "rag_tool_server": "Tool Server",
                "rag_service": "RAG Service", 
                "ollama": "Ollama AI",
                "source_code_directory": "Source Directory",
                "chroma_db_directory": "Database Storage"
            }
            
            all_healthy = True
            for key, name in components.items():
                status = health_data.get(key, "unknown")
                if status in ["healthy", "available", "connected"]:
                    status_parts.append(f"✅ **{name}**: Online")
                else:
                    status_parts.append(f"❌ **{name}**: {status}")
                    all_healthy = False
            
            # Add summary info
            codebases = health_data.get("available_codebases", 0)
            current = health_data.get("current_codebase", "None")
            
            status_parts.extend([
                f"\n📚 **Available Codebases**: {codebases}",
                f"🎯 **Current Selection**: {current}",
                f"\n{'✅ System Ready' if all_healthy else '⚠️ Issues Detected'}"
            ])
            
            result = "\n".join(status_parts)
            
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status", 
                    "data": {"description": "Status check completed", "done": True}
                })
            
            return result
            
        except Exception as e:
            error_msg = f"❌ System Status Check Failed: {str(e)}"
            if __event_emitter__:
                await __event_emitter__({
                    "type": "status",
                    "data": {"description": f"Status check failed: {str(e)}", "done": True}
                })
            return error_msg