#!/usr/bin/env python3
"""
Test if the OpenWebUI plugin is properly registered
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_plugin_registration():
    """Test if our plugin is registered with OpenWebUI"""
    print("🔧 Testing Plugin Registration")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test 1: Check if tools endpoint works
    print("\n🧪 Test 1: Check tools endpoint")
    print("-" * 30)
    
    try:
        response = session.get(f"{OPENWEBUI_URL}/api/tools", timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                tools = response.json()
                print(f"✅ Found {len(tools)} tools")
                
                # Look for our tool
                found_our_tool = False
                for tool in tools:
                    tool_id = tool.get('id', '')
                    tool_name = tool.get('name', '')
                    print(f"  - {tool_id}: {tool_name}")
                    
                    if 'code_analyzer' in tool_id.lower() or 'code_analyzer' in tool_name.lower():
                        found_our_tool = True
                        print(f"    🎯 FOUND OUR TOOL!")
                        print(f"       ID: {tool_id}")
                        print(f"       Name: {tool_name}")
                        print(f"       Description: {tool.get('description', 'N/A')[:100]}...")
                
                if not found_our_tool:
                    print("❌ Our tool 'code_analyzer_tools' not found!")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"Raw response: {response.text[:200]}...")
        else:
            print(f"❌ Tools endpoint failed: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Tools endpoint error: {e}")
    
    # Test 2: Try to call a simple function
    print("\n🧪 Test 2: Try simple function call")
    print("-" * 30)
    
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "help"}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 200
            },
            timeout=60
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"Response length: {len(content)}")
            print(f"Preview: {content[:200]}...")
            
            # Check if it looks like our plugin responded
            if any(indicator in content for indicator in ["Code Analysis", "codebase", "🎯", "📚"]):
                print("🎉 Looks like our plugin responded!")
            else:
                print("❌ Generic response - plugin not called")
        else:
            print(f"❌ Function call failed: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Function call error: {e}")
    
    # Test 3: Check models endpoint
    print("\n🧪 Test 3: Check models endpoint")
    print("-" * 30)
    
    try:
        response = session.get(f"{OPENWEBUI_URL}/api/models", timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            models = response.json()
            print(f"✅ Found {len(models)} models")
            
            # Look for models with tools
            for model in models:
                model_id = model.get('id', '')
                if 'llama3' in model_id:
                    print(f"  - {model_id}")
                    if 'tools' in model:
                        print(f"    Tools: {model['tools']}")
        else:
            print(f"❌ Models endpoint failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Models endpoint error: {e}")

if __name__ == "__main__":
    test_plugin_registration()
