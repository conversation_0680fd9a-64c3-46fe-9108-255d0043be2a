#!/usr/bin/env python3
"""
Find the correct tool ID that actually works with the API
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_tool_ids():
    """Test different tool IDs to find the working one"""
    print("🔍 Testing Tool IDs to Find the Working One")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Based on our previous investigation and memories
    tool_ids_to_test = [
        "code_analyzer_tools",      # Current one we're using
        "codebase_analyzer",        # Previous name
        "code_analysis_tool",       # Variation
        "Code Analysis Tool",       # Display name
        "code-analyzer-tools",      # Hyphenated version
        "rag_tools",               # Original name before renaming
        "code_rag_tools",          # Previous name we found in memories
        "openwebui_rag_plugin",    # Another possibility
        "enhanced_rag_tools",      # Variation
    ]
    
    test_query = "select utils codebase"
    
    for tool_id in tool_ids_to_test:
        print(f"\n🧪 Testing tool_id: \"{tool_id}\"")
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": test_query}],
                    "tool_ids": [tool_id],
                    "stream": False,
                    "max_tokens": 300
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                
                # Check for actual tool execution (not generic response)
                success_indicators = [
                    "Selected Enhanced Codebase: utils",
                    "Documents: 479",
                    "ready_enhanced",
                    "Enhanced Statistics",
                    "code_analyzer_server_url",
                    "Status: ready_enhanced"
                ]
                
                if any(indicator in content for indicator in success_indicators):
                    print(f"   ✅ SUCCESS! Tool executed with ID: {tool_id}")
                    print(f"   Response: {content[:150]}...")
                    return tool_id
                else:
                    print(f"   ❌ Generic response with: {tool_id}")
                    print(f"   Preview: {content[:80]}...")
            else:
                print(f"   ❌ Request failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n❌ No working tool ID found!")
    print("This suggests the tool may not be properly registered for API access.")
    return None

def check_web_interface_vs_api():
    """Compare what works in web interface vs API"""
    print("\n🔍 Comparing Web Interface vs API Results")
    print("=" * 50)
    
    print("From our previous testing:")
    print("✅ Web Interface (Working):")
    print("   - 'select utils codebase' → Actual codebase selection")
    print("   - 'list codebases' → Shows actual indexed codebases")
    print("   - Tool is properly installed and functioning")
    
    print("\n❌ API Calls (Not Working):")
    print("   - Same queries return generic responses")
    print("   - Tool is not being executed at all")
    print("   - Suggests tool ID or registration issue")

def suggest_solutions():
    """Suggest solutions based on our findings"""
    print("\n🔧 Suggested Solutions")
    print("=" * 30)
    
    print("1. **Check Tool Registration in OpenWebUI:**")
    print("   - Go to Workspace → Tools")
    print("   - Find the Code Analysis Tool")
    print("   - Note the exact tool ID shown")
    print("   - Ensure it's marked as 'Active'")
    
    print("\n2. **Check Model Assignment:**")
    print("   - Go to Workspace → Models → llama3:latest")
    print("   - Verify the tool is enabled")
    print("   - Check the exact tool name/ID used")
    
    print("\n3. **Re-install Tool if Needed:**")
    print("   - Delete existing tool")
    print("   - Re-install with current code")
    print("   - Ensure proper registration")
    
    print("\n4. **Check for Name Conflicts:**")
    print("   - Multiple tools with similar names")
    print("   - API might be using wrong tool")
    print("   - Clean up duplicate tools")

def main():
    """Main function"""
    print("🔧 Finding Correct Tool ID for API Access")
    print("=" * 70)
    print("The tool works in web interface but not via API.")
    print("This suggests a tool ID or registration issue.\n")
    
    # Test different tool IDs
    working_tool_id = test_tool_ids()
    
    # Compare results
    check_web_interface_vs_api()
    
    # Suggest solutions
    suggest_solutions()
    
    print("\n📊 SUMMARY")
    print("=" * 20)
    if working_tool_id:
        print(f"✅ Found working tool ID: {working_tool_id}")
        print("🎉 API access should now work!")
    else:
        print("❌ No working tool ID found via API")
        print("🔧 Manual investigation needed in OpenWebUI interface")
        print("📋 The tool needs to be properly registered for API access")

if __name__ == "__main__":
    main()
