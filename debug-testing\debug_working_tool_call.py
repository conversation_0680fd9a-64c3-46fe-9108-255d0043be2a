#!/usr/bin/env python3
"""
Debug what happens when the tool IS called successfully
"""

import requests
import json
import time

def test_working_tool_call():
    """Test a query that we know triggers the tool"""
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    model = "llama3:latest"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test query that works (function name)
    query = "tmwmem_alloc"
    
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user", 
                "content": query
            }
        ],
        "tool_ids": ["code_analyzer_tool"],  # Use the correct tool ID
        "stream": False
    }
    
    print(f"🔧 Testing working tool call: '{query}'")
    print(f"🔧 Using tool_ids: ['code_analyzer_tool']")
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=60
        )
        end_time = time.time()
        
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📊 Response Time: {end_time - start_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            
            # Extract the actual content
            choices = result.get("choices", [])
            if choices:
                message = choices[0].get("message", {})
                content = message.get("content", "")
                
                print(f"\n💬 Full Message Content:")
                print("="*100)
                print(content)
                print("="*100)
                
                # Look for all possible chunk indicators
                chunk_indicators = [
                    "chunks found",
                    "chunk found", 
                    "code context retrieved",
                    "context retrieved",
                    "relevant code",
                    "found in",
                    "results found",
                    "matches found"
                ]
                
                found_indicators = []
                for indicator in chunk_indicators:
                    if indicator in content.lower():
                        found_indicators.append(indicator)
                
                print(f"\n🔍 Detailed Analysis:")
                print(f"   Content Length: {len(content)} characters")
                print(f"   Found Chunk Indicators: {found_indicators}")
                
                # Look for numbers that might be chunk counts
                import re
                number_patterns = [
                    r'(\d+)\s+chunks?\s+found',
                    r'(\d+)\s+results?\s+found',
                    r'(\d+)\s+matches?\s+found',
                    r'(\d+)\s+relevant',
                    r'found\s+(\d+)',
                    r'retrieved\s+(\d+)',
                    r'(\d+)\s+code\s+sections?',
                    r'(\d+)\s+contexts?'
                ]
                
                all_numbers = []
                for pattern in number_patterns:
                    matches = re.findall(pattern, content.lower())
                    if matches:
                        all_numbers.extend(matches)
                
                print(f"   Potential Chunk Numbers: {all_numbers}")
                
                # Check for specific success patterns from GUI
                gui_patterns = [
                    "code context retrieved successfully",
                    "=== relevant code context ===",
                    "=== code metadata ===",
                    "context 1:",
                    "context 2:",
                    "file:",
                    "language:",
                    "type:",
                    "domains:",
                    "quality:",
                    "complexity:"
                ]
                
                found_gui_patterns = []
                for pattern in gui_patterns:
                    if pattern in content.lower():
                        found_gui_patterns.append(pattern)
                
                print(f"   GUI-style Patterns: {found_gui_patterns}")
                
                # Check for actual code content
                code_indicators = [
                    "tmwmem",
                    "void *",
                    "malloc",
                    "alloc",
                    "function",
                    "struct",
                    "#include",
                    "typedef"
                ]
                
                found_code = []
                for indicator in code_indicators:
                    if indicator in content.lower():
                        found_code.append(indicator)
                
                print(f"   Code Content Indicators: {found_code}")
                
                # Overall assessment
                tool_definitely_called = len(found_indicators) > 0 or len(found_gui_patterns) > 0
                has_actual_code = len(found_code) > 2
                
                print(f"\n🎯 Assessment:")
                print(f"   Tool Definitely Called: {tool_definitely_called}")
                print(f"   Has Actual Code Content: {has_actual_code}")
                print(f"   Likely Chunk Count: {all_numbers[0] if all_numbers else 'Not detected'}")
                
            else:
                print("❌ No choices in response")
        else:
            print(f"❌ Error Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

def test_natural_language_with_tool_id():
    """Test a natural language query with explicit tool_ids"""
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    model = "llama3:latest"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test natural language query that worked in GUI
    query = "How is the TMW library structured?"
    
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user", 
                "content": query
            }
        ],
        "tool_ids": ["code_analyzer_tool"],  # Force tool usage
        "stream": False
    }
    
    print(f"\n\n🔧 Testing natural language with tool_ids: '{query}'")
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=60
        )
        end_time = time.time()
        
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📊 Response Time: {end_time - start_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            
            # Extract the actual content
            choices = result.get("choices", [])
            if choices:
                message = choices[0].get("message", {})
                content = message.get("content", "")
                
                print(f"\n💬 Message Content (first 500 chars):")
                print("="*80)
                print(content[:500])
                print("="*80)
                
                # Quick check for tool usage
                tool_used = any(indicator in content.lower() for indicator in [
                    "code context retrieved", "chunks found", "=== relevant code context ===",
                    "tmwmem", "utils codebase", "context 1:"
                ])
                
                generic_response = any(indicator in content.lower() for indicator in [
                    "tmw.js", "javascript", "node.js", "directory structure", "lib/"
                ])
                
                print(f"\n🔍 Quick Analysis:")
                print(f"   Tool Used: {tool_used}")
                print(f"   Generic Response: {generic_response}")
                print(f"   Contains TMW C code: {'tmwmem' in content.lower() and 'void' in content.lower()}")
                
            else:
                print("❌ No choices in response")
        else:
            print(f"❌ Error Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    print("🚀 DEBUGGING WORKING TOOL CALLS")
    print("="*100)
    
    # Test function name that we know works
    test_working_tool_call()
    
    # Test natural language with explicit tool_ids
    test_natural_language_with_tool_id()
    
    print(f"\n{'='*100}")
    print("🎯 DEBUG COMPLETE")
    print("="*100)
