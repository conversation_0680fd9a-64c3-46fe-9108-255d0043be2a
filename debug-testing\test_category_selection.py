#!/usr/bin/env python3
"""
Test the category selection functionality
"""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the functions from the auto tester
from openwebui_auto_tester import get_all_test_categories, get_test_prompts, preview_category_prompts

def test_category_functions():
    """Test the category selection functions"""
    print("🧪 Testing Category Selection Functions")
    print("=" * 50)
    
    # Test 1: Get all categories
    print("\n1. Testing get_all_test_categories():")
    all_categories = get_all_test_categories()
    print(f"   ✅ Found {len(all_categories)} categories:")
    for category, tests in all_categories.items():
        print(f"      • {category}: {len(tests)} tests")
    
    # Test 2: Get all prompts
    print("\n2. Testing get_test_prompts() with no filter:")
    all_prompts = get_test_prompts()
    print(f"   ✅ Total prompts: {len(all_prompts)}")
    
    # Test 3: Get specific categories
    print("\n3. Testing get_test_prompts() with specific categories:")
    selected_categories = ["System Status", "Management"]
    filtered_prompts = get_test_prompts(selected_categories)
    print(f"   ✅ Filtered prompts for {selected_categories}: {len(filtered_prompts)}")
    
    # Verify the filtering worked
    for prompt in filtered_prompts:
        if prompt["category"] not in selected_categories:
            print(f"   ❌ ERROR: Found prompt from wrong category: {prompt['category']}")
            return False
    
    print("   ✅ All prompts are from correct categories")
    
    # Test 4: Preview function
    print("\n4. Testing preview_category_prompts():")
    preview_category_prompts("Quick Test", all_categories["Quick Test"])
    
    print("\n✅ All category selection functions working correctly!")
    return True

def show_category_breakdown():
    """Show detailed breakdown of all categories"""
    print("\n📊 Detailed Category Breakdown")
    print("=" * 60)
    
    all_categories = get_all_test_categories()
    total_tests = 0
    
    for i, (category, tests) in enumerate(all_categories.items(), 1):
        print(f"\n{i:2d}. {category} ({len(tests)} tests)")
        print("-" * 40)
        
        for j, test in enumerate(tests, 1):
            print(f"    {j:2d}. {test['description']}")
            # Show first 60 chars of prompt
            prompt_preview = test['prompt'][:60] + "..." if len(test['prompt']) > 60 else test['prompt']
            print(f"        💬 \"{prompt_preview}\"")
        
        total_tests += len(tests)
    
    print(f"\n📈 Summary: {len(all_categories)} categories, {total_tests} total tests")

def main():
    """Main test function"""
    print("🔍 Category Selection Test Suite")
    print("=" * 50)
    
    print("Choose test mode:")
    print("1. Test category functions")
    print("2. Show detailed category breakdown")
    print("3. Both")
    
    try:
        choice = input("\nEnter choice (1-3): ").strip()
        
        if choice == "1":
            test_category_functions()
        elif choice == "2":
            show_category_breakdown()
        elif choice == "3":
            test_category_functions()
            show_category_breakdown()
        else:
            print("Invalid choice. Running function tests...")
            test_category_functions()
            
    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")

if __name__ == "__main__":
    main()
