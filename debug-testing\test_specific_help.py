#!/usr/bin/env python3
"""
Test the updated help intent detection with specific commands
"""

import sys
import os
import asyncio

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from open_webui_code_analyzer_tool import Tools

class MockValves:
    def __init__(self):
        self.CODE_ANALYZER_SERVER_URL = "http://home-ai-server.local:5002"
        self.request_timeout = 30
        self.auto_context_injection = True
        self.context_format = "detailed"
        self.current_codebase = None

async def test_help_commands():
    """Test various help command variations"""
    
    tool = Tools()
    tool.valves = MockValves()
    
    test_commands = [
        # Specific help commands (should work)
        "code analysis help",
        "code tools help", 
        "codebase help",
        "help with code analysis",
        "how to analyze code",
        
        # Generic help commands (should work)
        "help",
        "what can you do",
        
        # Should NOT trigger help intent
        "help me debug this function",  # Too specific/contextual
        "I need help with Python",     # Too specific/contextual
    ]
    
    print("🧪 Testing Updated Help Intent Detection")
    print("=" * 60)
    
    for i, command in enumerate(test_commands, 1):
        print(f"\n📋 Test {i}: '{command}'")
        
        intent = tool._detect_query_intent(command)
        print(f"   Detected intent: {intent}")
        
        if intent == "help":
            print("   ✅ HELP INTENT DETECTED")
        else:
            print(f"   ❌ Different intent: {intent}")
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY:")
    print("✅ Specific commands should detect 'help' intent")
    print("✅ Generic standalone commands should detect 'help' intent") 
    print("❌ Contextual help requests should NOT detect 'help' intent")

if __name__ == "__main__":
    asyncio.run(test_help_commands())
