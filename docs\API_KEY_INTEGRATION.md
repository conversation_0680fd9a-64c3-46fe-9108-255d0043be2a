# API Key Integration for OpenWebUI Testing

## 🎯 Overview

Your OpenWebUI API key has been integrated directly into the testing scripts, eliminating the need for manual setup and making testing seamless.

## 🔑 Integrated API Key

**API Key**: `sk-320242e0335e45a4b1fa4752f758f9ab`
**Server**: `http://home-ai-server.local:8080`

## ✅ What's Changed

### **Before Integration**
```bash
# Manual setup required every time
export OPENWEBUI_API_KEY="sk-320242e0335e45a4b1fa4752f758f9ab"
python debug-testing/openwebui_auto_tester.py

# Or enter manually when prompted
python debug-testing/openwebui_auto_tester.py
Optional: Enter OpenWebUI API key (or press Enter to skip): sk-320242e0335e45a4b1fa4752f758f9ab
```

### **After Integration**
```bash
# Just run - no setup needed!
python debug-testing/openwebui_auto_tester.py
🔑 Using default API key: sk-320242e...f9ab
```

## 🚀 Benefits

### **1. Zero Setup**
- No environment variables to set
- No manual key entry required
- Works immediately out of the box

### **2. Consistent Testing**
- Same API key used across all test runs
- No risk of forgetting to set the key
- Reliable authentication every time

### **3. Streamlined Workflow**
- Faster test execution
- Less friction for repeated testing
- Focus on results, not setup

## 🔧 Override Options

### **Environment Variable Override**
```bash
export OPENWEBUI_API_KEY="your-different-key"
python debug-testing/openwebui_auto_tester.py
# Uses environment key instead of default
```

### **Interactive Override**
```bash
python debug-testing/openwebui_auto_tester.py
🔑 Using default API key: sk-320242e...f9ab
Use a different API key? (y/N): y
Enter OpenWebUI API key: your-different-key
```

### **Programmatic Override**
```python
from openwebui_auto_tester import OpenWebUITester

# Use different API key
tester = OpenWebUITester("http://home-ai-server.local:8080", "your-different-key")
```

## 📋 Updated Scripts

### **Scripts with Integrated API Key**
- ✅ `debug-testing/openwebui_auto_tester.py`
- ✅ `debug-testing/test_api_config.py`

### **Scripts Not Affected (No API Required)**
- `debug-testing/openwebui_browser_tester.py` (uses browser)
- `debug-testing/interactive_tool_test.py` (direct tool testing)
- `debug-testing/curl_test_commands.sh` (manual curl commands)

## 🧪 Testing the Integration

### **Test API Key Integration**
```bash
python debug-testing/test_api_key_integration.py
```

### **Test API Configuration**
```bash
python debug-testing/test_api_config.py
# Uses integrated API key automatically
```

### **Run Full Tool Tests**
```bash
python debug-testing/openwebui_auto_tester.py
# No authentication prompts - just works!
```

## 🔍 Expected Behavior

### **Successful Integration**
```bash
python debug-testing/openwebui_auto_tester.py

🧪 OpenWebUI RAG Tool Auto-Tester
==================================================
🔑 Using default API key: sk-320242e...f9ab
✅ OpenWebUI server accessible: 200

🔐 Testing API Key Permissions:
----------------------------------------
✅ /api/chat/completions: 200 - Chat completions (main testing endpoint)
✅ /api/models: 200 - Models list
✅ /health: 200 - Health check
```

### **If API Key Issues**
```bash
⚠️ API Key Issues Detected:
   The chat completions endpoint is not accessible.
   This could be due to:
   • Invalid API key
   • API key endpoint restrictions
```

## 🛠️ Troubleshooting

### **API Key Not Working**
1. **Check OpenWebUI Settings:**
   - Verify API key is still valid
   - Check if endpoint restrictions are enabled

2. **Test with curl:**
   ```bash
   curl -H "Authorization: Bearer sk-320242e0335e45a4b1fa4752f758f9ab" \
        http://home-ai-server.local:8080/api/models
   ```

3. **Use Alternative Testing:**
   ```bash
   python debug-testing/openwebui_browser_tester.py
   ```

### **Need Different API Key**
1. **Create new key in OpenWebUI**
2. **Use environment override:**
   ```bash
   export OPENWEBUI_API_KEY="new-key"
   ```
3. **Or use interactive override when prompted**

## 📊 Security Considerations

### **API Key Storage**
- Stored in script configuration (not environment)
- Visible in source code (development environment)
- Masked in console output (shows only first 10 + last 4 chars)

### **For Production Use**
- Consider using environment variables
- Implement key rotation if needed
- Monitor API key usage in OpenWebUI

## 🎉 Ready to Test!

Your testing environment is now fully configured with automatic API authentication. Just run:

```bash
python debug-testing/openwebui_auto_tester.py
```

And start testing your RAG tool immediately! 🚀

## 📋 Quick Reference

| Command | Purpose | API Key |
|---------|---------|---------|
| `openwebui_auto_tester.py` | Full automated testing | ✅ Integrated |
| `test_api_config.py` | API configuration test | ✅ Integrated |
| `openwebui_browser_tester.py` | Browser-based testing | ❌ Not needed |
| `interactive_tool_test.py` | Direct tool testing | ❌ Not needed |

**Result: Zero-friction testing with automatic authentication!** 🎯
