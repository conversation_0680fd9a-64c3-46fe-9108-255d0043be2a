#!/usr/bin/env python3
"""
Detailed analysis of server responses to understand chunk vs context formatting
"""

import requests
import json

# Server configuration
SERVER_URL = "http://home-ai-server.local:5002"
CODEBASE = "utils"

def test_server_response_detail():
    """Test server response to understand chunk vs context structure"""
    print("🔍 DETAILED SERVER RESPONSE ANALYSIS")
    print("="*60)
    
    # Select codebase first
    print("\n🔧 Selecting codebase...")
    select_payload = {"codebase_name": CODEBASE}
    response = requests.post(f"{SERVER_URL}/tools/select_codebase", json=select_payload, timeout=10)
    print(f"   Selection status: {response.status_code}")
    
    # Test the memory management query
    query = "tell me about memory management in the utils codebase"
    print(f"\n🔧 Testing query: '{query}'")
    
    payload = {
        "query": query,
        "codebase_name": CODEBASE,
        "n_results": 5,
        "context_preferences": None
    }
    
    print(f"   Payload: {json.dumps(payload, indent=2)}")
    
    response = requests.post(
        f"{SERVER_URL}/tools/get_optimized_context",
        json=payload,
        headers={"Content-Type": "application/json"},
        timeout=30
    )
    
    print(f"\n📊 Response Analysis:")
    print(f"   Status: {response.status_code}")
    print(f"   Content-Length: {len(response.text)}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"   Response keys: {list(data.keys())}")
            
            chunk_count = data.get("chunk_count", "NOT_PROVIDED")
            query_echo = data.get("query", "NOT_PROVIDED")
            result = data.get("result", "")
            
            print(f"   chunk_count: {chunk_count}")
            print(f"   query: {query_echo}")
            print(f"   result length: {len(result)}")
            
            print(f"\n📄 Full Result Content:")
            print("-" * 40)
            print(result)
            print("-" * 40)
            
            # Analyze the result structure
            print(f"\n🔍 Result Structure Analysis:")
            
            # Count different patterns
            context_patterns = [
                ("Found X relevant", r"Found (\d+) relevant"),
                ("## Context", r"## Context \d+"),
                ("Context X:", r"Context \d+:"),
                ("```", r"```"),
                ("**File:**", r"\*\*File:\*\*"),
                ("**Quality:**", r"\*\*Quality:\*\*")
            ]
            
            import re
            for pattern_name, pattern in context_patterns:
                matches = re.findall(pattern, result)
                print(f"   {pattern_name}: {len(matches)} matches")
                if matches and pattern_name == "Found X relevant":
                    print(f"      Found: {matches[0]} sections")
            
        except json.JSONDecodeError as e:
            print(f"   ❌ JSON decode error: {e}")
            print(f"   Raw response: {response.text[:500]}...")
    else:
        print(f"   ❌ Error response: {response.text}")

def test_different_n_results():
    """Test with different n_results to see how chunk_count changes"""
    print("\n" + "="*60)
    print("🔍 TESTING DIFFERENT N_RESULTS")
    print("="*60)
    
    query = "tmwmem_alloc"
    
    for n_results in [1, 3, 5, 10]:
        print(f"\n🔧 Testing n_results={n_results}")
        
        payload = {
            "query": query,
            "codebase_name": CODEBASE,
            "n_results": n_results,
            "context_preferences": None
        }
        
        response = requests.post(
            f"{SERVER_URL}/tools/get_optimized_context",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            chunk_count = data.get("chunk_count", "NOT_PROVIDED")
            result = data.get("result", "")
            
            # Count contexts in result
            import re
            context_matches = re.findall(r"## Context \d+", result)
            found_matches = re.findall(r"Found (\d+) relevant", result)
            
            print(f"   chunk_count: {chunk_count}")
            print(f"   ## Context patterns: {len(context_matches)}")
            if found_matches:
                print(f"   'Found X relevant': {found_matches[0]}")
            print(f"   Result length: {len(result)}")
        else:
            print(f"   ❌ Error: {response.status_code}")

if __name__ == "__main__":
    test_server_response_detail()
    test_different_n_results()
