#!/usr/bin/env python3
"""
Test the improved parameter handling fix
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_select_codebase_fix():
    """Test the improved select codebase fix"""
    print("🔧 Testing Improved Select Codebase Fix")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test the select codebase command
    print("\n🧪 Testing: 'select codebase utils'")
    print("-" * 40)
    
    try:
        payload = {
            "model": "llama3:latest",
            "messages": [{"role": "user", "content": "select codebase utils"}],
            "tool_ids": ["code_analyzer_tool"],
            "stream": False,
            "max_tokens": 800
        }
        
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            print(f"✅ Response received ({len(content)} chars)")
            
            # Check for different types of responses
            if "dict object having no attribute 'strip'" in content:
                print("❌ STILL HAS OLD ERROR: Parameter handling not fixed")
                return "old_error"
            elif "Invalid codebase parameter" in content:
                print("⚠️ NEW ERROR HANDLING: Tool is catching parameter issues")
                return "new_error_handling"
            elif any(indicator in content for indicator in ["✅", "Selected", "utils", "ready_enhanced", "Successfully"]):
                print("🎉 SUCCESS: Select codebase is working!")
                return "success"
            elif "error message from the `code_analyzer_tools/select_codebase` tool" in content:
                print("❌ ORIGINAL ERROR: Tool still has old error")
                return "original_error"
            else:
                print("⚠️ UNCLEAR: Unknown response type")
                return "unclear"
            
        else:
            print(f"❌ HTTP {response.status_code}: {response.text[:200]}")
            return "http_error"
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return "exception"

def test_status_after_fix():
    """Test status command to see current state"""
    print(f"\n📊 Testing Status Command")
    print("-" * 40)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    try:
        payload = {
            "model": "llama3:latest",
            "messages": [{"role": "user", "content": "status"}],
            "tool_ids": ["code_analyzer_tool"],
            "stream": False,
            "max_tokens": 500
        }
        
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            print(f"✅ Status response received ({len(content)} chars)")
            
            # Look for current codebase info
            lines = content.split('\n')
            for line in lines:
                if 'current' in line.lower() and ('codebase' in line.lower() or 'selection' in line.lower()):
                    print(f"📋 {line.strip()}")
                    
        else:
            print(f"❌ Status failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Status error: {e}")

def main():
    """Main test function"""
    print("🔧 Improved Parameter Handling Test")
    print("=" * 70)
    print("Testing the enhanced select_codebase function")
    
    # Test the fix
    result = test_select_codebase_fix()
    
    # Test status
    test_status_after_fix()
    
    # Analysis
    print(f"\n📊 ANALYSIS")
    print("=" * 50)
    
    if result == "success":
        print("🎉 EXCELLENT: Select codebase is working perfectly!")
        print("✅ Parameter handling fix successful")
        print("✅ Ready for full testing")
        
    elif result == "new_error_handling":
        print("🔧 PROGRESS: New error handling is working")
        print("⚠️ But still having parameter format issues")
        print("💡 OpenWebUI might be sending unexpected parameter format")
        
    elif result in ["old_error", "original_error"]:
        print("❌ ISSUE: Tool still has old errors")
        print("🔧 Need to ensure tool is properly updated in OpenWebUI")
        
    else:
        print(f"⚠️ UNCLEAR RESULT: {result}")
        print("🔧 May need further investigation")
    
    print(f"\n🎯 Next Steps:")
    if result == "success":
        print("✅ Run comprehensive tests:")
        print("   python debug-testing/openwebui_auto_tester.py")
    else:
        print("🔧 Update tool in OpenWebUI with latest code")
        print("📋 Check OpenWebUI Functions settings")

if __name__ == "__main__":
    main()
