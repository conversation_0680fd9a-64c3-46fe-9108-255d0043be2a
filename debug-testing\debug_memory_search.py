#!/usr/bin/env python3
"""
Debug script to investigate why memory management queries only return 1 chunk.
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path to import the tool
sys.path.append(str(Path(__file__).parent))

from open_webui_code_analyzer_tool import Tools

async def debug_memory_search():
    """Debug memory management search results"""
    print("🔍 Debugging Memory Management Search")
    print("=" * 60)
    
    # Initialize the tool
    tool = Tools()
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    
    # Ensure utils is selected
    print("1. Selecting utils codebase...")
    await tool.select_codebase("utils")
    
    # Test different memory-related queries
    test_queries = [
        "how is memory managed",
        "memory management",
        "memory allocation", 
        "malloc",
        "free",
        "tmwmem",
        "memory functions"
    ]
    
    for i, query in enumerate(test_queries, 2):
        print(f"\n{i}. Testing query: '{query}'")
        
        # Check what search parameters would be used
        search_params = tool._analyze_query_for_context(query)
        print(f"   Search params: {search_params}")
        
        try:
            # Test with different n_results values
            for n_results in [5, 10, 20]:
                print(f"   Testing with n_results={n_results}...")
                result = await tool.get_code_context(query, codebase_name="utils", n_results=n_results)
                
                # Count chunks in result
                chunk_count = result.count("## Context")
                if chunk_count == 0:
                    # Try alternative counting method
                    import re
                    match = re.search(r'Found (\d+) relevant code section', result)
                    if match:
                        chunk_count = int(match.group(1))
                
                print(f"     Found {chunk_count} chunks")
                
                if chunk_count > 1:
                    print(f"     ✅ Success with n_results={n_results}")
                    break
            else:
                print(f"     ⚠️ Only found {chunk_count} chunk(s) even with n_results=20")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 Memory search debug completed!")

if __name__ == "__main__":
    try:
        asyncio.run(debug_memory_search())
    except KeyboardInterrupt:
        print("\n⚠️ Debug interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Debug failed with error: {e}")
        sys.exit(1)
