// VGA timing parameters for 320x240 resolution
module vga_timing (
    input wire clk_50m,
    input wire rst_n,
    output reg [8:0] x_pos,
    output reg [7:0] y_pos,
    output reg hsync,
    output reg vsync,
    output reg display_enable
);
    // Horizontal timing (adjusted for 50MHz)
    parameter H_DISPLAY = 320;
    parameter H_FRONT = 10;    // Reduced from 20
    parameter H_SYNC = 20;     // Reduced from 30
    parameter H_BACK = 10;     // Reduced from 30
    parameter H_TOTAL = H_DISPLAY + H_FRONT + H_SYNC + H_BACK;

    // Vertical timing (kept same as resolution is unchanged)
    parameter V_DISPLAY = 240;
    parameter V_FRONT = 10;
    parameter V_SYNC = 4;
    parameter V_BACK = 10;
    parameter V_TOTAL = V_DISPLAY + V_FRONT + V_SYNC + V_BACK;

    reg [9:0] h_count;
    reg [9:0] v_count;

    always @(posedge clk_50m or negedge rst_n) begin
        if (!rst_n) begin
            h_count <= 10'd0;
            v_count <= 10'd0;
            hsync <= 1'b1;
            vsync <= 1'b1;
            display_enable <= 1'b0;
        end
        else begin
            // Horizontal counter
            if (h_count < H_TOTAL - 1)
                h_count <= h_count + 10'd1;
            else begin
                h_count <= 10'd0;
                if (v_count < V_TOTAL - 1)
                    v_count <= v_count + 10'd1;
                else
                    v_count <= 10'd0;
            end

            // Generate hsync
            hsync <= ~(h_count >= (H_DISPLAY + H_FRONT) &&
                      h_count < (H_DISPLAY + H_FRONT + H_SYNC));

            // Generate vsync
            vsync <= ~(v_count >= (V_DISPLAY + V_FRONT) &&
                      v_count < (V_DISPLAY + V_FRONT + V_SYNC));

            // Generate display enable
            display_enable <= (h_count < H_DISPLAY) && (v_count < V_DISPLAY);

            // Generate pixel coordinates
            if (h_count < H_DISPLAY)
                x_pos <= h_count[8:0];
            if (v_count < V_DISPLAY)
                y_pos <= v_count[7:0];
        end
    end
endmodule

// Color conversion and frame buffer
module vga_to_tft_converter (
    input wire clk,
    input wire rst_n,
    
    // VGA-style inputs
    input wire [7:0] red_in,
    input wire [7:0] green_in,
    input wire [7:0] blue_in,
    input wire hsync_in,
    input wire vsync_in,
    input wire display_enable_in,
    
    // TFT interface outputs
    output reg [15:0] tft_pixel_data,
    output reg pixel_valid
);
    // Convert 24-bit RGB to 16-bit RGB565
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            tft_pixel_data <= 16'd0;
            pixel_valid <= 1'b0;
        end
        else begin
            pixel_valid <= display_enable_in;
            tft_pixel_data <= {red_in[7:3], green_in[7:2], blue_in[7:3]};
        end
    end
endmodule

// Test pattern generator - Red Square
module test_pattern_gen (
    input wire clk,
    input wire rst_n,
    input wire [8:0] x_pos,
    input wire [7:0] y_pos,
    output reg [7:0] red,
    output reg [7:0] green,
    output reg [7:0] blue
);
    // Parameters for red square
    parameter SQUARE_X = 100;
    parameter SQUARE_Y = 80;
    parameter SQUARE_SIZE = 50;

    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            red <= 8'd0;
            green <= 8'd0;
            blue <= 8'd0;
        end
        else begin
            // Draw red square on black background
            if (x_pos >= SQUARE_X && x_pos < (SQUARE_X + SQUARE_SIZE) &&
                y_pos >= SQUARE_Y && y_pos < (SQUARE_Y + SQUARE_SIZE)) begin
                red <= 8'd255;    // Full red
                green <= 8'd0;
                blue <= 8'd0;
            end
            else begin
                red <= 8'd0;      // Black background
                green <= 8'd0;
                blue <= 8'd0;
            end
        end
    end
endmodule

// Top module optimized for 50MHz
module tft_vga_top (
    input wire clk_50m,       // 50MHz system clock
    input wire rst_n,         // Active low reset
    
    // TFT SPI Interface
    output wire spi_clk,
    output wire spi_mosi,
    output wire spi_cs_n,
    output wire dc,
    output wire rst_disp_n,
    
    // Status output
    output wire busy
);
    // Internal signals
    wire [8:0] x_pos;
    wire [7:0] y_pos;
    wire hsync, vsync, display_enable;
    wire [7:0] red, green, blue;
    wire [15:0] tft_pixel_data;
    wire pixel_valid;

    // VGA timing generator
    vga_timing timing_gen (
        .clk_50m(clk_50m),
        .rst_n(rst_n),
        .x_pos(x_pos),
        .y_pos(y_pos),
        .hsync(hsync),
        .vsync(vsync),
        .display_enable(display_enable)
    );

    // Test pattern generator (same as before)
    test_pattern_gen pattern_gen (
        .clk(clk_50m),
        .rst_n(rst_n),
        .x_pos(x_pos),
        .y_pos(y_pos),
        .red(red),
        .green(green),
        .blue(blue)
    );

    // VGA to TFT converter (same as before)
    vga_to_tft_converter converter (
        .clk(clk_50m),
        .rst_n(rst_n),
        .red_in(red),
        .green_in(green),
        .blue_in(blue),
        .hsync_in(hsync),
        .vsync_in(vsync),
        .display_enable_in(display_enable),
        .tft_pixel_data(tft_pixel_data),
        .pixel_valid(pixel_valid)
    );

    // TFT Display driver
    ili9341_driver tft_driver (
        .clk_50m(clk_50m),
        .rst_n(rst_n),
        .spi_clk(spi_clk),
        .spi_mosi(spi_mosi),
        .spi_cs_n(spi_cs_n),
        .dc(dc),
        .rst_disp_n(rst_disp_n),
        .pixel_data(tft_pixel_data),
        .pixel_addr_x(x_pos[7:0]),
        .pixel_addr_y(y_pos),
        .busy(busy)
    );

endmodule