{"health": {"success": true, "status_code": 200, "response": {"code_analyzer_service": "healthy", "version": "3.1.0", "optimization_level": "single_llm_calls", "supported_languages": ["C", "C++", "Python", "C#", "<PERSON><PERSON>", "CommonLisp", "EmacsLisp", "Fortran", "Go", "HTML", "JSON", "Java", "JavaScript", "<PERSON><PERSON>", "Make", "<PERSON><PERSON>", "PHP", "<PERSON><PERSON>", "Rust", "SQL", "Scheme", "TCL", "TypeScript", "VHDL", "Verilog", "XML", "YAML"], "embedding_provider": "ollama", "ollama_host": "http://ollama:11434", "chroma_db_path": "./chroma_db", "source_code_path": "./source_code", "available_codebases": 10, "current_codebase": "utils", "enhanced_codebases": 10, "basic_codebases": 0, "enhancement_coverage": "10/10", "languages_detected": {"C++": 3, "C/C++": 3, "C": 2, "C#": 2, "Markdown": 3, "YAML": 2, "Make": 1, "JavaScript": 2, "HTML": 1, "Bash": 1, "Go": 1, "Rust": 1, "TypeScript": 2, "Verilog": 1, "JSON": 1}, "total_indexed_chunks": 2283, "codebase_status_summary": {"ready_enhanced": 10}, "active_search_indexes": 3, "search_optimization": "enabled", "ollama": "healthy", "available_models": 40, "embedding_model_available": true, "ollama_models": ["nomic-embed-text:latest", "deepseek-coder:33b", "deepseek-coder:6.7b", "codestral:22b", "smollm2:1.7b", "Gemma3n:latest", "codellama:7b-instruct", "smollm:135m", "deepseek-r1:8b", "phi4-reasoning:latest"], "embedding_test": "passed", "embedding_dimensions": 768, "source_code_directory": "available", "source_subdirectories": 10, "source_subdirectory_names": ["utils", "go-example-master", "rust-starter-master", "test_project", "z80emu", "bookstore", "vga-to-spi-tft", "modbus", "TypeScript-Node-Starter-master", "networking_project"], "detected_file_types": [".cs", ".cpp", ".h", ".c"], "file_type_counts": {".c": 37, ".cpp": 6, ".h": 52, ".cs": 60}, "total_source_files": 155, "chroma_db_directory": "available", "chroma_db_size_bytes": 117319643, "chroma_db_size_mb": 111.88, "chromadb_collections": 10, "collection_names": ["go-example-master", "vga-to-spi-tft", "bookstore", "TypeScript-Node-Starter-master", "test_project", "utils", "rust-starter-master", "networking_project", "z80emu", "modbus"], "collection_details": {"go-example-master": {"document_count": 54, "status": "healthy", "enhanced_metadata": true, "metadata_version": "v3.0_enhanced"}, "vga-to-spi-tft": {"document_count": 2, "status": "healthy", "enhanced_metadata": true, "metadata_version": "v3.0_enhanced"}, "bookstore": {"document_count": 4, "status": "healthy", "enhanced_metadata": true, "metadata_version": "v3.0_enhanced"}, "TypeScript-Node-Starter-master": {"document_count": 23, "status": "healthy", "enhanced_metadata": true, "metadata_version": "v3.0_enhanced"}, "test_project": {"document_count": 10, "status": "healthy", "enhanced_metadata": true, "metadata_version": "v3.0_enhanced"}, "utils": {"document_count": 479, "status": "healthy", "enhanced_metadata": true, "metadata_version": "v3.0_enhanced"}, "rust-starter-master": {"document_count": 13, "status": "healthy", "enhanced_metadata": true, "metadata_version": "v3.0_enhanced"}, "networking_project": {"document_count": 19, "status": "healthy", "enhanced_metadata": true, "metadata_version": "v3.0_enhanced"}, "z80emu": {"document_count": 1391, "status": "healthy", "enhanced_metadata": true, "metadata_version": "v3.0_enhanced"}, "modbus": {"document_count": 288, "status": "healthy", "enhanced_metadata": true, "metadata_version": "v3.0_enhanced"}}, "enhanced_collections_count": 10, "enhancement_adoption": "10/10", "system_info": "psutil not available for system monitoring", "overall_status": "healthy", "performance_metrics": {"single_llm_optimization": "enabled", "search_pre_filtering": "enabled", "enhanced_metadata_coverage": "10/10", "optimization_features": ["context_only_retrieval", "semantic_pre_filtering", "quality_based_ranking", "complexity_aware_search"]}, "timestamp": "2025-07-01T13:04:26.364574", "uptime_check": "completed"}, "url": "http://home-ai-server:5002/health", "method": "GET", "payload": null}, "status": {"success": true, "status_code": 200, "response": {"status": "online", "service": "OpenWebUI Enhanced Multi-Language Code Analyzer Tool Server", "version": "3.1.0", "timestamp": "2025-07-01T13:04:26.393613", "code_analyzer_service": "operational", "available_codebases": 10, "current_codebase": "utils", "ollama": "connected", "ollama_models": 40, "overall": "healthy"}, "url": "http://home-ai-server:5002/status", "method": "GET", "payload": null}, "list_codebases": {"success": true, "status_code": 200, "response": {"result": "📚 **Available Codebases (Enhanced Multi-Language Support):**\n\n**🚀 utils**\n   Status: ready_enhanced\n   Chunks: 479\n   Enhanced Metadata: Yes (v3.0_enhanced)\n   Last Updated: 2025-06-28T18:59:05.565020\n   Languages: C++, C/C++, C, C#\n   Files: .c(19), .cpp(3), .cs(1), .h(27)\n   Complexity: small\n\n**🚀 go-example-master**\n   Status: ready_enhanced\n   Chunks: 54\n   Enhanced Metadata: Yes (v3.0_enhanced)\n   Last Updated: 2025-06-30T18:47:58.982411\n   Languages: Markdown, YAML, Make, JavaScript, HTML, Bash, Go\n   Files: .go(37), .html(1), .js(1), .md(7), .sh(4), .yaml(1), .yml(1), makefile(2)\n   Complexity: small\n\n**🚀 rust-starter-master**\n   Status: ready_enhanced\n   Chunks: 13\n   Enhanced Metadata: Yes (v3.0_enhanced)\n   Last Updated: 2025-06-30T18:24:26.615934\n   Languages: Markdown, Rust\n   Files: .md(1), .rs(13)\n   Complexity: small\n\n**🚀 test_project**\n   Status: ready_enhanced\n   Chunks: 10\n   Enhanced Metadata: Yes (v3.0_enhanced)\n   Last Updated: 2025-06-27T16:11:09.199778\n   Languages: C++\n   Files: .cpp(2)\n   Complexity: very_small\n\n**🚀 z80emu**\n   Status: ready_enhanced\n   Chunks: 1,391\n   Enhanced Metadata: Yes (v3.0_enhanced)\n   Last Updated: 2025-06-27T16:11:13.564655\n   Languages: C#\n   Files: .cs(59)\n   Complexity: small\n\n**🚀 bookstore**\n   Status: ready_enhanced\n   Chunks: 4\n   Enhanced Metadata: Yes (v3.0_enhanced)\n   Last Updated: 2025-06-30T17:51:59.699536\n   Languages: TypeScript\n   Files: .ts(4)\n   Complexity: very_small\n\n**🚀 vga-to-spi-tft**\n   Status: ready_enhanced\n   Chunks: 2\n   Enhanced Metadata: Yes (v3.0_enhanced)\n   Last Updated: 2025-07-01T00:48:39.760918\n   Languages: Verilog\n   Files: .v(2)\n   Complexity: very_small\n\n**🚀 modbus**\n   Status: ready_enhanced\n   Chunks: 288\n   Enhanced Metadata: Yes (v3.0_enhanced)\n   Last Updated: 2025-06-27T16:12:41.173841\n   Languages: C/C++, C\n   Files: .c(18), .h(24)\n   Complexity: small\n\n**🚀 TypeScript-Node-Starter-master**\n   Status: ready_enhanced\n   Chunks: 23\n   Enhanced Metadata: Yes (v3.0_enhanced)\n   Last Updated: 2025-06-30T17:33:03.559462\n   Languages: Markdown, YAML, TypeScript, JavaScript, JSON\n   Files: .js(3), .json(7), .md(1), .ts(20), .yml(2)\n   Complexity: small\n\n**🚀 networking_project**\n   Status: ready_enhanced\n   Chunks: 19\n   Enhanced Metadata: Yes (v3.0_enhanced)\n   Last Updated: 2025-06-27T16:13:09.152773\n   Languages: C++, C/C++\n   Files: .cpp(1), .h(1)\n   Complexity: very_small\n"}, "url": "http://home-ai-server:5002/tools/list_codebases", "method": "POST", "payload": {}}, "get_code_stats": {"success": true, "status_code": 200, "response": {"result": "📊 **Basic Codebase Statistics: utils**\n⚠️ Using legacy mode - use get_enhanced_stats for comprehensive analysis\n\n📄 **Total chunks**: 479\n📁 **Unique files**: 43\n🕒 **Last updated**: 2025-06-28T18:59:05.565020\n\n💻 **Languages:**\n   • C: 460\n   • CPP: 15\n   • CSHARP: 4"}, "url": "http://home-ai-server:5002/tools/get_code_stats", "method": "POST", "payload": {"codebase_name": "utils"}}, "search_code": {"success": true, "status_code": 200, "response": {"result": "🔍 **Legacy Search**: utils\n⚠️ Consider using enhanced_search for better filtering\nFound 3 results:\n\n\n**Result 1** (Relevance: -353.280)\n📁 **File**: `tmwphys.c` | **Language**: C | **Type**: function\n\n```c\n=== CODE METADATA ===\nFile: tmwphys.c | Lines: 800-846 | Language: C | Type: function | Domains: memory_management, io_operations | Quality: fair | Complexity: high | Function: tmwphys_modifyPhys | Parameters: 3 | Patterns: conditional_logic\n\n=== SOURCE CODE ===\nFunction: tmwphys_modifyPhys\nFile: source_code/utils/tmwphys.c\nLines: 800-846\n\nTMWTYPES_BOOL TMWDEFS_GLOBAL tmwphys_modifyPhys(\n  TMWCHNL...\n```\n\n\n**Result 2** (Relevance: -354.137)\n📁 **File**: `tmwsim.h` | **Language**: C | **Type**: struct_specifier\n\n```c\n=== CODE METADATA ===\nFile: tmwsim.h | Lines: 202-248 | Language: C | Type: struct_specifier | Domains: memory_management, data_structures, testing | Quality: good | Complexity: high | Patterns: iterative\n\n=== SOURCE CODE ===\nDefinition (struct_specifier)\nFile: source_code/utils/tmwsim.h\nLines: 202-248\n\nstruct TMWSIMPointStruct {\n  /* List Member, must be first entry */\n  TMWDLIST_MEMBER listMembe...\n```\n\n\n**Result 3** (Relevance: -384.395)\n📁 **File**: `tmwphys.c` | **Language**: C | **Type**: function\n\n```c\n=== CODE METADATA ===\nFile: tmwphys.c | Lines: 849-888 | Language: C | Type: function | Domains: io_operations, system_calls | Quality: fair | Complexity: high | Function: tmwphys_modifyChannel | Parameters: 3\n\n=== SOURCE CODE ===\nFunction: tmwphys_modifyChannel\nFile: source_code/utils/tmwphys.c\nLines: 849-888\n\nTMWTYPES_BOOL TMWDEFS_GLOBAL tmwphys_modifyChannel(\n  TMWCHNL *pChannel,\n  const TMWPHY...\n```\n"}, "url": "http://home-ai-server:5002/tools/search_code", "method": "POST", "payload": {"query": "memory management", "codebase_name": "utils", "n_results": 3}}, "ask_about_code": {"success": true, "status_code": 200, "response": {"result": "⚠️ **LEGACY MODE - DOUBLE LLM CALL WARNING**\nThis function uses two LLM calls (inefficient). Consider using get_optimized_context instead.\n\n🤖 **AI Analysis of utils:** In the context of the code, memory allocation is handled through a combination of functions and classes in the `tmwmem` module. The `tmwmem_alloc()` function is used to allocate memory for a specific size, while the `tmwmem_free()` function is used to free previously allocated memory.\n\nThe `tmwmem` module provides a set of functions that are used to manage memory in a target-independent way. These functions include `tmwmem_alloc()`, `tmwmem_free()`, and `tmwmem_realloc()`. The `tmwmem_alloc()` function is used to allocate memory for a specific size, while the `tmwmem_free()` function is used to free previously allocated memory. The `tmwmem_realloc()` function is used to reallocate memory for a specific size, but it also allows for the ability to move the memory location if necessary.\n\nIn addition to these functions, the `tmwmem` module also provides a set of classes that are used to manage memory in a target-dependent way. These classes include `TMWMEM_ALLOCATOR`, `TMWMEM_POOL`, and `TMWMEM_HEAP`. The `TMWMEM_ALLOCATOR` class is used to allocate memory for a specific size, while the `TMWMEM_POOL` class is used to manage a pool of memory. The `TMWMEM_HEAP` class is used to manage a heap of memory.\n\nOverall, the `tmwmem` module provides a set of functions and classes that are used to manage memory in a target-independent way, while also providing a set of classes that are used to manage memory in a target-dependent way. This allows for a consistent and efficient way of managing memory across different targets.\n\n📚 **Based on 10 code sections:**\n   1. `tmwdiag.c` (function, lines 402-436)\n   2. `tmwmem.c` (function, lines 252-279)\n   3. `tmwcrypto.c` (function, lines 2184-2341)\n   4. `tmwdb.c` (function, lines 66-82)\n   5. `tmwvrsn.cs` (using_directive, lines 1-1)\n   6. `tmwcrypto.c` (function, lines 412-558)\n   7. `tmwmem.c` (function, lines 281-324)\n   8. `tmwcrypto.c` (function, lines 385-408)\n   9. `tmwsim.c` (function, lines 1112-1140)\n   10. `tmwappl.c` (function, lines 118-143)"}, "url": "http://home-ai-server:5002/tools/ask_about_code", "method": "POST", "payload": {"question": "How does memory allocation work?", "codebase_name": "utils"}}, "raw_search": {"success": true, "status_code": 200, "response": {"results": [{"content": "=== CODE METADATA ===\nFile: tmwdiag.c | Lines: 402-436 | Language: C | Type: function | Domains: memory_management, data_structures, io_operations | Quality: good | Complexity: high | Function: tmwdiag_showMemoryUsage | Parameters: 1 | Patterns: iterative, conditional_logic\n\n=== SOURCE CODE ===\nFunction: tmwdiag_showMemoryUsage\nFile: source_code/utils/tmwdiag.c\nLines: 402-436\n\nvoid TMWDEFS_GLOBAL tmwdiag_showMemoryUsage(\n  TMWMEM_GET_USAGE_FUNC pFunc)\n{\n  TMWDIAG_ANLZ_ID id;\n  TMWTYPES_CHAR buf[256];\n  TMWMEM_POOL_STRUCT allocInfo;\n  const TMWTYPES_CHAR *pName;\n  TMWTYPES_UCHAR   i=0;\n\n  if (tmwdiag_initId(&id, TMWDEFS_NULL, TMWDEFS_NULL, TMWDEFS_NULL, TMWDIAG_ID_MMI) == TMWDEFS_FALSE)\n  {\n    return;\n  }\n\n  (void)tmwtarg_snprintf(buf, sizeof(buf), \"      structure                     allocated       free          max        size\\n\");\n\n  tmwdiag_putLine(&id, \"\\n\");\n  tmwdiag_putLine(&id, buf);\n  \n  while(pFunc(i, &pName, &allocInfo))\n  {\n    i++;\n\n#if TMWCNFG_USE_DYNAMIC_MEMORY && !TMWCNFG_ALLOC_ONLY_AT_STARTUP \n    if(allocInfo.max > 0)\n      (void)tmwtarg_snprintf(buf, sizeof(buf), \"%30s  %10d   %10d   %10d  %10d\\n\", pName, allocInfo.allocated, (allocInfo.max-allocInfo.allocated), allocInfo.max, allocInfo.size);\n    else\n      (void)tmwtarg_snprintf(buf, sizeof(buf), \"%30s  %10d   %10d   %10d  %10d\\n\", pName, allocInfo.allocated, 0, 0, allocInfo.size);\n    tmwdiag_putLine(&id, buf);\n#else\n    (void)tmwtarg_snprintf(buf, sizeof(buf), \"%30s  %10d   %10d   %10d  %10d\\n\", pName, tmwdlist_size(&allocInfo.allocatedBuffers), tmwdlist_size(&allocInfo.freeBuffers), allocInfo.max, allocInfo.size);\n    tmwdiag_putLine(&id, buf);\n#endif\n  }\n}\n\n=== SEARCHABLE TERMS ===\nfunction_tmwdiag_showMemoryUsage domain_memory_management domain_data_structures domain_io_operations quality_good complexity_high memory_management", "metadata": {"api_surface": "{\"visibility\": \"unknown\", \"is_public\": false, \"is_static\": false, \"is_abstract\": false, \"is_virtual\": false, \"return_type\": null, \"parameters\": []}", "chunk_id": "07157aea", "code_patterns": "[\"iterative\", \"conditional_logic\"]", "codebase_name": "utils", "complexity_metrics": "{\"line_count\": 35, \"non_empty_lines\": 30, \"comment_lines\": 0, \"code_lines\": 30, \"cyclomatic_complexity\": 9, \"nesting_depth\": 3, \"parameter_count\": 1, \"has_loops\": true, \"has_conditionals\": true, \"has_exception_handling\": false, \"complexity_score\": \"high\"}", "dependencies": "{\"function_calls\": [\"tmwdlist_size\", \"while\", \"pFunc\", \"tmwdiag_showMemoryUsage\", \"else\", \"sizeof\", \"tmwdiag_putLine\", \"tmwtarg_snprintf\", \"if\", \"tmwdiag_initId\"], \"imports\": [], \"includes\": [], \"external_libraries\": [], \"internal_calls\": [\"free\", \"printf\"]}", "documentation_info": "{\"has_docstring\": false, \"has_comments\": false, \"comment_ratio\": 0.0, \"doc_type\": \"\", \"doc_length\": 0}", "end_line": 436, "filepath": "source_code/utils/tmwdiag.c", "function_name": "tmwdiag_showMemoryUsage", "has_enhanced_metadata": true, "language": "c", "metadata_version": "3.0_enhanced", "processed_date": "2025-06-28T18:59:05.565020", "processor_version": "v3.0_enhanced", "quality_indicators": "{\"has_documentation\": false, \"has_error_handling\": false, \"has_logging\": true, \"has_constants\": true, \"has_magic_numbers\": false, \"naming_convention\": \"camelCase\", \"code_smell_indicators\": [\"long_parameter_list\"], \"maintainability_score\": \"good\"}", "relative_path": "tmwdiag.c", "semantic_tags": "[\"memory_management\", \"data_structures\", \"io_operations\"]", "start_line": 402, "type": "function"}, "distance": 355.42529296875, "relevance_score": -354.42529296875, "semantic_tags": ["memory_management", "data_structures", "io_operations"], "complexity_score": "high", "quality_score": "good", "is_documented": false}, {"content": "=== CODE METADATA ===\nFile: tmwvrsn.cs | Lines: 1-1 | Language: CSHARP | Type: using_directive | Domains: memory_management | Quality: fair | Complexity: low\n\n=== SOURCE CODE ===\nDefinition (using_directive)\nFile: source_code/utils/tmwvrsn.cs\nLines: 1-1\n\nusing System;\n\n=== SEARCHABLE TERMS ===\ndomain_memory_management quality_fair complexity_low", "metadata": {"api_surface": "{\"visibility\": \"unknown\", \"is_public\": false, \"is_static\": false, \"is_abstract\": false, \"is_virtual\": false, \"return_type\": null, \"parameters\": []}", "chunk_id": "3a880e55", "code_patterns": "[]", "codebase_name": "utils", "complexity_metrics": "{\"line_count\": 1, \"non_empty_lines\": 1, \"comment_lines\": 0, \"code_lines\": 1, \"cyclomatic_complexity\": 1, \"nesting_depth\": 0, \"parameter_count\": 0, \"has_loops\": false, \"has_conditionals\": false, \"has_exception_handling\": false, \"complexity_score\": \"low\"}", "dependencies": "{\"function_calls\": [], \"imports\": [\"System\"], \"includes\": [], \"external_libraries\": [\"System\"], \"internal_calls\": []}", "documentation_info": "{\"has_docstring\": false, \"has_comments\": false, \"comment_ratio\": 0.0, \"doc_type\": \"\", \"doc_length\": 0}", "end_line": 1, "filepath": "source_code/utils/tmwvrsn.cs", "has_enhanced_metadata": true, "language": "csharp", "metadata_version": "3.0_enhanced", "processed_date": "2025-06-28T18:59:05.565020", "processor_version": "v3.0_enhanced", "quality_indicators": "{\"has_documentation\": false, \"has_error_handling\": false, \"has_logging\": false, \"has_constants\": false, \"has_magic_numbers\": false, \"naming_convention\": \"PascalCase\", \"code_smell_indicators\": [], \"maintainability_score\": \"fair\"}", "relative_path": "tmwvrsn.cs", "semantic_tags": "[\"memory_management\"]", "start_line": 1, "type": "using_directive"}, "distance": 372.7984619140625, "relevance_score": -371.7984619140625, "semantic_tags": ["memory_management"], "complexity_score": "low", "quality_score": "fair", "is_documented": false}, {"content": "=== CODE METADATA ===\nFile: tmwsim.c | Lines: 578-587 | Language: C | Type: function | Domains: string_operations, memory_management | Quality: fair | Complexity: low | Function: tmwsim_setDescription | Parameters: 2\n\n=== SOURCE CODE ===\nFunction: tmwsim_setDescription\nFile: source_code/utils/tmwsim.c\nLines: 578-587\n\nvoid TMWDEFS_GLOBAL tmwsim_setDescription(\n  TMWSIM_POINT *pDataPoint, \n  TMWTYPES_CHAR *pDesc)\n{\n  memset(pDataPoint->description, 0, TMWSIM_MAX_DESC_LENGTH);\n  if(pDesc != TMWDEFS_NULL)\n  {\n    STRCPY(pDataPoint->description, TMWSIM_MAX_DESC_LENGTH, pDesc);\n  }\n}\n\n=== SEARCHABLE TERMS ===\nfunction_tmwsim_setDescription domain_string_operations domain_memory_management quality_fair complexity_low", "metadata": {"api_surface": "{\"visibility\": \"unknown\", \"is_public\": false, \"is_static\": false, \"is_abstract\": false, \"is_virtual\": false, \"return_type\": null, \"parameters\": []}", "chunk_id": "cfdd0970", "code_patterns": "[]", "codebase_name": "utils", "complexity_metrics": "{\"line_count\": 10, \"non_empty_lines\": 10, \"comment_lines\": 0, \"code_lines\": 10, \"cyclomatic_complexity\": 2, \"nesting_depth\": 2, \"parameter_count\": 2, \"has_loops\": false, \"has_conditionals\": true, \"has_exception_handling\": false, \"complexity_score\": \"low\"}", "dependencies": "{\"function_calls\": [\"memset\", \"tmwsim_setDescription\", \"if\", \"STRCPY\"], \"imports\": [], \"includes\": [], \"external_libraries\": [], \"internal_calls\": []}", "documentation_info": "{\"has_docstring\": false, \"has_comments\": false, \"comment_ratio\": 0.0, \"doc_type\": \"\", \"doc_length\": 0}", "end_line": 587, "filepath": "source_code/utils/tmwsim.c", "function_name": "tmwsim_setDescription", "has_enhanced_metadata": true, "language": "c", "metadata_version": "3.0_enhanced", "processed_date": "2025-06-28T18:59:05.565020", "processor_version": "v3.0_enhanced", "quality_indicators": "{\"has_documentation\": false, \"has_error_handling\": false, \"has_logging\": false, \"has_constants\": false, \"has_magic_numbers\": false, \"naming_convention\": \"camelCase\", \"code_smell_indicators\": [], \"maintainability_score\": \"fair\"}", "relative_path": "tmwsim.c", "semantic_tags": "[\"string_operations\", \"memory_management\"]", "start_line": 578, "type": "function"}, "distance": 392.5269775390625, "relevance_score": -391.5269775390625, "semantic_tags": ["string_operations", "memory_management"], "complexity_score": "low", "quality_score": "fair", "is_documented": false}], "query": "memory allocation", "codebase": "utils", "total_results": 3, "supported_languages": ["C", "C++", "Python", "C#", "<PERSON><PERSON>", "CommonLisp", "EmacsLisp", "Fortran", "Go", "HTML", "JSON", "Java", "JavaScript", "<PERSON><PERSON>", "Make", "<PERSON><PERSON>", "PHP", "<PERSON><PERSON>", "Rust", "SQL", "Scheme", "TCL", "TypeScript", "VHDL", "Verilog", "XML", "YAML"], "optimization_used": "enhanced"}, "url": "http://home-ai-server:5002/search", "method": "POST", "payload": {"query": "memory allocation", "n_results": 3}}, "process_codebase_check": {"success": true, "status_code": 200, "response": {"result": "❌ Error processing codebase: Source code directory not found: source_code/test_check_only"}, "url": "http://home-ai-server:5002/tools/process_codebase", "method": "POST", "payload": {"codebase_name": "test_check_only", "exclude_dirs": ["build", "test"]}}, "root": {"success": true, "status_code": 200, "response": {"message": "OpenWebUI Enhanced Multi-Language Code Analyzer Tool Server", "version": "3.1.0", "optimization": "Single LLM calls - No double LLM processing", "supported_languages": ["C", "C++", "Python", "C#", "<PERSON><PERSON>", "CommonLisp", "EmacsLisp", "Fortran", "Go", "HTML", "JSON", "Java", "JavaScript", "<PERSON><PERSON>", "Make", "<PERSON><PERSON>", "PHP", "<PERSON><PERSON>", "Rust", "SQL", "Scheme", "TCL", "TypeScript", "VHDL", "Verilog", "XML", "YAML"], "ollama_host": "http://ollama:11434", "ollama_status": "connected", "current_codebase": "utils", "current_codebase_info": {"name": "utils", "status": "ready_enhanced", "has_source": true, "has_database": true, "chunk_count": 479, "last_updated": "2025-06-28T18:59:05.565020", "source_path": "source_code/utils", "detected_languages": ["C++", "C/C++", "C", "C#"], "file_counts": {".c": 19, ".cpp": 3, ".h": 27, ".cs": 1}, "complexity_hint": "small", "has_enhanced_metadata": true, "metadata_version": "v3.0_enhanced", "enhancement_recommendation": null}, "code_analyzer_service": "available", "enhanced_features": ["semantic_tagging", "quality_analysis", "complexity_metrics", "optimized_context_retrieval", "pre_filtering_search_indexes", "multi_codebase_support", "single_llm_optimization"], "optimized_tools": ["get_optimized_context", "enhanced_search", "get_enhanced_stats"], "legacy_tools": ["search_code", "ask_about_code", "get_code_stats"], "management_tools": ["list_codebases", "select_codebase", "process_codebase", "delete_codebase"]}, "url": "http://home-ai-server:5002/", "method": "GET", "payload": null}, "docs": {"success": true, "status_code": 200, "response": "\n    <!DOCTYPE html>\n    <html>\n    <head>\n    <link type=\"text/css\" rel=\"stylesheet\" href=\"https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui.css\">\n    <link rel=\"shortcut icon\" href=\"https://fastapi.tiangolo.com/img/favicon.png\">\n    <title>OpenWebUI Enhanced Multi-Language Code Analyzer Tool Server - Swagger UI</title>\n    </head>\n    <body>\n    <div id=\"swagger-ui\">\n    </div>\n    <script src=\"https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui-bundle.js\"></script>\n    <!-- `SwaggerUIBundle` is now available on the page -->\n    <script>\n    const ui = SwaggerUIBundle({\n        url: '/openapi.json',\n    \"dom_id\": \"#swagger-ui\",\n\"layout\": \"BaseLayout\",\n\"deepLinking\": true,\n\"showExtensions\": true,\n\"showCommonExtensions\": true,\noauth2RedirectUrl: window.location.origin + '/docs/oauth2-redirect',\n    presets: [\n        SwaggerUIBundle.presets.apis,\n        SwaggerUIBundle.SwaggerUIStandalonePreset\n        ],\n    })\n    </script>\n    </body>\n    </html>\n    ", "url": "http://home-ai-server:5002/docs", "method": "GET", "payload": null}, "openapi": {"success": true, "status_code": 200, "response": {"openapi": "3.1.0", "info": {"title": "OpenWebUI Enhanced Multi-Language Code Analyzer Tool Server", "description": "Enhanced tool server for C/C++/C#/Python code analysis with optimized context retrieval (no double LLM calls).", "version": "3.1.0"}, "paths": {"/tools/get_optimized_context": {"post": {"summary": "Get Optimized Context", "description": "🎯 OPTIMIZED: Get context for OpenWebUI (NO LLM response generation)", "operationId": "get_optimized_context_tools_get_optimized_context_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OptimizedContextArgs"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/tools/enhanced_search": {"post": {"summary": "Enhanced Search", "description": "🔍 ENHANCED: Advanced search with semantic filtering (NO LLM calls)", "operationId": "enhanced_search_tools_enhanced_search_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnhancedCodeSearchArgs"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/tools/list_codebases": {"post": {"summary": "List Codebases", "description": "📚 List all available codebases with enhanced metadata insights", "operationId": "list_codebases_tools_list_codebases_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/tools/select_codebase": {"post": {"summary": "Select Codebase", "description": "🎯 Select a codebase with enhanced index building", "operationId": "select_codebase_tools_select_codebase_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodebaseSelectArgs"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/tools/process_codebase": {"post": {"summary": "Process Codebase", "description": "⚙️ Process a codebase with enhanced metadata extraction", "operationId": "process_codebase_tools_process_codebase_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodebaseProcessArgs"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/tools/get_enhanced_stats": {"post": {"summary": "Get Enhanced Stats", "description": "📊 Get comprehensive enhanced statistics about a codebase", "operationId": "get_enhanced_stats_tools_get_enhanced_stats_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodebaseSelectArgs"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/tools/delete_codebase": {"post": {"summary": "Delete Codebase", "description": "🗑️ Delete a specific codebase and clean up all associated data", "operationId": "delete_codebase_tools_delete_codebase_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodebaseDeleteArgs"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/tools/search_code": {"post": {"summary": "Search Code", "description": "🔍 LEGACY: Basic code search (use enhanced_search for better results)", "operationId": "search_code_tools_search_code_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeSearchArgs"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/tools/ask_about_code": {"post": {"summary": "Ask About Code", "description": "🤖 LEGACY: Ask with LLM response generation (CAUSES DOUBLE LLM CALLS - NOT RECOMMENDED)", "operationId": "ask_about_code_tools_ask_about_code_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodeQuestionArgs"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/tools/get_code_stats": {"post": {"summary": "Get Code Stats", "description": "📊 LEGACY: Basic statistics (use get_enhanced_stats for better insights)", "operationId": "get_code_stats_tools_get_code_stats_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CodebaseSelectArgs"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/": {"get": {"summary": "<PERSON> Root", "operationId": "read_root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/health": {"get": {"summary": "Health Check", "description": "Comprehensive health check with enhanced metadata detection", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/status": {"get": {"summary": "Status Check", "description": "Simple status endpoint - lightweight alternative to /health", "operationId": "status_check_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/enhanced_features": {"get": {"summary": "Get Enhanced Features", "description": "Get documentation of all enhanced features", "operationId": "get_enhanced_features_enhanced_features_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/stats": {"get": {"summary": "Get Stats", "description": "Get collection statistics (enhanced compatibility with original API)", "operationId": "get_stats_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/search": {"post": {"summary": "Search Endpoint", "description": "Direct search endpoint (enhanced compatibility with original API)", "operationId": "search_endpoint_search_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Request"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/context": {"post": {"summary": "Context Endpoint", "description": "OPTIMIZED: Context-only endpoint (NO LLM response generation)", "operationId": "context_endpoint_context_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Request"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/ask": {"post": {"summary": "Ask Endpoint", "description": "LEGACY: Ask endpoint with double LLM warning", "operationId": "ask_endpoint_ask_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Request"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"CodeQuestionArgs": {"properties": {"question": {"type": "string", "title": "Question", "description": "Question about the codebase"}, "codebase_name": {"type": "string", "title": "Codebase Name", "description": "Name of the codebase to query"}, "n_results": {"type": "integer", "maximum": 25.0, "minimum": 1.0, "title": "N Results", "description": "Number of context chunks to use (optimized for 16k context)", "default": 10}, "filter_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filter Type", "description": "Filter by type"}, "filter_language": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filter Language", "description": "Filter by language"}, "filter_file": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filter File", "description": "Filter by file pattern"}}, "type": "object", "required": ["question", "codebase_name"], "title": "CodeQuestionArgs"}, "CodeSearchArgs": {"properties": {"query": {"type": "string", "title": "Query", "description": "The search query for code"}, "codebase_name": {"type": "string", "title": "Codebase Name", "description": "Name of the codebase to search"}, "n_results": {"type": "integer", "maximum": 25.0, "minimum": 1.0, "title": "N Results", "description": "Number of results to return (optimized for 16k context)", "default": 10}, "filter_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filter Type", "description": "Filter by type (function, class, method, etc.)"}, "filter_language": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filter Language", "description": "Filter by language (27 supported: c, cpp, python, csharp, javascript, typescript, rust, java, go, sql, tcl, verilog, bash, commonlisp, elisp, scheme, lua, make, json, yaml, xml, php, perl, markdown, html, fortran, vhdl)"}, "filter_file": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filter File", "description": "Filter by file pattern"}}, "type": "object", "required": ["query", "codebase_name"], "title": "CodeSearchArgs"}, "CodebaseDeleteArgs": {"properties": {"codebase_name": {"type": "string", "title": "Codebase Name", "description": "Name of the codebase to delete"}}, "type": "object", "required": ["codebase_name"], "title": "CodebaseDeleteArgs"}, "CodebaseProcessArgs": {"properties": {"codebase_name": {"type": "string", "title": "Codebase Name", "description": "Name of the codebase to process"}, "exclude_dirs": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Exclude Dirs", "description": "Directories to exclude during processing"}}, "type": "object", "required": ["codebase_name"], "title": "CodebaseProcessArgs"}, "CodebaseSelectArgs": {"properties": {"codebase_name": {"type": "string", "title": "Codebase Name", "description": "Name of the codebase to select"}}, "type": "object", "required": ["codebase_name"], "title": "CodebaseSelectArgs"}, "EnhancedCodeSearchArgs": {"properties": {"query": {"type": "string", "title": "Query", "description": "The search query for code"}, "codebase_name": {"type": "string", "title": "Codebase Name", "description": "Name of the codebase to search"}, "n_results": {"type": "integer", "maximum": 25.0, "minimum": 1.0, "title": "N Results", "description": "Number of results to return (optimized for 16k context)", "default": 10}, "semantic_tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Semantic Tags", "description": "Filter by semantic tags (memory_management, network_operations, etc.)"}, "complexity_levels": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Complexity Levels", "description": "Filter by complexity (low, medium, high, very_high)"}, "quality_levels": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Quality Levels", "description": "Filter by quality (poor, fair, good, excellent)"}, "prefer_public_api": {"type": "boolean", "title": "Prefer Public Api", "description": "Prefer public API functions/methods", "default": false}, "prefer_documented": {"type": "boolean", "title": "Prefer Documented", "description": "Prefer well-documented code", "default": true}, "filter_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filter Type", "description": "Filter by type (function, class, method, etc.)"}, "filter_language": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filter Language", "description": "Filter by language (27 supported: c, cpp, python, csharp, javascript, typescript, rust, java, go, sql, tcl, verilog, bash, commonlisp, elisp, scheme, lua, make, json, yaml, xml, php, perl, markdown, html, fortran, vhdl)"}, "filter_file": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filter File", "description": "Filter by file pattern"}}, "type": "object", "required": ["query", "codebase_name"], "title": "EnhancedCodeSearchArgs"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "OptimizedContextArgs": {"properties": {"query": {"type": "string", "title": "Query", "description": "Query for context retrieval"}, "codebase_name": {"type": "string", "title": "Codebase Name", "description": "Name of the codebase"}, "n_results": {"type": "integer", "maximum": 25.0, "minimum": 1.0, "title": "N Results", "description": "Number of context chunks (optimized for 16k context)", "default": 10}, "context_preferences": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Context Preferences", "description": "Context preferences (semantic_tags, complexity_levels, etc.)"}}, "type": "object", "required": ["query", "codebase_name"], "title": "OptimizedContextArgs"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}, "url": "http://home-ai-server:5002/openapi.json", "method": "GET", "payload": null}}