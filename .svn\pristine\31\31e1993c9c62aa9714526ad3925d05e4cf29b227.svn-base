#!/usr/bin/env python3
"""
Test script to help debug the RAG system on home-ai-server.
This script helps you test the RAG system via API calls.
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

# Configuration for home-ai-server
RAG_SERVER_HOST = os.getenv("RAG_SERVER_HOST", "home-ai-server")
RAG_SERVER_PORT = os.getenv("RAG_SERVER_PORT", "5002")
RAG_SERVER_URL = f"http://{RAG_SERVER_HOST}:{RAG_SERVER_PORT}"

def test_server_connection():
    """Test connection to the RAG server"""
    print(f"\n🔗 Testing connection to {RAG_SERVER_URL}")
    
    try:
        # Test basic connection
        response = requests.get(f"{RAG_SERVER_URL}/", timeout=10)
        if response.status_code == 200:
            info = response.json()
            print("✅ RAG server is responding")
            print(f"   Version: {info.get('version', 'unknown')}")
            print(f"   Supported languages: {info.get('supported_languages', [])}")
            print(f"   Current codebase: {info.get('current_codebase', 'none')}")
            return True
        else:
            print(f"❌ Server responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to RAG server")
        print(f"   Make sure the server is running at {RAG_SERVER_URL}")
        print("   Check if the Docker container is up:")
        print("   docker ps | grep rag-server")
        return False
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

def test_health_endpoint():
    """Test the health endpoint"""
    print(f"\n🏥 Testing health endpoint")
    
    try:
        response = requests.get(f"{RAG_SERVER_URL}/health", timeout=10)
        if response.status_code == 200:
            health = response.json()
            print("✅ Health endpoint responding")
            print(f"   Overall status: {health.get('overall_status', 'unknown')}")
            print(f"   RAG service: {health.get('rag_service', 'unknown')}")
            print(f"   Ollama status: {health.get('ollama', 'unknown')}")
            print(f"   Embedding provider: {health.get('embedding_provider', 'unknown')}")
            print(f"   ChromaDB collections: {health.get('chromadb_collections', 0)}")
            print(f"   Available codebases: {health.get('available_codebases', 0)}")
            
            # Show any warnings or critical issues
            if health.get('warnings'):
                print(f"   ⚠️ Warnings: {health['warnings']}")
            if health.get('critical_issues'):
                print(f"   🚨 Critical issues: {health['critical_issues']}")
            if health.get('recommendations'):
                print(f"   💡 Recommendations: {health['recommendations']}")
            
            return True
        else:
            print(f"❌ Health endpoint returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health endpoint test failed: {e}")
        return False

def test_list_codebases():
    """Test listing codebases"""
    print(f"\n📚 Testing list codebases endpoint")
    
    try:
        response = requests.post(f"{RAG_SERVER_URL}/tools/list_codebases", timeout=30)
        if response.status_code == 200:
            result = response.json()
            result_text = result.get('result', '')
            print("✅ List codebases endpoint responding")
            print("   Response preview:")
            print(f"   {result_text[:300]}{'...' if len(result_text) > 300 else ''}")
            
            # Check if any codebases were found
            if 'No codebases found' in result_text:
                print("   ⚠️ No codebases found - you may need to set up source code")
                return False
            else:
                print("   ✅ Codebases detected in response")
                return True
        else:
            print(f"❌ List codebases returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ List codebases test failed: {e}")
        return False

def test_search_functionality():
    """Test basic search functionality"""
    print(f"\n🔍 Testing search functionality")
    
    # First, try to get a list of codebases
    try:
        response = requests.post(f"{RAG_SERVER_URL}/tools/list_codebases", timeout=30)
        if response.status_code != 200:
            print("❌ Cannot get codebases list for search test")
            return False
        
        result = response.json()
        result_text = result.get('result', '')
        
        if 'No codebases found' in result_text:
            print("❌ No codebases available for search test")
            return False
        
        # Extract first codebase name (basic parsing)
        lines = result_text.split('\n')
        codebase_name = None
        for line in lines:
            if line.strip().startswith('**') and ('✅' in line or '⚠️' in line or '📦' in line):
                # Extract name from markdown format
                parts = line.split('**')
                if len(parts) >= 2:
                    name_part = parts[1].strip()
                    # Remove emoji and extract clean name
                    codebase_name = ''.join(c for c in name_part if c.isalnum() or c in ['_', '-']).strip()
                    break
        
        if not codebase_name:
            print("❌ Could not extract codebase name for search test")
            return False
        
        print(f"   Testing search with codebase: {codebase_name}")
        
        # Test search
        search_payload = {
            "query": "function",
            "codebase_name": codebase_name,
            "n_results": 3
        }
        
        response = requests.post(f"{RAG_SERVER_URL}/tools/search_code", 
                               json=search_payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            result_text = result.get('result', '')
            print("✅ Search endpoint responding")
            print("   Search result preview:")
            print(f"   {result_text[:200]}{'...' if len(result_text) > 200 else ''}")
            return True
        else:
            print(f"❌ Search returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Search test failed: {e}")
        return False

def test_ai_functionality():
    """Test AI question answering"""
    print(f"\n🤖 Testing AI functionality")
    
    # Similar to search test, get a codebase first
    try:
        response = requests.post(f"{RAG_SERVER_URL}/tools/list_codebases", timeout=30)
        if response.status_code != 200:
            print("❌ Cannot get codebases list for AI test")
            return False
        
        result = response.json()
        result_text = result.get('result', '')
        
        if 'No codebases found' in result_text:
            print("❌ No codebases available for AI test")
            return False
        
        # Extract first codebase name
        lines = result_text.split('\n')
        codebase_name = None
        for line in lines:
            if line.strip().startswith('**') and ('✅' in line or '⚠️' in line or '📦' in line):
                parts = line.split('**')
                if len(parts) >= 2:
                    name_part = parts[1].strip()
                    codebase_name = ''.join(c for c in name_part if c.isalnum() or c in ['_', '-']).strip()
                    break
        
        if not codebase_name:
            print("❌ Could not extract codebase name for AI test")
            return False
        
        print(f"   Testing AI with codebase: {codebase_name}")
        
        # Test AI question
        ai_payload = {
            "question": "What functions are available in this codebase?",
            "codebase_name": codebase_name,
            "n_results": 3
        }
        
        response = requests.post(f"{RAG_SERVER_URL}/tools/ask_about_code", 
                               json=ai_payload, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            result_text = result.get('result', '')
            print("✅ AI endpoint responding")
            print("   AI response preview:")
            print(f"   {result_text[:200]}{'...' if len(result_text) > 200 else ''}")
            return True
        else:
            print(f"❌ AI endpoint returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ AI test failed: {e}")
        return False

def run_comprehensive_test():
    """Run all tests"""
    print("🧪 RAG Server Comprehensive Test")
    print("=" * 50)
    print(f"Testing server at: {RAG_SERVER_URL}")
    print("=" * 50)
    
    tests = [
        ("Server Connection", test_server_connection),
        ("Health Endpoint", test_health_endpoint),
        ("List Codebases", test_list_codebases),
        ("Search Functionality", test_search_functionality),
        ("AI Functionality", test_ai_functionality),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running test: {test_name}")
        if test_func():
            passed += 1
            print(f"✅ {test_name}: PASSED")
        else:
            print(f"❌ {test_name}: FAILED")
    
    print("\n" + "=" * 50)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! RAG server is working correctly.")
    elif passed >= total - 1:
        print("⚠️ Most tests passed. Minor issues detected.")
    else:
        print("❌ Multiple tests failed. Check server status and configuration.")
    
    print("\n💡 Next Steps:")
    if passed < total:
        print("1. Check if the RAG server container is running:")
        print("   docker ps | grep rag-server")
        print("2. Check server logs:")
        print("   docker logs openwebui-rag-server")
        print("3. Verify server configuration in docker-compose.yml")
        print("4. Test direct server access:")
        print(f"   curl {RAG_SERVER_URL}/health")
    else:
        print("1. Run collection tests:")
        print("   python test_collections.py --list-collections")
        print("2. Test specific codebase:")
        print("   python test_collections.py --collection your_codebase")
        print("3. Run performance benchmarks:")
        print("   python test_collections.py --collection your_codebase --benchmark")
    
    return passed == total

def main():
    """Main test function"""
    success = run_comprehensive_test()
    
    if not success:
        print(f"\n🔧 Troubleshooting Guide:")
        print(f"If the server is not responding, check:")
        print(f"1. Container status: docker ps")
        print(f"2. Container logs: docker logs openwebui-rag-server")
        print(f"3. Network connectivity: ping {RAG_SERVER_HOST}")
        print(f"4. Port availability: telnet {RAG_SERVER_HOST} {RAG_SERVER_PORT}")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())