#!/usr/bin/env python3
"""
Investigate how tools are executed in OpenWebUI
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def check_openwebui_config():
    """Check OpenWebUI configuration and tool settings"""
    print("🔍 Checking OpenWebUI Configuration")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test different configuration endpoints
    config_endpoints = [
        "/api/config",
        "/api/v1/config", 
        "/api/settings",
        "/api/tools/config",
        "/api/models/config"
    ]
    
    for endpoint in config_endpoints:
        print(f"\n🧪 Testing endpoint: {endpoint}")
        try:
            response = session.get(f"{OPENWEBUI_URL}{endpoint}", timeout=10)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                content_type = response.headers.get("content-type", "")
                if "json" in content_type:
                    try:
                        data = response.json()
                        print(f"   JSON response with {len(data)} keys")
                        if isinstance(data, dict):
                            print(f"   Keys: {list(data.keys())[:5]}...")  # Show first 5 keys
                    except:
                        print("   JSON parse failed")
                else:
                    print(f"   Content-Type: {content_type}")
            
        except Exception as e:
            print(f"   Error: {e}")

def test_tool_execution_modes():
    """Test different tool execution modes"""
    print("\n🔍 Testing Tool Execution Modes")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test with different function calling modes
    test_configs = [
        {
            "name": "Default mode",
            "params": {
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "select utils codebase"}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 300
            }
        },
        {
            "name": "Native function calling",
            "params": {
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "select utils codebase"}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 300,
                "function_calling": "native"
            }
        },
        {
            "name": "Auto function calling",
            "params": {
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "select utils codebase"}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 300,
                "function_calling": "auto"
            }
        },
        {
            "name": "With tool_choice",
            "params": {
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "select utils codebase"}],
                "tool_ids": ["code_analyzer_tool"],
                "tool_choice": "auto",
                "stream": False,
                "max_tokens": 300
            }
        }
    ]
    
    for config in test_configs:
        print(f"\n🧪 Testing: {config['name']}")
        print("-" * 30)
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json=config["params"],
                timeout=30
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                
                # Check for actual tool execution
                if any(indicator in content for indicator in ["Selected Enhanced Codebase", "Documents: 479", "ready_enhanced"]):
                    print(f"   ✅ SUCCESS: Tool executed!")
                    print(f"   Response: {content[:100]}...")
                elif "utils codebase" in content.lower():
                    print(f"   ⚠️ Tool-aware but generic response")
                    print(f"   Response: {content[:100]}...")
                else:
                    print(f"   ❌ Completely generic response")
                    print(f"   Response: {content[:100]}...")
                    
                # Check for tool calls in response
                message = result.get("choices", [{}])[0].get("message", {})
                if "tool_calls" in message:
                    print(f"   🔧 Tool calls found: {len(message['tool_calls'])}")
                    
            else:
                print(f"   ❌ Request failed")
                if response.status_code == 400:
                    try:
                        error = response.json()
                        print(f"   Error: {error}")
                    except:
                        print(f"   Error: {response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def check_direct_tool_call():
    """Try to call the tool function directly"""
    print("\n🔍 Testing Direct Tool Function Calls")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Try OpenAI-style function calling
    print("\n🧪 Testing OpenAI-style function calling")
    print("-" * 30)
    
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "Select the utils codebase"}],
                "tools": [
                    {
                        "type": "function",
                        "function": {
                            "name": "select_codebase",
                            "description": "Select a codebase for analysis",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "codebase_name": {
                                        "type": "string",
                                        "description": "Name of the codebase to select"
                                    }
                                },
                                "required": ["codebase_name"]
                            }
                        }
                    }
                ],
                "tool_choice": "auto",
                "stream": False,
                "max_tokens": 300
            },
            timeout=30
        )
        
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   Response keys: {list(result.keys())}")
            message = result.get("choices", [{}])[0].get("message", {})
            print(f"   Message keys: {list(message.keys())}")
            
            if "tool_calls" in message:
                print(f"   ✅ Tool calls generated: {len(message['tool_calls'])}")
                for i, tool_call in enumerate(message["tool_calls"]):
                    print(f"      Tool {i+1}: {tool_call}")
            else:
                content = message.get("content", "")
                print(f"   Content: {content[:100]}...")
        else:
            print(f"   Error: {response.text[:200]}")
            
    except Exception as e:
        print(f"   Error: {e}")

def main():
    """Main investigation function"""
    print("🔧 Deep Tool Execution Investigation")
    print("=" * 70)
    
    # Check configuration
    check_openwebui_config()
    
    # Test execution modes
    test_tool_execution_modes()
    
    # Test direct function calls
    check_direct_tool_call()
    
    print("\n📋 INVESTIGATION SUMMARY")
    print("=" * 40)
    print("This investigation helps identify:")
    print("1. How OpenWebUI handles tool execution via API")
    print("2. Whether function calling modes affect tool execution")
    print("3. If direct function calling works differently")
    print("4. Configuration differences between web and API access")

if __name__ == "__main__":
    main()
