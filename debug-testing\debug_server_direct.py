#!/usr/bin/env python3
"""
Direct server debugging script to test the code analyzer server endpoints
"""

import requests
import json

# Server configuration - try multiple URLs
POSSIBLE_URLS = [
    "http://localhost:5002",
    "http://127.0.0.1:5002",
    "http://home-ai-server.local:5002",
    "http://code-analyzer-server:5002"
]

SERVER_URL = None

def find_working_server():
    """Find a working server URL"""
    global SERVER_URL
    print("🔧 Finding working server...")

    for url in POSSIBLE_URLS:
        print(f"   Trying: {url}")
        try:
            response = requests.get(f"{url}/health", timeout=5)
            if response.status_code == 200:
                print(f"   ✅ Found working server: {url}")
                print(f"   Response: {response.text[:200]}")
                SERVER_URL = url
                return True
        except Exception as e:
            print(f"   ❌ Failed: {e}")

    print("   ❌ No working server found")
    return False

def test_server_health():
    """Test basic server health"""
    if SERVER_URL is None:
        return find_working_server()
    return True

def test_enhanced_search():
    """Test the enhanced search endpoint that we know works"""
    print("\n🔧 Testing enhanced search endpoint...")
    payload = {
        "query": "tmwmem_alloc",
        "codebase_name": "utils",
        "n_results": 3
    }
    
    try:
        response = requests.post(
            f"{SERVER_URL}/tools/enhanced_search",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            result = data.get("result", "")
            print(f"   Result length: {len(str(result))}")
            print(f"   Result preview: {str(result)[:300]}...")
            return True
        else:
            print(f"   Error: {response.text}")
            return False
    except Exception as e:
        print(f"   Error: {e}")
        return False

def test_optimized_context():
    """Test the optimized context endpoint that's failing"""
    print("\n🔧 Testing optimized context endpoint...")
    payload = {
        "query": "tmwmem_alloc",
        "codebase_name": "utils",
        "n_results": 3,
        "context_preferences": None
    }
    
    try:
        response = requests.post(
            f"{SERVER_URL}/tools/get_optimized_context",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            result = data.get("result", "")
            print(f"   Result type: {type(result)}")
            print(f"   Result length: {len(str(result))}")
            print(f"   Result content: {str(result)[:500]}...")
            
            if "No relevant code context found" in str(result):
                print("   ❌ ISSUE: No context found despite enhanced search working")
                return False
            else:
                print("   ✅ Context found!")
                return True
        else:
            print(f"   Error: {response.text}")
            return False
    except Exception as e:
        print(f"   Error: {e}")
        return False

def test_with_relaxed_preferences():
    """Test with relaxed filtering preferences"""
    print("\n🔧 Testing with relaxed preferences...")
    payload = {
        "query": "tmwmem_alloc",
        "codebase_name": "utils",
        "n_results": 5,
        "context_preferences": {
            "prefer_documented": False,
            "prefer_public_api": False,
            "filter_language": None,
            "filter_type": None
        }
    }
    
    try:
        response = requests.post(
            f"{SERVER_URL}/tools/get_optimized_context",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            result = data.get("result", "")
            print(f"   Relaxed result length: {len(str(result))}")
            
            if "No relevant code context found" in str(result):
                print("   ❌ Still no context with relaxed preferences")
                return False
            else:
                print("   ✅ Context found with relaxed preferences!")
                print(f"   Content: {str(result)[:500]}...")
                return True
        else:
            print(f"   Error: {response.text}")
            return False
    except Exception as e:
        print(f"   Error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting direct server debugging...")
    print("=" * 60)
    
    # Test 1: Server health
    if not test_server_health():
        print("❌ Server health check failed. Cannot continue.")
        return
    
    # Test 2: Enhanced search (known to work)
    if not test_enhanced_search():
        print("❌ Enhanced search failed. This is unexpected.")
        return
    
    # Test 3: Optimized context (the failing endpoint)
    if test_optimized_context():
        print("✅ Optimized context is working!")
        return
    
    # Test 4: Try with relaxed preferences
    if test_with_relaxed_preferences():
        print("✅ Issue was with filtering preferences!")
    else:
        print("❌ Issue is deeper in the optimized context logic")
    
    print("\n" + "=" * 60)
    print("🎯 CONCLUSION:")
    print("   Enhanced search works but optimized context fails.")
    print("   This suggests a bug in the filtering or formatting logic")
    print("   in the get_optimized_context method on the server.")

if __name__ == "__main__":
    main()
