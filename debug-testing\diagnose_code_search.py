#!/usr/bin/env python3
"""
Diagnose why code search isn't returning actual codebase content
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_step_by_step():
    """Test each step of the code search process"""
    print("🔍 Diagnosing Code Search Issues")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Step 1: Check status
    print("\n📊 Step 1: Check current status")
    print("-" * 40)
    
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "status"}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 500
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"✅ Status response ({len(content)} chars)")
            
            # Look for current codebase info
            lines = content.split('\n')
            for line in lines:
                if 'current' in line.lower() and ('codebase' in line.lower() or 'selection' in line.lower()):
                    print(f"📋 {line.strip()}")
        else:
            print(f"❌ Status failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Status error: {e}")
    
    # Step 2: Select codebase explicitly
    print(f"\n🎯 Step 2: Select utils codebase explicitly")
    print("-" * 40)
    
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "select codebase utils"}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 500
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"✅ Selection response ({len(content)} chars)")
            
            # Check for success indicators
            if any(indicator in content for indicator in ["✅", "Selected", "successfully", "utils"]):
                print("🎉 Selection appears successful")
            else:
                print("⚠️ Selection may have failed")
                
            print(f"Preview: {content[:200]}...")
        else:
            print(f"❌ Selection failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Selection error: {e}")
    
    # Step 3: Test direct search function
    print(f"\n🔍 Step 3: Test direct search function call")
    print("-" * 40)
    
    search_queries = [
        "search_code memory allocation utils",
        "get_code_context memory allocation",
        "smart_code_context memory allocation",
    ]
    
    for query in search_queries:
        print(f"\n🧪 Testing: '{query}'")
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": query}],
                    "tool_ids": ["code_analyzer_tool"],
                    "stream": False,
                    "max_tokens": 800
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                print(f"✅ Response ({len(content)} chars)")
                
                # Check for actual code indicators
                code_indicators = [
                    "#include", "malloc(", "free(", "void*", "struct", 
                    "FILE*", "printf", "return", "int main", "static"
                ]
                
                found_code = [ind for ind in code_indicators if ind in content]
                
                if found_code:
                    print(f"🎉 ACTUAL CODE FOUND! Indicators: {found_code[:3]}...")
                    print(f"Code preview: {content[:300]}...")
                else:
                    print("⚠️ No actual code found - likely generic response")
                    print(f"Preview: {content[:150]}...")
                    
            else:
                print(f"❌ Search failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Search error: {e}")

def test_code_analyzer_server_direct():
    """Test Code Analysis server directly to see if it has the data"""
    print(f"\n🔧 Step 4: Test Code Analysis Server Directly")
    print("-" * 40)
    
    CODE_ANALYZER_SERVER_URL = "http://home-ai-server:5002"
    
    try:
        # Test search endpoint directly
        response = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/search_code",
            json={
                "query": "memory allocation",
                "codebase_name": "utils",
                "n_results": 3
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Code Analysis server response: {data}")

            # Handle different response formats
            result = data.get('result', {})
            if isinstance(result, dict):
                results = result.get('results', [])
                print(f"✅ Code Analysis server returned {len(results)} results")

                if results:
                    print("🎉 Code Analysis server has actual code data!")
                    for i, result_item in enumerate(results[:2]):
                        content = result_item.get('content', '')[:100]
                        print(f"   Result {i+1}: {content}...")
                else:
                    print("❌ Code Analysis server returned no results")
            else:
                print(f"✅ Code Analysis server returned: {str(result)[:200]}...")
                # Check if it contains actual code indicators
                if any(indicator in str(result) for indicator in ['#include', 'malloc', 'free', 'struct', 'return']):
                    print("🎉 Code Analysis server has actual code data!")
                else:
                    print("❌ Code Analysis server returned generic content")
                
        else:
            print(f"❌ Code Analysis server search failed: {response.status_code}")
            print(f"Error: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Code Analysis server error: {e}")

def main():
    """Main diagnostic function"""
    print("🔧 Code Search Diagnostic Tool")
    print("=" * 70)
    print("Investigating why code search returns generic responses")
    
    # Run diagnostics
    test_step_by_step()
    test_code_analyzer_server_direct()
    
    print("\n📋 DIAGNOSTIC SUMMARY")
    print("=" * 50)
    print("🔍 Check the results above to identify the issue:")
    print("")
    print("✅ If Code Analysis server has data but tool doesn't:")
    print("   → Tool routing issue - queries not reaching search functions")
    print("")
    print("❌ If Code Analysis server has no data:")
    print("   → Codebase indexing issue - utils not properly indexed")
    print("")
    print("⚠️ If selection fails:")
    print("   → Codebase selection not working properly")
    
    print("\n🎯 Next Steps:")
    print("1. Check if Code Analysis server has actual utils code data")
    print("2. Verify tool is calling search functions correctly")
    print("3. Test direct tool function calls")

if __name__ == "__main__":
    main()
