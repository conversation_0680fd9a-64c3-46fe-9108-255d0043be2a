#!/usr/bin/env python3
"""
Check if the tool is actually being executed by looking for debug logs
"""

import requests
import json
import time

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_tool_execution():
    """Test if tool is being executed and check for debug logs"""
    print("🔍 Testing Tool Execution and Debug Logs")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    print("📋 Our tool should print debug logs like:")
    print("   🔧 [DEBUG] Plugin called with query: 'status'")
    print("   🔧 [DEBUG] Detected intent: codebase_management")
    print("   🔧 [DEBUG] Routing to management handler")
    print()
    
    # Test with a simple query that should trigger our tool
    test_queries = [
        "status",
        "select utils codebase", 
        "list codebases"
    ]
    
    for query in test_queries:
        print(f"🧪 Testing query: '{query}'")
        print("-" * 40)
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": query}],
                    "tool_ids": ["code_analyzer_tool"],
                    "stream": False,
                    "max_tokens": 200
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                
                print(f"   Status: {response.status_code}")
                print(f"   Response length: {len(content)}")
                print(f"   Preview: {content[:100]}...")
                
                # Check for tool-specific responses
                tool_indicators = [
                    "Selected Enhanced Codebase",
                    "server status", 
                    "version",
                    "Documents:",
                    "ready_enhanced",
                    "code_analyzer_server_url"
                ]
                
                if any(indicator in content for indicator in tool_indicators):
                    print("   ✅ Tool-specific response detected!")
                else:
                    print("   ❌ Generic response - tool not executing")
                    
            else:
                print(f"   ❌ Request failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        print()
        time.sleep(1)

def check_docker_logs():
    """Instructions for checking Docker logs"""
    print("🔍 Checking Docker Logs for Debug Output")
    print("=" * 50)
    
    print("To see if our debug logs are appearing, run these commands:")
    print()
    print("1. Check OpenWebUI container logs:")
    print("   docker logs open-webui --tail 50 -f")
    print()
    print("2. Check Code Analyzer server logs:")
    print("   docker logs code-analyzer-server --tail 50 -f")
    print()
    print("3. Look for our debug messages:")
    print("   🔧 [DEBUG] Plugin called with query: '...'")
    print("   🔧 [DEBUG] Detected intent: ...")
    print("   🔧 [DEBUG] Routing to management handler")
    print()
    print("If you DON'T see these debug logs when testing in OpenWebUI,")
    print("it means the tool is not being executed at all.")

def test_tool_registration():
    """Test if tool is properly registered"""
    print("🔍 Testing Tool Registration")
    print("=" * 40)
    
    print("Expected tool configuration:")
    print("- Tool ID: code_analyzer_tools")
    print("- Server URL: http://code-analyzer-server:5002 (internal Docker)")
    print("- Tool should be enabled for llama3:latest model")
    print()
    
    print("To verify tool registration:")
    print("1. Go to OpenWebUI → Workspace → Tools")
    print("2. Look for 'Codebase Analyzer' or similar")
    print("3. Check if it's marked as 'Active' or 'Enabled'")
    print("4. Go to Workspace → Models → llama3:latest")
    print("5. Check if the tool is enabled in the Tools section")

def main():
    """Main testing function"""
    print("🔧 Tool Execution Diagnosis")
    print("=" * 70)
    print("This will help determine if the tool is being executed at all")
    print()
    
    test_tool_execution()
    check_docker_logs()
    test_tool_registration()
    
    print("\n📊 DIAGNOSIS SUMMARY")
    print("=" * 40)
    print("If API calls return generic responses:")
    print("1. Check if debug logs appear in Docker containers")
    print("2. Verify tool is properly registered in OpenWebUI")
    print("3. Ensure tool is enabled for the model being used")
    print("4. Try re-installing the tool with updated code")
    print()
    print("The auto-tester USED TO WORK, so we can fix this!")

if __name__ == "__main__":
    main()
