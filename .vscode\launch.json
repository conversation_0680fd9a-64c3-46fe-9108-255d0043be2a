{"version": "0.2.0", "configurations": [{"name": "Debug Tool Test Harness", "type": "python", "request": "launch", "program": "${workspaceFolder}/debug-testing/tool_test_harness.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": [], "justMyCode": false, "stopOnEntry": false}, {"name": "Debug Specific Query", "type": "python", "request": "launch", "program": "${workspaceFolder}/debug-testing/debug_specific_query.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}, "args": ["get stats for utils"], "justMyCode": false, "stopOnEntry": false}]}