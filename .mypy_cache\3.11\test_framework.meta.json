{"data_mtime": 1752118886, "dep_lines": [6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["asyncio", "logging", "tempfile", "os", "pathlib", "typing", "framework_integration", "language_registry", "processing_pipeline", "chunk_system", "gpu_infrastructure", "builtins", "_asyncio", "_frozen_importlib", "_typeshed", "abc", "enum", "language_framework", "types", "typing_extensions"], "hash": "c66b2753a2ffa5dadfe146cbe512c951235e76e9", "id": "test_framework", "ignore_all": false, "interface_hash": "ea7f3fd6db78cf7e83313fb121c7849a7429c085", "mtime": 1752195349, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\test_framework.py", "plugin_data": null, "size": 20676, "suppressed": [], "version_id": "1.15.0"}