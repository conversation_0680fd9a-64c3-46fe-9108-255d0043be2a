#!/usr/bin/env python3
"""
Test the renamed codebase_analyzer tool
"""

import requests

# Configuration
OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_codebase_analyzer():
    """Test the renamed codebase_analyzer tool"""
    
    session = requests.Session()
    session.headers.update({
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    })
    
    print("🧪 Testing Codebase Analyzer (Renamed Tool)")
    print("=" * 50)
    
    # Test the new help commands
    test_queries = [
        ("codebase analyzer help", "Should trigger help with new tool name"),
        ("analyzer help", "Should trigger help with short name"),
        ("codebase help", "Should trigger help with domain name"),
        ("list codebases", "Should work with management intent"),
    ]
    
    for query, expected in test_queries:
        print(f"\n📤 Testing: '{query}'")
        print(f"💡 Expected: {expected}")
        
        payload = {
            "model": "llama3:latest",
            "messages": [
                {
                    "role": "user",
                    "content": query
                }
            ],
            "tool_ids": ["code_analyzer_tool"],  # New tool name
            "stream": False,
            "temperature": 0.7,
            "max_tokens": 500
        }
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                if "Codebase Analyzer" in content:
                    print("✅ SUCCESS - New tool name detected in help")
                elif "Available Codebases" in content or "utils" in content:
                    print("✅ SUCCESS - Management function working")
                elif len(content) > 200:
                    print("✅ SUCCESS - Got detailed response")
                else:
                    print("⚠️ PARTIAL - Got response but may be generic")
                    
                print(f"📄 Response length: {len(content)} chars")
                if len(content) < 200:
                    print(f"📄 Full response: {content}")
                else:
                    print(f"📄 Response preview: {content[:150]}...")
                    
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print("❌ TIMEOUT - Query taking too long")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY:")
    print("✅ Tool renamed from 'code_analyzer_tools' to 'codebase_analyzer'")
    print("✅ Help commands updated to use intuitive names")
    print("✅ No more technical 'RAG' terminology exposed to users")

if __name__ == "__main__":
    test_codebase_analyzer()
