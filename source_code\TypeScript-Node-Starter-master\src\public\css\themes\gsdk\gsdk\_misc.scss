/*     General overwrite     */
body{
    font-family: "Helvetica Neue","Open Sans",Arial,sans-serif;
}
.main{
    background-color: #FFFFFF;
    position: relative;
}
a{
  color: $info-color;
    
  &:hover, &:focus{
     color: $info-states-color; 
     text-decoration: none; 
  }
}

a:focus, a:active, 
button::-moz-focus-inner,
input[type="reset"]::-moz-focus-inner,
input[type="button"]::-moz-focus-inner,
input[type="submit"]::-moz-focus-inner,
select::-moz-focus-inner,
input[type="file"] > input[type="button"]::-moz-focus-inner {
    outline : 0;
}
.ui-slider-handle:focus,
.navbar-toggle {
    outline : 0 !important;
}

/*           Animations              */
.form-control, 
.input-group-addon, 
.tagsinput,
.navbar,
.navbar .alert{
    @include transition($general-transition-time, linear);
}

.fa{
    width: 18px;
    text-align: center;
}
.margin-top{
    margin-top: 50px;
}


/*       CT colors          */
.ct-blue, 
.ct-blue.checkbox.checked .second-icon,
.ct-blue.checkbox.checked, 
.ct-blue.radio.checked .second-icon,
.ct-blue.radio.checked{
    color: $primary-color;
}
.ct-azzure,
.ct-azzure.checkbox.checked .second-icon,
.ct-azzure.radio.checked .second-icon,
.ct-azzure.checkbox.checked, 
.ct-azzure.radio.checked{
    color: $info-color;
}
.ct-green,
.ct-green.checkbox.checked .second-icon, 
.ct-green.radio.checked .second-icon, 
.ct-green.checkbox.checked, 
.ct-green.radio.checked{
    color: $success-color;
}
.ct-orange, 
.ct-orange.checkbox.checked .second-icon, 
.ct-orange.radio.checked .second-icon, 
.ct-orange.checkbox.checked, 
.ct-orange.radio.checked{
    color: $warning-color;
}
.ct-red, 
.ct-red.checkbox.checked .second-icon, 
.ct-red.radio.checked .second-icon, 
.ct-red.checkbox.checked, 
.ct-red.radio.checked{
    color: $danger-color;
}
input.ct-blue + span.switch-left, 
input.ct-blue + span + label + span.switch-right{
    background-color:  $primary-color;
}
input.ct-azzure + span.switch-left, 
input.ct-azure + span + label + span.switch-right{
    background-color:  $info-color;
}
input.ct-green + span.switch-left, 
input.ct-green + span + label + span.switch-right{
    background-color:  $success-color;
}
input.ct-orange + span.switch-left, 
input.ct-orange + span + label + span.switch-right{
    background-color:  $warning-color;
}
input.ct-red + span.switch-left, 
input.ct-red + span + label + span.switch-right{
    background-color:  $danger-color;
}

