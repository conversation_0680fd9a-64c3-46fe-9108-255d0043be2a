#!/bin/bash
# OpenWebUI Code Analysis Tool Test Commands using curl
# Run these commands to test the tool via HTTP API

OPENWEBUI_URL="http://home-ai-server.local:8080"

echo "🧪 OpenWebUI Code Analysis Tool curl Test Commands"
echo "=========================================="

# Test 1: Basic status check
echo ""
echo "🔍 Test 1: Basic status check"
echo "Command:"
echo "curl -X POST \"$OPENWEBUI_URL/api/chat/completions\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"model\":\"llama3:latest\",\"messages\":[{\"role\":\"user\",\"content\":\"status\"}],\"stream\":false}'"

# Test 2: List codebases
echo ""
echo "🔍 Test 2: List codebases"
echo "Command:"
echo "curl -X POST \"$OPENWEBUI_URL/api/chat/completions\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"model\":\"llama3:latest\",\"messages\":[{\"role\":\"user\",\"content\":\"list codebases\"}],\"stream\":false}'"

# Test 3: Select codebase
echo ""
echo "🔍 Test 3: Select codebase"
echo "Command:"
echo "curl -X POST \"$OPENWEBUI_URL/api/chat/completions\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"model\":\"llama3:latest\",\"messages\":[{\"role\":\"user\",\"content\":\"select codebase utils\"}],\"stream\":false}'"

# Test 4: Code search
echo ""
echo "🔍 Test 4: Code search"
echo "Command:"
echo "curl -X POST \"$OPENWEBUI_URL/api/chat/completions\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"model\":\"llama3:latest\",\"messages\":[{\"role\":\"user\",\"content\":\"find memory allocation functions\"}],\"stream\":false}'"

# Test 5: AI analysis
echo ""
echo "🔍 Test 5: AI analysis"
echo "Command:"
echo "curl -X POST \"$OPENWEBUI_URL/api/chat/completions\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"model\":\"llama3:latest\",\"messages\":[{\"role\":\"user\",\"content\":\"How does memory management work in this codebase?\"}],\"stream\":false}'"

echo ""
echo "📋 Instructions:"
echo "1. Copy and paste each curl command into your terminal"
echo "2. Look for the tool responses in the JSON output"
echo "3. Check the 'content' field in the response"
echo ""
echo "💡 Tip: Pipe output through 'jq' for better formatting:"
echo "   curl ... | jq '.choices[0].message.content'"
