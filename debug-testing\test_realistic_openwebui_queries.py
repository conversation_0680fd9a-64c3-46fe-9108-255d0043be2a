#!/usr/bin/env python3
"""
Realistic OpenWebUI test queries based on actual utils codebase analysis
Tests the system as if executed from OpenWebUI GUI with real-world queries
"""

import asyncio
import sys
import os

# Add the current directory to Python path so we can import the tool
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the tool
from open_webui_code_analyzer_tool import Tools

class MockEventEmitter:
    """Mock event emitter that captures all status messages"""
    def __init__(self, verbose=True):
        self.verbose = verbose
        self.events = []
        self.status_messages = []
    
    async def __call__(self, event):
        """Mock event emission"""
        self.events.append(event)
        
        if event.get("type") == "status":
            data = event.get("data", {})
            description = data.get("description", "No description")
            done = data.get("done", False)
            
            self.status_messages.append({
                "description": description,
                "done": done,
                "timestamp": len(self.events)
            })
            
            if self.verbose:
                status = "✅" if done else "🔄"
                print(f"   {status} {description}")

class RealisticOpenWebUITester:
    """Test realistic queries as if from OpenWebUI GUI"""
    
    def __init__(self):
        self.tool = Tools()
        self.emitter = MockEventEmitter(verbose=True)
        
        # Configure for our test environment
        self.tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
        self.tool.valves.current_codebase = "utils"
        self.tool.valves.auto_context_injection = True
        
        print(f"🔧 Configured for OpenWebUI simulation")
        print(f"🔧 Server: {self.tool.valves.code_analyzer_server_url}")
        print(f"🔧 Codebase: {self.tool.valves.current_codebase}")
    
    def get_realistic_test_queries(self):
        """Get realistic queries based on actual codebase analysis"""
        return [
            # Memory Management Queries (should find multiple chunks)
            {
                "query": "How does memory allocation work in this codebase?",
                "category": "Memory Management",
                "expected_chunks": "3-5",
                "description": "Should find tmwmem_alloc, mbmem_alloc, mmbmem_alloc functions"
            },
            {
                "query": "Show me all memory allocation functions",
                "category": "Memory Management", 
                "expected_chunks": "4-6",
                "description": "Should find various *mem_alloc functions across modules"
            },
            {
                "query": "Find memory deallocation and cleanup code",
                "category": "Memory Management",
                "expected_chunks": "3-5", 
                "description": "Should find *mem_free functions and cleanup patterns"
            },
            
            # Error Handling Queries (should find multiple chunks)
            {
                "query": "How are errors handled in this system?",
                "category": "Error Handling",
                "expected_chunks": "4-7",
                "description": "Should find tmwdiag_error, mbdiag_response_error, tmwphysd_error"
            },
            {
                "query": "Show me error reporting functions",
                "category": "Error Handling",
                "expected_chunks": "3-6",
                "description": "Should find various *_error and *_warning functions"
            },
            {
                "query": "Find diagnostic and warning functions",
                "category": "Error Handling", 
                "expected_chunks": "2-4",
                "description": "Should find tmwdiag_warning and related diagnostic code"
            },
            
            # Specific Function Queries (should find exact matches)
            {
                "query": "tmwmem_alloc",
                "category": "Specific Functions",
                "expected_chunks": "1-3",
                "description": "Should find the exact tmwmem_alloc function"
            },
            {
                "query": "tmwdiag_error",
                "category": "Specific Functions", 
                "expected_chunks": "1-2",
                "description": "Should find tmwdiag_error function"
            },
            {
                "query": "TMWMEM_HEADER",
                "category": "Data Structures",
                "expected_chunks": "1-3",
                "description": "Should find TMWMEM_HEADER usage in memory functions"
            },
            
            # Architecture Queries (should find broader patterns)
            {
                "query": "What are the main modules in this codebase?",
                "category": "Architecture",
                "expected_chunks": "5-10",
                "description": "Should find code from tmwmem, tmwdiag, mbmem, etc."
            },
            {
                "query": "How is the TMW library structured?",
                "category": "Architecture",
                "expected_chunks": "4-8", 
                "description": "Should find various TMW* prefixed functions and structures"
            },
            
            # Network/Communication Queries
            {
                "query": "Show me socket and network code",
                "category": "Networking",
                "expected_chunks": "2-4",
                "description": "Should find socket management and network-related code"
            },
            {
                "query": "How does TCP communication work?",
                "category": "Networking",
                "expected_chunks": "1-3",
                "description": "Should find TCP socket implementation"
            },
            
            # Buffer Management
            {
                "query": "How are buffers managed?",
                "category": "Buffer Management", 
                "expected_chunks": "3-6",
                "description": "Should find buffer allocation/deallocation patterns"
            },
            {
                "query": "Find buffer allocation patterns",
                "category": "Buffer Management",
                "expected_chunks": "2-5",
                "description": "Should find allocateBuffer, deallocateBuffer functions"
            }
        ]
    
    async def test_single_query(self, test_case):
        """Test a single query and analyze results"""
        query = test_case["query"]
        category = test_case["category"]
        expected_chunks = test_case["expected_chunks"]
        description = test_case["description"]
        
        print(f"\n{'='*80}")
        print(f"🧪 TESTING: {category}")
        print(f"Query: '{query}'")
        print(f"Expected: {expected_chunks} chunks")
        print(f"Should: {description}")
        print(f"{'='*80}")
        
        # Reset tracking
        self.emitter.events = []
        self.emitter.status_messages = []
        
        try:
            # Call the main OpenWebUI entry point
            result = await self.tool.__call__(
                user_query=query,
                __event_emitter__=self.emitter
            )
            
            # Analyze results
            return self.analyze_test_result(test_case, result)
            
        except Exception as e:
            print(f"❌ EXCEPTION: {e}")
            return {
                "success": False,
                "error": str(e),
                "chunks_found": 0,
                "status_messages": len(self.emitter.status_messages)
            }
    
    def analyze_test_result(self, test_case, result):
        """Analyze the test result and extract metrics"""
        query = test_case["query"]
        expected_chunks = test_case["expected_chunks"]
        
        # Extract final status message
        final_status = "No status"
        chunks_found = 0
        
        if self.emitter.status_messages:
            final_message = self.emitter.status_messages[-1]
            final_status = final_message["description"]
            
            # Extract chunk count from status message
            import re
            chunk_match = re.search(r'(\d+) chunk', final_status)
            if chunk_match:
                chunks_found = int(chunk_match.group(1))
        
        # Analyze result content
        result_length = len(result) if result else 0
        has_context = "Context" in str(result) if result else False
        has_code = "```" in str(result) if result else False
        
        # Determine success
        success = (
            result_length > 100 and  # Has substantial content
            chunks_found > 0 and     # Found some chunks
            has_context and          # Has context sections
            "No relevant code context found" not in str(result)
        )
        
        # Print analysis
        print(f"\n📊 RESULTS:")
        print(f"   Final Status: {final_status}")
        print(f"   Chunks Found: {chunks_found}")
        print(f"   Expected: {expected_chunks}")
        print(f"   Result Length: {result_length} chars")
        print(f"   Has Context: {has_context}")
        print(f"   Has Code: {has_code}")
        print(f"   Success: {'✅' if success else '❌'}")
        
        if result and len(result) > 0:
            preview = str(result)[:200].replace('\n', ' ')
            print(f"   Preview: {preview}...")
        
        return {
            "success": success,
            "chunks_found": chunks_found,
            "expected_chunks": expected_chunks,
            "result_length": result_length,
            "has_context": has_context,
            "has_code": has_code,
            "final_status": final_status,
            "status_messages": len(self.emitter.status_messages)
        }
    
    async def run_comprehensive_test(self):
        """Run all realistic test queries"""
        print("🚀 STARTING REALISTIC OPENWEBUI QUERY TESTING")
        print("="*80)
        
        test_queries = self.get_realistic_test_queries()
        results = []
        
        for i, test_case in enumerate(test_queries, 1):
            print(f"\n[{i}/{len(test_queries)}]", end=" ")
            result = await self.test_single_query(test_case)
            result["test_case"] = test_case
            results.append(result)
            
            # Brief pause between tests
            await asyncio.sleep(0.5)
        
        # Generate summary report
        self.generate_summary_report(results)
        
        return results
    
    def generate_summary_report(self, results):
        """Generate a comprehensive summary report"""
        print(f"\n{'='*80}")
        print("📊 COMPREHENSIVE TEST SUMMARY")
        print(f"{'='*80}")
        
        total_tests = len(results)
        successful_tests = sum(1 for r in results if r["success"])
        failed_tests = total_tests - successful_tests
        
        print(f"\n🎯 OVERALL RESULTS:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Successful: {successful_tests} ({successful_tests/total_tests*100:.1f}%)")
        print(f"   Failed: {failed_tests} ({failed_tests/total_tests*100:.1f}%)")
        
        # Group by category
        categories = {}
        for result in results:
            category = result["test_case"]["category"]
            if category not in categories:
                categories[category] = []
            categories[category].append(result)
        
        print(f"\n📋 RESULTS BY CATEGORY:")
        for category, cat_results in categories.items():
            successful = sum(1 for r in cat_results if r["success"])
            total = len(cat_results)
            avg_chunks = sum(r["chunks_found"] for r in cat_results) / total if total > 0 else 0
            
            print(f"   {category}: {successful}/{total} successful, avg {avg_chunks:.1f} chunks")
        
        # Show failed tests
        failed_results = [r for r in results if not r["success"]]
        if failed_results:
            print(f"\n❌ FAILED TESTS:")
            for result in failed_results:
                query = result["test_case"]["query"]
                reason = "No chunks found" if result["chunks_found"] == 0 else "Other issue"
                print(f"   '{query}' - {reason}")
        
        # Show high-performing tests
        high_performing = [r for r in results if r["success"] and r["chunks_found"] >= 3]
        if high_performing:
            print(f"\n🏆 HIGH-PERFORMING TESTS (3+ chunks):")
            for result in high_performing:
                query = result["test_case"]["query"]
                chunks = result["chunks_found"]
                print(f"   '{query}' - {chunks} chunks")

async def main():
    """Main test execution"""
    tester = RealisticOpenWebUITester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
