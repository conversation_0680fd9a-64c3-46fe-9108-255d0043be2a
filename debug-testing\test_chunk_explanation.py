#!/usr/bin/env python3
"""
Test what the Code Analysis server returns for statistics and how it's being interpreted
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
CODE_ANALYZER_SERVER_URL = "http://home-ai-server.local:5002"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_code_analyzer_server_stats():
    """Test what the Code Analysis server actually returns for statistics"""
    print("🔍 Testing Code Analysis Server Statistics Response")
    print("=" * 60)
    
    try:
        response = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/get_codebase_stats",
            json={"codebase_name": "utils"},
            timeout=30
        )
        
        print(f"📡 Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            result = data.get('result', '')
            
            print(f"📊 Code Analysis server raw response:")
            print("-" * 40)
            print(result)
            print("-" * 40)
            
            # Check what terminology is used
            if 'chunks' in result.lower():
                print("⚠️ Code Analysis server mentions 'chunks'")
            if 'files' in result.lower():
                print("✅ Code Analysis server mentions 'files'")
            if 'documents' in result.lower():
                print("✅ Code Analysis server mentions 'documents'")
                
        else:
            print(f"❌ Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_openwebui_interpretation():
    """Test how OpenWebUI interprets the statistics"""
    print(f"\n🌐 Testing OpenWebUI Interpretation")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    queries = [
        "show details about the utils code base",
        "get statistics for utils codebase",
        "what are the metrics for utils?",
    ]
    
    for query in queries:
        print(f"\n🧪 Query: '{query}'")
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": f"select codebase utils\n{query}"}],
                    "tool_ids": ["code_analyzer_tool"],
                    "stream": False,
                    "max_tokens": 800
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                # Check for problematic interpretations
                problematic_phrases = [
                    "chunks.*indicates.*files",
                    "chunks.*number of.*files",
                    "chunks.*source code files",
                    "chunks.*modules",
                    "479.*files",
                    "479.*modules"
                ]
                
                issues = []
                for phrase in problematic_phrases:
                    import re
                    if re.search(phrase, content, re.IGNORECASE):
                        issues.append(phrase)
                
                if issues:
                    print(f"❌ Problematic interpretation found:")
                    for issue in issues:
                        print(f"   • Pattern: {issue}")
                    
                    # Show the problematic lines
                    lines = content.split('\n')
                    problem_lines = []
                    for line in lines:
                        if any(term in line.lower() for term in ['chunks', '479']):
                            problem_lines.append(line.strip())
                    
                    print(f"   Problematic lines:")
                    for line in problem_lines[:3]:
                        print(f"   → {line}")
                else:
                    print("✅ No problematic chunk interpretation found")
                
                # Show preview
                preview = content[:300] + "..." if len(content) > 300 else content
                print(f"Preview: {preview}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def suggest_fix():
    """Suggest how to fix the chunk interpretation issue"""
    print(f"\n🔧 Suggested Fix")
    print("=" * 50)
    
    print("The issue is that the LLM is misinterpreting 'chunks' as 'files'.")
    print("")
    print("📋 Correct definitions:")
    print("• Chunks = Text segments created by splitting files for vector embedding")
    print("• Files = Actual source code files (.c, .h, .cpp, etc.)")
    print("• Documents = Could refer to either chunks or files depending on context")
    print("")
    print("🎯 Solutions:")
    print("1. Add clarifying context to statistics responses")
    print("2. Use more precise terminology in tool responses")
    print("3. Educate the LLM about RAG/vector database concepts")
    print("")
    print("💡 Example improved response:")
    print("'Total indexed chunks: 479 (text segments from source files for search)'")
    print("'Unique source files: [actual file count]'")

def main():
    """Main test function"""
    print("🧪 Chunk vs Files Interpretation Test")
    print("=" * 70)
    print("Testing how 'chunks' are being misinterpreted as 'files'")
    
    # Test what Code Analysis server returns
    test_code_analyzer_server_stats()
    
    # Test how OpenWebUI interprets it
    test_openwebui_interpretation()
    
    # Suggest fix
    suggest_fix()

if __name__ == "__main__":
    main()
