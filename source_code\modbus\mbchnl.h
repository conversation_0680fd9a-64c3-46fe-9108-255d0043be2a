/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2023 */
/*****************************************************************************/
/*                                                                           */
/* This file is the property of:                                             */
/*                                                                           */
/*                       Triangle MicroWorks, Inc.                           */
/*                      Raleigh, North Carolina USA                          */
/*                       www.TriangleMicroWorks.com                          */
/*                          (919) 870-6615                                   */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*                                                                           */
/*****************************************************************************/

/* file: mbchnl.h
 * description: Modbus Channel Implementation.
 */
#ifndef MBCHNL_DEFINED
#define MBCHNL_DEFINED

#ifndef __midl
#include "tmwscl/utils/tmwappl.h"
#include "tmwscl/utils/tmwchnl.h"

#include "tmwscl/modbus/mblink.h"
#endif

/* Define support for user response callbacks. The user can specify a user
 * callback when requests are generated by calling the various request
 * functions defined in mmbbrm.h. This callback will be called to alert
 * the user to the the current status of the request. As described in
 * mmbbrm.h most requests will consist of a single request/response in
 * which case the user callback will be called once with either a success
 * status or one of the failure status codes. Some requests require multiple
 * request/response cycles in which case the user callback will get called
 * one or more times with an intermediate response status. The request is
 * complete when the response status is success or a failure code.
 */
typedef enum MBChannelRespStatus {
  MBCHNL_RESP_STATUS_SUCCESS = 0,
  MBCHNL_RESP_STATUS_FAILURE,
  MBCHNL_RESP_STATUS_TIMEOUT,
  MBCHNL_RESP_STATUS_CANCELED
} MBCHNL_RESP_STATUS;

#ifndef __midl
/* Structure which contains the information passed to the user code in the
 * user callback. 
 *  pSession - session request was issued to
 *  status - response status
 *  pTxData - pointer to original request transmit data structure returned 
 *   by the call to the mmbbrm request function which generated the request.
 *  pRxData - pointer to received response data structure or TMWDEFS_NULL if
 *   no response.
 *  responseTime - elapsed time in milliseconds between when request was sent
 *   to when response was received.
 */
typedef struct MBChannelResponseStruct {
  TMWSESN *pSession;
  MBCHNL_RESP_STATUS status;
  TMWSESN_TX_DATA *pTxData;
  TMWSESN_RX_DATA *pRxData;
  TMWTYPES_MILLISECONDS responseTime;
} MBCHNL_RESPONSE_INFO;

/* Define the response callback function
 */
typedef void (*MBCHNL_CALLBACK_FUNC)(
  void *pCallbackParam,
  MBCHNL_RESPONSE_INFO *pResponse);

/* Define MB Master transmit data structure */
#define MBCHNL_TX_DATA_BUFFER_MAX 256

typedef struct MBChannelTransDataStruct {
  /* Generic TMW data structure, must be first entry */
  TMWSESN_TX_DATA tmw;

  /* Has this request been sent */
  TMWTYPES_BOOL sent;

  /* Priority of this request */
  TMWTYPES_UCHAR priority;

  /* Internal callback */
  void *pInternalCallbackParam;
  MBCHNL_CALLBACK_FUNC pInternalCallback;

  /* User callback */
  void *pUserCallbackParam;
  MBCHNL_CALLBACK_FUNC pUserCallback;

  /* buffer to hold the message to be transmitted */
  TMWTYPES_UCHAR buffer[MBCHNL_TX_DATA_BUFFER_MAX];

} MBCHNL_TX_DATA;

#ifdef __cplusplus
extern "C" {
#endif

  /* function: mbchnl_initConfig 
   * purpose:  Initialize channel configuration data structures. The   
   *  user should call this routine to initialize these data structures
   *  and then modify the desired fields before calling mbchnl_openChannel
   *  to actually open the desired channel.
   * arguments: 
   *  pLinkConfig - pointer to link configuration structure
   *  pPhysConfig - pointer to physical configuration structure
   * returns:
   *   void
   */
  TMWDEFS_SCL_API void TMWDEFS_GLOBAL mbchnl_initConfig(
    MBLINK_CONFIG *pLinkConfig, 
    TMWPHYS_CONFIG *pPhysConfig);

  /* function: mbchnl_openChannel 
   * purpose: Open modbus channel
   * arguments:
   *  pApplContext - application context to add channel to
   *  pCallback - routine to call when events occur on this channel,
   *   can be used to track statistics on this channel.
   *  pCallbackParam - user callback parameter to pass to pCallback
   *  pLinkConfig - pointer to link layer configuration
   *  pPhysConfig - pointer to physical layer configuration
   *  pIOConfig - pointer to target I/O configuration data structure
   *   which is passed directly to the target routines implemented
   *   in tmwtarg.h/c
   *  pTmwTargConfig - TMW specific IO configuration information 
   *   which will be passed to low level IO routines in tmwtarg.h.
   * returns:
   *  pointer to new channel, this pointer is used to reference this
   *  channel in other calls to the SCL.
   */
  TMWDEFS_SCL_API TMWCHNL * TMWDEFS_GLOBAL mbchnl_openChannel(
    TMWAPPL *pApplContext,
    TMWCHNL_STAT_CALLBACK pCallback,
    void *pCallbackParam,
    const MBLINK_CONFIG *pLinkConfig, 
    const TMWPHYS_CONFIG *pPhysConfig, 
    const void *pIOConfig,
    struct TMWTargConfigStruct *pTmwTargConfig);

  /* function: mbchnl_getChannelConfig  
   * purpose:  Get current configuration from an open channel
   * arguments:
   *  pSession - session to get configuration from
   *  pLinkConfig - modbus configuration data structure to be filled in
   *  pPhysConfig - modbus configuration data structure to be filled in
   * returns:
   *  TMWDEFS_TRUE if successful
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL mbchnl_getChannelConfig(
    TMWCHNL *pChannel,
    MBLINK_CONFIG *pLinkConfig, 
    TMWPHYS_CONFIG *pPhysConfig);

  /* function: mbchnl_setChannelConfig 
   * purpose: Modify a currently open channel
   *  NOTE: normally mbchnl_getChannelConfig() will be called
   *   to get the current config, some values will be changed 
   *   and this function will be called to set the values.
   * arguments:
   *  pChannel - channel to modify
   *  pLinkConfig - modbus configuration data structure containing values to set
   *  pPhysConfig - modbus configuration data structure containing values to set
   * returns:
   *  TMWDEFS_TRUE if successful
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL mbchnl_setChannelConfig(
    TMWCHNL              *pChannel,
    const MBLINK_CONFIG  *pLinkConfig, 
    const TMWPHYS_CONFIG *pPhysConfig);

  /* function: mbchnl_modifyPhys 
   *  DEPRECATED FUNCTION, SHOULD USE mbchnl_setChannelConfig()
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL mbchnl_modifyPhys(
    TMWCHNL *pChannel, 
    const TMWPHYS_CONFIG *pPhysConfig, 
    TMWTYPES_ULONG configMask);

  /* function: mbchnl_modifyLink  
   *  DEPRECATED FUNCTION, SHOULD USE mbchnl_setChannelConfig()
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL mbchnl_modifyLink(
    TMWCHNL *pChannel, 
    const MBLINK_CONFIG *pLinkConfig, 
    TMWTYPES_ULONG configMask);

  /* function: mbchnl_closeChannel
   * purpose: Close a previously opened channel
   * arguments:
   *  pChannel - channel to close
   *   returned by mbchnl_openChannel.
   * returns:
   *  TMWDEFS_TRUE if successful
   *  TMWDEFS_FALSE otherwise
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL mbchnl_closeChannel(
    TMWCHNL *pChannel);

  /* function: mbchnl_sendMessage  
   * purpose: Send a message to the channel indicated in the Tx Data structure
   * arguments:
   *  pTxData - pointer to Tx Data structure containing the message to be sent
   *   and other information including what channel, flags and response timeout
   *   value.
   * returns:
   *  TMWDEFS_TRUE if successful, 
   *  TMWDEFS_FALSE otherwise.
   */ 
  TMWTYPES_BOOL TMWDEFS_GLOBAL mbchnl_sendMessage(
    TMWSESN_TX_DATA *pTxData);

  /* function mbchnl_afterTxCallback  
   * purpose: Send a message to the channel indicated in the Tx Data structure
   * arguments:
   *  pCallbackParam - pointer to Tx Data structure containing the message being sent
   *   and other information including what channel, flags and response timeout
   *   value.
   * returns:
   *  void
   */ 
  void TMWDEFS_CALLBACK mbchnl_afterTxCallback(
    void *pCallbackParam);

  /* function mbchnl_failedTxCallback
   * purpose: Callback function called if transmit fails because connection
   *  is not open.
   * arguments:
   *  pTxData - pointer to structure containing information about transmit
   * returns:
   *  void
   */
  void TMWDEFS_CALLBACK mbchnl_failedTxCallback(
      void *pCallbackParam);

  /* function mbchnl_removeRequest  
   * purpose: Remove the specified request from the queue
   * arguments:
   *  pTxData - pointer to Tx Data structure
   * returns:
   *  void
   */ 
  void TMWDEFS_GLOBAL mbchnl_removeRequest(
    TMWSESN_TX_DATA *pTxData);

  /* function: mbchnl_cleanupMessage 
   * purpose: Remove the requested message from the channel message queue
   *  cancelling the response timer if there is one. Also call the callback
   *  functions if there are any and deallocate the structure pointed to by
   *  pTxData.
   * arguments: 
   *  pTxData - pointer to TX Data containing message to cleanup
   *  pRxData - pointer to RX Data structure containing received message
   *  status - response status
   *  requestStatus - status of request
   * returns:
   *  void
   */
  TMWDEFS_GLOBAL void mbchnl_cleanupMessage(
    TMWSESN_TX_DATA *pTxData,
    TMWSESN_RX_DATA *pRxData,
    MBCHNL_RESP_STATUS status,
    TMWTYPES_UCHAR requestStatus);
  
  /* function: mbchnl_deleteMessages 
   * purpose: Delete all of the messages queued at the channel for the 
   *  specified session.
   * arguments:
   *  pChannel - channel to delete messages from
   *  pSession - session to delete messages for  
   * returns:
   *  void
   */
  TMWDEFS_GLOBAL void mbchnl_deleteMessages(
    TMWCHNL *pChannel,
    TMWSESN *pSession);

  /* function: mbchnl_newTxData
   * purpose: Allocate a new TX Data structure to be used for sending a message.
   * arguments:
   *  pChannel - pointer to channel to send message on
   *  pSession - pointer to session to send message on 
   * returns:
   *  pointer to TMWSESN_TX_DATA if successful
   *  TMWDEFS_NULL otherwise
   */
  TMWSESN_TX_DATA * TMWDEFS_GLOBAL mbchnl_newTxData(
    TMWCHNL *pChannel,
    TMWSESN *pSession, 
    TMWTYPES_USHORT size);

  /* function: mbchnl_freeTxData 
   * purpose: Free a TX Data structure   
   * arguments:
   *  pTxData - pointer to data structure to free
   * returns:
   *  void
   */
  void TMWDEFS_GLOBAL mbchnl_freeTxData(
    TMWSESN_TX_DATA *pTxData);
  
  /* function: mbchnl_discardInvLen 
   * purpose: Indicate that a received message was discarded because of 
   *  invalid received length 
   * arguments:
   *  pTxData - pointer to session
   * returns:
   *  void
   */
  void TMWDEFS_GLOBAL  mbchnl_discardInvLen(
    TMWSESN *pSession);

#ifdef __cplusplus
}
#endif

#endif /* __midl */

#endif /* MBCHNL_DEFINED */
