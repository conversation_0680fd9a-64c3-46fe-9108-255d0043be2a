"""
Language Processor Implementations for 27 Supported Languages
Implements specific processors for each language supported by the system.
"""

import re
import os
from pathlib import Path
from typing import Dict, List, Set, Any, Optional
from language_framework import LanguageProcessor, FileRelationship, LanguageContext, AnalysisScope
from semantic_patterns import semantic_registry
import logging

logger = logging.getLogger(__name__)

class CCppProcessor(LanguageProcessor):
    """Processor for C and C++ languages with header-implementation pair support"""
    
    def get_language_name(self) -> str:
        return "c_cpp"
    
    def get_supported_extensions(self) -> Set[str]:
        return {'.c', '.cpp', '.cxx', '.cc', '.c++', '.h', '.hpp', '.hxx', '.hh'}
    
    def get_processing_priority(self) -> int:
        return 1  # High priority
    
    def detect_file_relationships(self, file_path: str) -> List[FileRelationship]:
        """Handle C/C++ header-implementation pairs"""
        relationships = []
        path_obj = Path(file_path)
        
        if path_obj.suffix in {'.h', '.hpp', '.hxx', '.hh'}:
            # Header file - find corresponding implementation
            impl_file = self._find_implementation_file(file_path)
            if impl_file:
                relationships.append(FileRelationship(
                    file_path=file_path,
                    relationship_type="header_to_implementation",
                    related_files=[impl_file],
                    analysis_scope=AnalysisScope.HEADER_IMPLEMENTATION_PAIR,
                    metadata={"role": "header", "paired_file": impl_file}
                ))
        
        elif path_obj.suffix in {'.c', '.cpp', '.cxx', '.cc', '.c++'}:
            # Implementation file - find corresponding header
            header_file = self._find_header_file(file_path)
            if header_file:
                relationships.append(FileRelationship(
                    file_path=file_path,
                    relationship_type="implementation_to_header",
                    related_files=[header_file],
                    analysis_scope=AnalysisScope.HEADER_IMPLEMENTATION_PAIR,
                    metadata={"role": "implementation", "paired_file": header_file}
                ))
        
        return relationships
    
    def extract_context(self, file_path: str, content: str) -> LanguageContext:
        """Extract C/C++ specific context using comprehensive semantic patterns"""

        # Use comprehensive semantic patterns
        language = "cpp" if Path(file_path).suffix.lower() in {'.cpp', '.cxx', '.cc', '.c++', '.hpp', '.hxx', '.hh'} else "c"
        semantic_elements = semantic_registry.extract_semantic_elements(content, language)

        metadata = {
            "includes": semantic_elements.get("import_patterns", []),
            "namespaces": self._extract_namespaces(content),
            "classes": semantic_elements.get("class_patterns", []),
            "functions": semantic_elements.get("function_patterns", []),
            "structs": self._extract_structs(content),
            "enums": self._extract_enums(content),
            "variables": semantic_elements.get("variable_patterns", []),
            "semantic_complexity": self._calculate_semantic_complexity(semantic_elements),
            "code_patterns": self._identify_code_patterns(content, semantic_elements)
        }
        
        relationships = self.detect_file_relationships(file_path)
        
        return LanguageContext(
            language="c_cpp",
            file_path=file_path,
            content=content,
            relationships=relationships,
            metadata=metadata
        )
    
    def generate_architectural_insights(self, contexts: List[LanguageContext]) -> Dict[str, Any]:
        """Generate C/C++ specific architectural insights"""
        insights = {
            "header_implementation_pairs": [],
            "namespace_organization": {},
            "class_hierarchies": [],
            "memory_management_patterns": [],
            "design_patterns": []
        }
        
        for context in contexts:
            if context.language == "c_cpp":
                # Analyze header-implementation relationships
                for rel in context.relationships:
                    if rel.analysis_scope == AnalysisScope.HEADER_IMPLEMENTATION_PAIR:
                        insights["header_implementation_pairs"].append({
                            "header": rel.metadata.get("paired_file") if rel.metadata.get("role") == "implementation" else context.file_path,
                            "implementation": context.file_path if rel.metadata.get("role") == "implementation" else rel.metadata.get("paired_file"),
                            "interface_functions": context.metadata.get("functions", [])
                        })
        
        return insights
    
    def _find_implementation_file(self, header_path: str) -> Optional[str]:
        """Find corresponding implementation file for header"""
        path_obj = Path(header_path)
        base_name = path_obj.stem
        directory = path_obj.parent

        # Common implementation extensions
        impl_extensions = ['.cpp', '.cxx', '.cc', '.c++', '.c']

        for ext in impl_extensions:
            impl_path = directory / f"{base_name}{ext}"
            if impl_path.exists():
                return str(impl_path)

        return None

    def _find_header_file(self, impl_path: str) -> Optional[str]:
        """Find corresponding header file for implementation"""
        path_obj = Path(impl_path)
        base_name = path_obj.stem
        directory = path_obj.parent

        # Common header extensions
        header_extensions = ['.h', '.hpp', '.hxx', '.hh']

        for ext in header_extensions:
            header_path = directory / f"{base_name}{ext}"
            if header_path.exists():
                return str(header_path)

        return None
    
    def _extract_includes(self, content: str) -> List[str]:
        """Extract #include statements"""
        include_pattern = r'#include\s*[<"](.*?)[>"]'
        return re.findall(include_pattern, content)
    
    def _extract_namespaces(self, content: str) -> List[str]:
        """Extract namespace declarations"""
        namespace_pattern = r'namespace\s+(\w+)'
        return re.findall(namespace_pattern, content)
    
    def _extract_classes(self, content: str) -> List[str]:
        """Extract class declarations"""
        class_pattern = r'class\s+(\w+)'
        return re.findall(class_pattern, content)
    
    def _extract_functions(self, content: str) -> List[str]:
        """Extract function declarations/definitions with detailed patterns"""
        function_patterns = [
            r'(?:^|\n)\s*(?:static\s+)?(?:inline\s+)?(?:\w+\s+)+(\w+)\s*\([^)]*\)\s*{',  # Function definitions
            r'(?:^|\n)\s*(?:extern\s+)?(?:\w+\s+)+(\w+)\s*\([^)]*\)\s*;',  # Function declarations
            r'(?:^|\n)\s*#define\s+(\w+)\s*\(',  # Macro functions
            r'(?:^|\n)\s*typedef\s+\w+\s*\(\s*\*\s*(\w+)\s*\)',  # Function pointers
        ]

        functions = []
        for pattern in function_patterns:
            functions.extend(re.findall(pattern, content, re.MULTILINE))

        return list(set(functions))  # Remove duplicates

    def _calculate_semantic_complexity(self, semantic_elements: Dict[str, List[str]]) -> str:
        """Calculate semantic complexity based on extracted elements"""
        total_elements = sum(len(elements) for elements in semantic_elements.values())

        if total_elements < 10:
            return "low"
        elif total_elements < 50:
            return "medium"
        else:
            return "high"

    def _identify_code_patterns(self, content: str, semantic_elements: Dict[str, List[str]]) -> List[str]:
        """Identify common code patterns in C/C++"""
        patterns = []

        # Check for common patterns
        if "malloc" in content or "free" in content:
            patterns.append("manual_memory_management")

        if "new" in content and "delete" in content:
            patterns.append("cpp_memory_management")

        if "std::" in content:
            patterns.append("standard_library_usage")

        if "template" in content:
            patterns.append("template_programming")

        if "virtual" in content:
            patterns.append("polymorphism")

        if len(semantic_elements.get("class_patterns", [])) > 0:
            patterns.append("object_oriented")

        return patterns
    
    def _extract_structs(self, content: str) -> List[str]:
        """Extract struct declarations"""
        struct_pattern = r'struct\s+(\w+)'
        return re.findall(struct_pattern, content)
    
    def _extract_enums(self, content: str) -> List[str]:
        """Extract enum declarations"""
        enum_pattern = r'enum\s+(?:class\s+)?(\w+)'
        return re.findall(enum_pattern, content)

class PythonProcessor(LanguageProcessor):
    """Processor for Python language with module import support"""
    
    def get_language_name(self) -> str:
        return "python"
    
    def get_supported_extensions(self) -> Set[str]:
        return {'.py', '.pyw'}
    
    def get_processing_priority(self) -> int:
        return 1  # High priority
    
    def detect_file_relationships(self, file_path: str) -> List[FileRelationship]:
        """Handle Python module import relationships"""
        relationships = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            imports = self._extract_imports(content)
            related_files = self._resolve_import_paths(file_path, imports)
            
            if related_files:
                relationships.append(FileRelationship(
                    file_path=file_path,
                    relationship_type="module_imports",
                    related_files=related_files,
                    analysis_scope=AnalysisScope.MODULE_GROUP,
                    metadata={"imports": imports, "module_type": self._determine_module_type(content)}
                ))
        
        except Exception as e:
            logger.error(f"Error detecting Python relationships for {file_path}: {e}")
        
        return relationships
    
    def extract_context(self, file_path: str, content: str) -> LanguageContext:
        """Extract Python specific context"""
        metadata = {
            "imports": self._extract_imports(content),
            "classes": self._extract_classes(content),
            "functions": self._extract_functions(content),
            "decorators": self._extract_decorators(content),
            "module_docstring": self._extract_module_docstring(content),
            "async_functions": self._extract_async_functions(content)
        }
        
        relationships = self.detect_file_relationships(file_path)
        
        return LanguageContext(
            language="python",
            file_path=file_path,
            content=content,
            relationships=relationships,
            metadata=metadata
        )
    
    def generate_architectural_insights(self, contexts: List[LanguageContext]) -> Dict[str, Any]:
        """Generate Python specific architectural insights"""
        insights = {
            "module_structure": {},
            "import_dependencies": [],
            "class_hierarchies": [],
            "async_patterns": [],
            "framework_usage": []
        }
        
        for context in contexts:
            if context.language == "python":
                # Analyze module structure
                imports = context.metadata.get("imports", [])
                insights["import_dependencies"].extend([
                    {"from_module": context.file_path, "imports": imports}
                ])
                
                # Detect framework usage
                framework_patterns = self._detect_frameworks(imports)
                if framework_patterns:
                    insights["framework_usage"].append({
                        "module": context.file_path,
                        "frameworks": framework_patterns
                    })
        
        return insights
    
    def _extract_imports(self, content: str) -> List[str]:
        """Extract import statements"""
        import_patterns = [
            r'import\s+([\w.]+)',
            r'from\s+([\w.]+)\s+import'
        ]
        
        imports = []
        for pattern in import_patterns:
            imports.extend(re.findall(pattern, content))
        
        return list(set(imports))  # Remove duplicates
    
    def _extract_classes(self, content: str) -> List[str]:
        """Extract class definitions"""
        class_pattern = r'class\s+(\w+)'
        return re.findall(class_pattern, content)
    
    def _extract_functions(self, content: str) -> List[str]:
        """Extract function definitions"""
        func_pattern = r'def\s+(\w+)\s*\('
        return re.findall(func_pattern, content)
    
    def _extract_async_functions(self, content: str) -> List[str]:
        """Extract async function definitions"""
        async_pattern = r'async\s+def\s+(\w+)\s*\('
        return re.findall(async_pattern, content)
    
    def _extract_decorators(self, content: str) -> List[str]:
        """Extract decorator usage"""
        decorator_pattern = r'@(\w+)'
        return re.findall(decorator_pattern, content)
    
    def _extract_module_docstring(self, content: str) -> str:
        """Extract module-level docstring"""
        docstring_pattern = r'^"""(.*?)"""'
        match = re.search(docstring_pattern, content, re.DOTALL)
        return match.group(1).strip() if match else ""
    
    def _resolve_import_paths(self, file_path: str, imports: List[str]) -> List[str]:
        """Resolve import statements to actual file paths"""
        # Simplified implementation - would need more sophisticated logic
        resolved_paths = []
        base_dir = Path(file_path).parent
        
        for imp in imports:
            # Convert module path to file path
            module_path = imp.replace('.', os.sep) + '.py'
            potential_path = base_dir / module_path
            
            if potential_path.exists():
                resolved_paths.append(str(potential_path))
        
        return resolved_paths
    
    def _determine_module_type(self, content: str) -> str:
        """Determine the type of Python module"""
        if 'if __name__ == "__main__"' in content:
            return "script"
        elif re.search(r'class\s+\w+', content):
            return "class_module"
        elif re.search(r'def\s+\w+', content):
            return "function_module"
        else:
            return "data_module"
    
    def _detect_frameworks(self, imports: List[str]) -> List[str]:
        """Detect framework usage from imports"""
        framework_patterns = {
            'django': ['django'],
            'flask': ['flask'],
            'fastapi': ['fastapi'],
            'pandas': ['pandas'],
            'numpy': ['numpy'],
            'requests': ['requests'],
            'asyncio': ['asyncio']
        }
        
        detected = []
        for framework, patterns in framework_patterns.items():
            if any(pattern in imp for imp in imports for pattern in patterns):
                detected.append(framework)
        
        return detected

class CSharpProcessor(LanguageProcessor):
    """Processor for C# language with namespace and assembly support"""

    def get_language_name(self) -> str:
        return "csharp"

    def get_supported_extensions(self) -> Set[str]:
        return {'.cs'}

    def get_processing_priority(self) -> int:
        return 1  # High priority

    def detect_file_relationships(self, file_path: str) -> List[FileRelationship]:
        """Handle C# namespace and project relationships"""
        relationships = []

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            namespace = self._extract_namespace(content)
            using_statements = self._extract_using_statements(content)

            if namespace or using_statements:
                relationships.append(FileRelationship(
                    file_path=file_path,
                    relationship_type="namespace_dependencies",
                    related_files=[],  # Would need project analysis to resolve
                    analysis_scope=AnalysisScope.PACKAGE_HIERARCHY,
                    metadata={
                        "namespace": namespace,
                        "using_statements": using_statements,
                        "assembly_references": self._extract_assembly_references(content)
                    }
                ))

        except Exception as e:
            logger.error(f"Error detecting C# relationships for {file_path}: {e}")

        return relationships

    def extract_context(self, file_path: str, content: str) -> LanguageContext:
        """Extract C# specific context"""
        metadata = {
            "namespace": self._extract_namespace(content),
            "using_statements": self._extract_using_statements(content),
            "classes": self._extract_classes(content),
            "interfaces": self._extract_interfaces(content),
            "methods": self._extract_methods(content),
            "properties": self._extract_properties(content),
            "attributes": self._extract_attributes(content)
        }

        relationships = self.detect_file_relationships(file_path)

        return LanguageContext(
            language="csharp",
            file_path=file_path,
            content=content,
            relationships=relationships,
            metadata=metadata
        )

    def generate_architectural_insights(self, contexts: List[LanguageContext]) -> Dict[str, Any]:
        """Generate C# specific architectural insights"""
        insights: Dict[str, Any] = {
            "namespace_organization": {},
            "dependency_injection_patterns": [],
            "mvc_patterns": [],
            "entity_framework_usage": [],
            "async_patterns": []
        }

        for context in contexts:
            if context.language == "csharp":
                namespace = context.metadata.get("namespace")
                if namespace:
                    if namespace not in insights["namespace_organization"]:
                        insights["namespace_organization"][namespace] = []
                    insights["namespace_organization"][namespace].append(context.file_path)

        return insights

    def _extract_namespace(self, content: str) -> str:
        """Extract namespace declaration"""
        namespace_pattern = r'namespace\s+([\w.]+)'
        match = re.search(namespace_pattern, content)
        return match.group(1) if match else ""

    def _extract_using_statements(self, content: str) -> List[str]:
        """Extract using statements"""
        using_pattern = r'using\s+([\w.]+);'
        return re.findall(using_pattern, content)

    def _extract_classes(self, content: str) -> List[str]:
        """Extract class declarations"""
        class_pattern = r'(?:public|private|protected|internal)?\s*class\s+(\w+)'
        return re.findall(class_pattern, content)

    def _extract_interfaces(self, content: str) -> List[str]:
        """Extract interface declarations"""
        interface_pattern = r'(?:public|private|protected|internal)?\s*interface\s+(\w+)'
        return re.findall(interface_pattern, content)

    def _extract_methods(self, content: str) -> List[str]:
        """Extract method declarations"""
        method_pattern = r'(?:public|private|protected|internal)?\s*(?:static\s+)?(?:async\s+)?(?:\w+\s+)?(\w+)\s*\([^)]*\)\s*{'
        return re.findall(method_pattern, content)

    def _extract_properties(self, content: str) -> List[str]:
        """Extract property declarations"""
        property_pattern = r'(?:public|private|protected|internal)?\s*(?:\w+\s+)?(\w+)\s*{\s*get'
        return re.findall(property_pattern, content)

    def _extract_attributes(self, content: str) -> List[str]:
        """Extract attribute usage"""
        attribute_pattern = r'\[(\w+)'
        return re.findall(attribute_pattern, content)

    def _extract_assembly_references(self, content: str) -> List[str]:
        """Extract assembly references (simplified)"""
        # This would typically come from project files, not source code
        return []

class JavaScriptProcessor(LanguageProcessor):
    """Processor for JavaScript language"""

    def get_language_name(self) -> str:
        return "javascript"

    def get_supported_extensions(self) -> Set[str]:
        return {'.js', '.mjs'}

    def get_processing_priority(self) -> int:
        return 2  # Medium priority

    def detect_file_relationships(self, file_path: str) -> List[FileRelationship]:
        """Handle JavaScript module relationships"""
        relationships = []

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            imports = self._extract_imports(content)
            exports = self._extract_exports(content)

            if imports or exports:
                relationships.append(FileRelationship(
                    file_path=file_path,
                    relationship_type="module_dependencies",
                    related_files=[],  # Would need resolution logic
                    analysis_scope=AnalysisScope.MODULE_GROUP,
                    metadata={
                        "imports": imports,
                        "exports": exports,
                        "module_type": self._determine_module_type(content)
                    }
                ))

        except Exception as e:
            logger.error(f"Error detecting JavaScript relationships for {file_path}: {e}")

        return relationships

    def extract_context(self, file_path: str, content: str) -> LanguageContext:
        """Extract JavaScript specific context"""
        metadata = {
            "imports": self._extract_imports(content),
            "exports": self._extract_exports(content),
            "functions": self._extract_functions(content),
            "classes": self._extract_classes(content),
            "arrow_functions": self._extract_arrow_functions(content),
            "async_functions": self._extract_async_functions(content),
            "framework_usage": self._detect_frameworks(content)
        }

        relationships = self.detect_file_relationships(file_path)

        return LanguageContext(
            language="javascript",
            file_path=file_path,
            content=content,
            relationships=relationships,
            metadata=metadata
        )

    def generate_architectural_insights(self, contexts: List[LanguageContext]) -> Dict[str, Any]:
        """Generate JavaScript specific architectural insights"""
        insights: Dict[str, Any] = {
            "module_structure": {},
            "framework_usage": [],
            "async_patterns": [],
            "design_patterns": []
        }

        for context in contexts:
            if context.language == "javascript":
                frameworks = context.metadata.get("framework_usage", [])
                if frameworks:
                    insights["framework_usage"].append({
                        "file": context.file_path,
                        "frameworks": frameworks
                    })

        return insights

    def _extract_imports(self, content: str) -> List[str]:
        """Extract import statements"""
        import_patterns = [
            r'import\s+.*?\s+from\s+[\'"]([^\'"]+)[\'"]',
            r'import\s+[\'"]([^\'"]+)[\'"]',
            r'require\([\'"]([^\'"]+)[\'"]\)'
        ]

        imports = []
        for pattern in import_patterns:
            imports.extend(re.findall(pattern, content))

        return imports

    def _extract_exports(self, content: str) -> List[str]:
        """Extract export statements"""
        export_patterns = [
            r'export\s+(?:default\s+)?(?:function\s+)?(\w+)',
            r'module\.exports\s*=\s*(\w+)',
            r'exports\.(\w+)'
        ]

        exports = []
        for pattern in export_patterns:
            exports.extend(re.findall(pattern, content))

        return exports

    def _extract_functions(self, content: str) -> List[str]:
        """Extract function declarations"""
        func_pattern = r'function\s+(\w+)\s*\('
        return re.findall(func_pattern, content)

    def _extract_classes(self, content: str) -> List[str]:
        """Extract class declarations"""
        class_pattern = r'class\s+(\w+)'
        return re.findall(class_pattern, content)

    def _extract_arrow_functions(self, content: str) -> List[str]:
        """Extract arrow function assignments"""
        arrow_pattern = r'(?:const|let|var)\s+(\w+)\s*=\s*\([^)]*\)\s*=>'
        return re.findall(arrow_pattern, content)

    def _extract_async_functions(self, content: str) -> List[str]:
        """Extract async function declarations"""
        async_pattern = r'async\s+function\s+(\w+)'
        return re.findall(async_pattern, content)

    def _determine_module_type(self, content: str) -> str:
        """Determine JavaScript module type"""
        if 'export' in content or 'import' in content:
            return "es6_module"
        elif 'module.exports' in content or 'require(' in content:
            return "commonjs_module"
        else:
            return "script"

    def _detect_frameworks(self, content: str) -> List[str]:
        """Detect JavaScript framework usage"""
        framework_patterns = {
            'react': ['React', 'useState', 'useEffect', 'jsx'],
            'vue': ['Vue', 'v-if', 'v-for'],
            'angular': ['@Component', '@Injectable', 'ngOnInit'],
            'express': ['express()', 'app.get', 'app.post'],
            'node': ['process.', '__dirname', '__filename']
        }

        detected = []
        for framework, patterns in framework_patterns.items():
            if any(pattern in content for pattern in patterns):
                detected.append(framework)

        return detected
