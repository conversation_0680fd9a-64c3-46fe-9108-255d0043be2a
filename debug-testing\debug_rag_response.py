#!/usr/bin/env python3
"""
Debug what the Code Analyzer server is actually returning
"""

import requests
import json

CODE_ANALYZER_SERVER_URL = "http://home-ai-server.local:5002"

def debug_code_analyzer_response():
    """Debug the actual Code Analyzer server response format"""
    print("🔍 Debugging Code Analyzer Server Response Format")
    print("=" * 60)
    
    try:
        # Test search endpoint directly
        response = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/search_code",
            json={
                "query": "memory allocation",
                "codebase_name": "utils",
                "n_results": 3
            },
            timeout=30
        )
        
        print(f"📡 Status: {response.status_code}")
        print(f"📡 Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print(f"📄 Raw response text:")
            print("-" * 40)
            print(response.text)
            print("-" * 40)
            
            try:
                data = response.json()
                print(f"\n📊 JSON structure:")
                print(f"Type: {type(data)}")
                print(f"Keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                
                if isinstance(data, dict):
                    for key, value in data.items():
                        print(f"  {key}: {type(value)} - {str(value)[:100]}...")
                elif isinstance(data, list):
                    print(f"List with {len(data)} items")
                    if data:
                        print(f"First item: {type(data[0])} - {str(data[0])[:100]}...")
                else:
                    print(f"Unexpected type: {type(data)}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                
        else:
            print(f"❌ Error response:")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_other_endpoints():
    """Test other Code Analyzer server endpoints"""
    print(f"\n🔧 Testing Other Code Analyzer Endpoints")
    print("=" * 50)
    
    endpoints = [
        ("/health", "GET", None),
        ("/status", "GET", None),
        ("/tools/list_codebases", "POST", {}),  # This endpoint requires POST with empty JSON payload
    ]
    
    for endpoint, method, payload in endpoints:
        print(f"\n📡 Testing {method} {endpoint}")
        
        try:
            if method == "GET":
                response = requests.get(f"{CODE_ANALYZER_SERVER_URL}{endpoint}", timeout=10)
            else:
                response = requests.post(f"{CODE_ANALYZER_SERVER_URL}{endpoint}", json=payload, timeout=10)
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"JSON: {str(data)[:200]}...")
                except:
                    print(f"Text: {response.text[:200]}...")
            else:
                print(f"Error: {response.text[:200]}...")
                
        except Exception as e:
            print(f"Exception: {e}")

def main():
    """Main debug function"""
    print("🔧 Code Analyzer Server Response Debug")
    print("=" * 70)
    
    # Debug the search response format
    debug_code_analyzer_response()
    
    # Test other endpoints
    test_other_endpoints()
    
    print(f"\n📋 Summary:")
    print("This will help us understand:")
    print("1. What format the Code Analyzer server actually returns")
    print("2. Whether the search endpoint is working")
    print("3. How to properly parse the response")

if __name__ == "__main__":
    main()
