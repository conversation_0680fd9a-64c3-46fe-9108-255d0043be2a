#!/usr/bin/env python3
"""
Debug why 'get stats for utils' returns generic data instead of real codebase stats
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
CODE_ANALYZER_SERVER_URL = "http://home-ai-server.local:5002"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_code_analyzer_server_stats():
    """Test what the Code Analyzer server actually returns for stats"""
    print("🔍 Testing Code Analyzer Server Stats Directly")
    print("=" * 50)
    
    try:
        response = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/get_codebase_stats",
            json={"codebase_name": "utils"},
            timeout=30
        )
        
        print(f"📡 Code Analyzer Server Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            result = data.get('result', '')
            
            print(f"📊 Code Analyzer server response ({len(result)} chars):")
            print("-" * 40)
            print(result)
            print("-" * 40)
            
            # Check if it contains real codebase data
            real_indicators = ["chunks", "files", "languages", "C:", "utils", "479", "43"]
            found_real = [ind for ind in real_indicators if ind in result]
            
            if found_real:
                print(f"✅ Contains real codebase data: {found_real}")
                return True
            else:
                print("❌ No real codebase data found")
                return False
        else:
            print(f"❌ Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_openwebui_stats():
    """Test what OpenWebUI returns for stats"""
    print(f"\n🌐 Testing OpenWebUI Stats Response")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test different ways to request stats
    queries = [
        "get stats for utils",
        "show statistics for utils",
        "utils codebase statistics",
        "get codebase stats utils"
    ]
    
    for query in queries:
        print(f"\n🧪 Testing: '{query}'")
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": f"select codebase utils\n{query}"}],
                    "tool_ids": ["code_analyzer_tool"],
                    "stream": False,
                    "max_tokens": 1000
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                print(f"✅ Response ({len(content)} chars)")
                
                # Check for real vs generic data
                real_indicators = ["chunks", "files", "languages", "C:", "utils", "479", "43"]
                generic_indicators = ["run time", "memory usage", "Number of runs", "_add_chunk_clarification"]
                
                found_real = [ind for ind in real_indicators if ind in content]
                found_generic = [ind for ind in generic_indicators if ind in content]
                
                if found_real and not found_generic:
                    print(f"🎉 REAL DATA: {found_real}")
                elif found_generic:
                    print(f"❌ GENERIC DATA: {found_generic}")
                else:
                    print("⚠️ UNCLEAR DATA TYPE")
                
                # Show preview
                preview = content[:200] + "..." if len(content) > 200 else content
                print(f"Preview: {preview}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Main debug function"""
    print("🔧 Statistics Routing Debug")
    print("=" * 70)
    print("Debugging why stats queries return generic data instead of real codebase stats")
    
    # Test Code Analyzer server directly
    code_analyzer_working = test_code_analyzer_server_stats()
    
    # Test OpenWebUI routing
    test_openwebui_stats()
    
    # Analysis
    print(f"\n📊 ANALYSIS")
    print("=" * 50)
    
    if code_analyzer_working:
        print("✅ Code Analyzer server has real codebase statistics")
        print("❌ Issue: OpenWebUI tool not routing to correct function")
        print("🔧 Solution: Fix query routing in tool")
    else:
        print("❌ Code Analyzer server not returning real statistics")
        print("🔧 Solution: Check Code Analyzer server endpoint")
    
    print(f"\n🎯 Expected real stats should include:")
    print("• Total chunks: 479")
    print("• Unique files: 43")
    print("• Languages: C, C++, C#")
    print("• Last updated timestamp")
    print("• File type breakdown")

if __name__ == "__main__":
    main()
