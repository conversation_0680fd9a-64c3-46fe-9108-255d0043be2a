#!/usr/bin/env python3
"""
Interactive Tool Test Script
Run key test prompts and see the results
"""

import asyncio
import sys
import os

# Add the parent directory to the path so we can import the plugin
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from open_webui_code_analyzer_tool import Tools

async def run_test_prompt(tools, prompt, description):
    """Run a single test prompt and display results"""
    print(f"\n{'='*60}")
    print(f"🧪 TEST: {description}")
    print(f"💬 PROMPT: '{prompt}'")
    print(f"{'='*60}")
    
    try:
        # Simulate how OpenWebUI would call the plugin
        result = await tools.__call__(prompt)
        
        if result:
            print("✅ RESULT:")
            print("-" * 40)
            # Truncate very long results for readability
            if len(result) > 500:
                print(result[:500] + "\n... [truncated] ...")
            else:
                print(result)
        else:
            print("ℹ️  Empty result (plugin let OpenWebUI handle this query)")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")

async def run_management_tests(tools):
    """Test management and status functions"""
    print("\n🔧 MANAGEMENT & STATUS TESTS")
    print("=" * 60)
    
    tests = [
        ("status", "Basic status check"),
        ("detailed status", "Comprehensive status"),
        ("list codebases", "List available codebases"),
        ("select codebase utils", "Select a codebase"),
        ("get stats for utils", "Get codebase statistics"),
    ]
    
    for prompt, description in tests:
        await run_test_prompt(tools, prompt, description)
        await asyncio.sleep(1)  # Brief pause between tests

async def run_code_analysis_tests(tools):
    """Test code analysis functionality"""
    print("\n🔍 CODE ANALYSIS TESTS")
    print("=" * 60)
    
    tests = [
        ("find memory allocation functions", "Simple code search"),
        ("How does memory management work?", "AI analysis question"),
        ("show me all C++ classes", "Language-specific search"),
        ("what security measures are in place?", "Security analysis"),
    ]
    
    for prompt, description in tests:
        await run_test_prompt(tools, prompt, description)
        await asyncio.sleep(2)  # Longer pause for analysis

async def run_format_tests(tools):
    """Test context format selection"""
    print("\n🎨 CONTEXT FORMAT TESTS")
    print("=" * 60)
    
    tests = [
        ("find malloc", "Simple search (should be clean format)"),
        ("Analyze the complete memory management strategy across all modules and explain how it prevents leaks", "Complex analysis (should be detailed format)"),
    ]
    
    for prompt, description in tests:
        await run_test_prompt(tools, prompt, description)
        await asyncio.sleep(2)

async def run_edge_case_tests(tools):
    """Test edge cases and error handling"""
    print("\n🚨 EDGE CASE TESTS")
    print("=" * 60)
    
    tests = [
        ("select codebase nonexistent", "Invalid codebase selection"),
        ("", "Empty query"),
        ("what's the weather like?", "Non-code query"),
    ]
    
    for prompt, description in tests:
        await run_test_prompt(tools, prompt, description)
        await asyncio.sleep(1)

async def interactive_mode(tools):
    """Interactive mode for custom testing"""
    print("\n🎮 INTERACTIVE MODE")
    print("=" * 60)
    print("Enter your own prompts to test the plugin!")
    print("Type 'quit' to exit, 'help' for suggestions")
    
    suggestions = [
        "status",
        "list codebases", 
        "select codebase utils",
        "find encryption functions",
        "how does error handling work?",
        "detailed status"
    ]
    
    while True:
        try:
            prompt = input("\n💬 Your prompt: ").strip()
            
            if prompt.lower() in ['quit', 'exit', 'q']:
                break
            elif prompt.lower() == 'help':
                print("\n💡 Suggested prompts:")
                for i, suggestion in enumerate(suggestions, 1):
                    print(f"   {i}. {suggestion}")
                continue
            elif not prompt:
                continue
                
            await run_test_prompt(tools, prompt, "Interactive test")
            
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Main test function"""
    print("🚀 OpenWebUI Code Analysis Tool Interactive Test Suite")
    print("=" * 70)
    
    # Initialize the plugin
    tools = Tools()
    
    print("\nSelect test mode:")
    print("1. Quick automated tests")
    print("2. Full test suite")
    print("3. Interactive mode")
    print("4. All tests + interactive")
    
    try:
        choice = input("\nEnter choice (1-4): ").strip()
        
        if choice == "1":
            asyncio.run(run_management_tests(tools))
        elif choice == "2":
            asyncio.run(run_management_tests(tools))
            asyncio.run(run_code_analysis_tests(tools))
            asyncio.run(run_format_tests(tools))
            asyncio.run(run_edge_case_tests(tools))
        elif choice == "3":
            asyncio.run(interactive_mode(tools))
        elif choice == "4":
            asyncio.run(run_management_tests(tools))
            asyncio.run(run_code_analysis_tests(tools))
            asyncio.run(run_format_tests(tools))
            asyncio.run(run_edge_case_tests(tools))
            asyncio.run(interactive_mode(tools))
        else:
            print("Invalid choice. Running quick tests...")
            asyncio.run(run_management_tests(tools))
            
    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
    
    print("\n" + "=" * 70)
    print("🎯 TESTING COMPLETE")
    print("=" * 70)
    print("💡 For more test prompts, see: PLUGIN_TEST_PROMPTS.md")
    print("🔧 For implementation details, see: STATUS_ENDPOINT_IMPLEMENTATION.md")

if __name__ == "__main__":
    main()
