#!/usr/bin/env python3
"""
Debug a specific query step by step
"""

import sys
import os
import asyncio

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from open_webui_code_analyzer_tool import Tools

class MockValves:
    def __init__(self):
        self.CODE_ANALYZER_SERVER_URL = "http://home-ai-server.local:5002"
        self.request_timeout = 30
        self.auto_context_injection = True
        self.context_format = "detailed"
        self.current_codebase = None

async def debug_query(query: str):
    """Debug a specific query step by step"""
    print(f"🔧 DEBUGGING QUERY: '{query}'")
    print("=" * 60)
    
    # Set up tool
    tool = Tools()
    tool.valves = MockValves()
    
    # Step 1: Intent Detection
    print("Step 1: Intent Detection")
    print("-" * 30)
    intent = tool._detect_query_intent(query)
    print(f"Detected intent: {intent}")
    
    # Step 2: If management intent, test routing
    if intent == "codebase_management":
        print("\nStep 2: Management Query Routing")
        print("-" * 30)
        
        query_lower = query.lower()
        print(f"Query lowercase: '{query_lower}'")
        
        # Test each condition
        print("\nTesting conditions:")
        
        # Stats condition (should be first now)
        stats_match = 'stats' in query_lower or 'statistics' in query_lower
        print(f"  Stats condition: {stats_match}")
        
        if stats_match:
            print("  → Would go to stats logic")
            
            # Test codebase extraction
            try:
                available_codebases = await tool._get_available_codebase_names()
                print(f"  Available codebases: {available_codebases}")
                
                import re
                codebase_name = None
                for codebase in available_codebases:
                    if len(codebase) > 20:
                        continue
                    pattern = r'\b' + re.escape(codebase.lower()) + r'\b'
                    if re.search(pattern, query_lower):
                        codebase_name = codebase
                        break
                
                print(f"  Extracted codebase: {codebase_name}")
                
                if codebase_name:
                    print(f"  → Would call get_codebase_stats('{codebase_name}')")
                elif tool.valves.current_codebase:
                    print(f"  → Would use current codebase: {tool.valves.current_codebase}")
                else:
                    print("  → Would return 'No codebase selected'")
                    
            except Exception as e:
                print(f"  Error in codebase extraction: {e}")
        
        # List condition
        list_match = any(phrase in query_lower for phrase in ['list', 'show', 'available'])
        print(f"  List condition: {list_match}")
        if list_match and not stats_match:
            print("  → Would go to list_codebases")
        
        # Select condition
        select_match = any(phrase in query_lower for phrase in ['select codebase', 'choose codebase', 'switch codebase'])
        print(f"  Select condition: {select_match}")
        if select_match:
            print("  → Would go to select_codebase")
    
    # Step 3: Full execution
    print("\nStep 3: Full Execution")
    print("-" * 30)
    
    try:
        result = await tool.__call__(query)
        print(f"Final result: {str(result)[:300]}...")
    except Exception as e:
        print(f"Execution error: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main function"""
    if len(sys.argv) > 1:
        query = " ".join(sys.argv[1:])
    else:
        query = input("Enter query to debug: ")
    
    await debug_query(query)

if __name__ == "__main__":
    asyncio.run(main())
