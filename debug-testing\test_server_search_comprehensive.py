#!/usr/bin/env python3
"""
Comprehensive server testing script to isolate search issues
Tests the code analyzer server directly at home-ai-server:5002
"""

import requests
import json
import time
from typing import Dict, List, Any

# Server configuration
SERVER_URL = "http://home-ai-server.local:5002"
CODEBASE = "utils"

class ServerTester:
    def __init__(self, server_url: str):
        self.server_url = server_url
        self.session = requests.Session()
        self.session.headers.update({"Content-Type": "application/json"})
    
    def test_server_health(self) -> bool:
        """Test basic server connectivity"""
        print("🔧 Testing server health...")
        try:
            response = self.session.get(f"{self.server_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Server healthy: {data.get('version', 'unknown')}")
                print(f"   Current codebase: {data.get('current_codebase', 'none')}")
                return True
            else:
                print(f"   ❌ Server unhealthy: {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ Connection failed: {e}")
            return False
    
    def select_codebase(self, codebase_name: str) -> bool:
        """Select the codebase on the server"""
        print(f"\n🔧 Selecting codebase: {codebase_name}")
        try:
            payload = {"codebase_name": codebase_name}
            response = self.session.post(f"{self.server_url}/tools/select_codebase", 
                                       json=payload, timeout=10)
            if response.status_code == 200:
                print(f"   ✅ Codebase selected successfully")
                return True
            else:
                print(f"   ❌ Selection failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"   ❌ Selection error: {e}")
            return False
    
    def test_endpoint(self, endpoint: str, payload: Dict, test_name: str) -> Dict[str, Any]:
        """Test a specific endpoint with payload"""
        print(f"\n🔧 Testing {test_name}...")
        print(f"   Endpoint: {endpoint}")
        print(f"   Payload: {json.dumps(payload, indent=2)}")
        
        try:
            response = self.session.post(f"{self.server_url}{endpoint}", 
                                       json=payload, timeout=30)
            
            result = {
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response_time": response.elapsed.total_seconds(),
                "content_length": len(response.text),
                "error": None,
                "data": None
            }
            
            if response.status_code == 200:
                try:
                    result["data"] = response.json()
                    print(f"   ✅ Success: {response.status_code}")
                    print(f"   Response time: {result['response_time']:.2f}s")
                    print(f"   Content length: {result['content_length']} chars")
                    
                    # Extract result content if available
                    if isinstance(result["data"], dict) and "result" in result["data"]:
                        result_content = result["data"]["result"]
                        if isinstance(result_content, str):
                            if "No relevant code context found" in result_content:
                                print(f"   ⚠️  No results found")
                                result["found_results"] = False
                            else:
                                print(f"   ✅ Results found!")
                                result["found_results"] = True
                                # Show preview
                                preview = str(result_content)[:200].replace('\n', ' ')
                                print(f"   Preview: {preview}...")
                        else:
                            result["found_results"] = True
                            print(f"   ✅ Non-string result: {type(result_content)}")
                    else:
                        result["found_results"] = True
                        print(f"   ✅ Response received")
                        
                except json.JSONDecodeError:
                    result["error"] = "Invalid JSON response"
                    print(f"   ❌ Invalid JSON response")
            else:
                result["error"] = response.text
                print(f"   ❌ Failed: {response.status_code}")
                print(f"   Error: {response.text[:200]}")
            
            return result
            
        except Exception as e:
            result = {
                "status_code": None,
                "success": False,
                "error": str(e),
                "found_results": False
            }
            print(f"   ❌ Request failed: {e}")
            return result
    
    def run_comprehensive_tests(self, codebase_name: str):
        """Run comprehensive search tests"""
        print("=" * 80)
        print("🚀 COMPREHENSIVE SERVER SEARCH TESTING")
        print(f"Server: {self.server_url}")
        print(f"Codebase: {codebase_name}")
        print("=" * 80)
        
        # Test 1: Server health
        if not self.test_server_health():
            print("❌ Server health check failed. Aborting tests.")
            return
        
        # Test 2: Select codebase
        if not self.select_codebase(codebase_name):
            print("❌ Codebase selection failed. Aborting tests.")
            return
        
        # Test 3: Define test queries
        test_queries = [
            # Memory management functions we know exist
            "tmwmem_alloc",
            "tmwmem_free", 
            "tmwmem_lowAlloc",
            "TMWMEM_HEADER",
            "TMWMEM_POOL_STRUCT",
            
            # General terms
            "memory",
            "malloc", 
            "free",
            "buffer",
            "allocation",
            
            # Basic code terms
            "function",
            "struct", 
            "typedef",
            "static",
            "void",
            
            # File-specific terms
            "tmwmem.c",
            "tmwmem.h"
        ]
        
        # Test 4: Test different endpoints
        endpoints_to_test = [
            {
                "endpoint": "/tools/get_optimized_context",
                "name": "Optimized Context (Primary)",
                "payload_template": {
                    "query": "",
                    "codebase_name": codebase_name,
                    "n_results": 5,
                    "context_preferences": None
                }
            },
            {
                "endpoint": "/tools/enhanced_search", 
                "name": "Enhanced Search",
                "payload_template": {
                    "query": "",
                    "codebase_name": codebase_name,
                    "n_results": 5
                }
            },
            {
                "endpoint": "/search",
                "name": "Legacy Search",
                "payload_template": {
                    "query": "",
                    "codebase_name": codebase_name,
                    "n_results": 5
                }
            }
        ]
        
        # Run tests
        results = {}
        
        for endpoint_config in endpoints_to_test:
            endpoint = endpoint_config["endpoint"]
            endpoint_name = endpoint_config["name"]
            results[endpoint_name] = {}
            
            print(f"\n{'='*60}")
            print(f"TESTING ENDPOINT: {endpoint_name}")
            print(f"{'='*60}")
            
            for query in test_queries:
                payload = endpoint_config["payload_template"].copy()
                payload["query"] = query
                
                test_name = f"{endpoint_name} - '{query}'"
                result = self.test_endpoint(endpoint, payload, test_name)
                results[endpoint_name][query] = result
                
                # Small delay between requests
                time.sleep(0.5)
        
        # Test 5: Summary report
        self.print_summary_report(results, test_queries)
    
    def print_summary_report(self, results: Dict, test_queries: List[str]):
        """Print a comprehensive summary report"""
        print("\n" + "="*80)
        print("📊 COMPREHENSIVE TEST SUMMARY")
        print("="*80)
        
        # Count successes per endpoint
        for endpoint_name, endpoint_results in results.items():
            print(f"\n🔍 {endpoint_name}:")
            
            successful_queries = []
            failed_queries = []
            found_results = []
            no_results = []
            
            for query, result in endpoint_results.items():
                if result["success"]:
                    successful_queries.append(query)
                    if result.get("found_results", False):
                        found_results.append(query)
                    else:
                        no_results.append(query)
                else:
                    failed_queries.append(query)
            
            print(f"   ✅ Successful requests: {len(successful_queries)}/{len(test_queries)}")
            print(f"   🎯 Found results: {len(found_results)}/{len(test_queries)}")
            print(f"   ⚠️  No results: {len(no_results)}/{len(test_queries)}")
            print(f"   ❌ Failed requests: {len(failed_queries)}/{len(test_queries)}")
            
            if found_results:
                print(f"   📋 Queries that found results: {', '.join(found_results)}")
            if no_results:
                print(f"   📋 Queries with no results: {', '.join(no_results[:5])}{'...' if len(no_results) > 5 else ''}")
            if failed_queries:
                print(f"   📋 Failed queries: {', '.join(failed_queries)}")
        
        # Overall conclusion
        print(f"\n🎯 CONCLUSION:")
        
        # Check if any endpoint found memory management functions
        memory_functions = ["tmwmem_alloc", "tmwmem_free", "tmwmem_lowAlloc", "TMWMEM_HEADER"]
        found_memory_functions = False
        
        for endpoint_name, endpoint_results in results.items():
            for func in memory_functions:
                if func in endpoint_results and endpoint_results[func].get("found_results", False):
                    found_memory_functions = True
                    break
        
        if found_memory_functions:
            print("   ✅ Server can find memory management functions")
            print("   🔍 Issue is likely in OpenWebUI tool communication")
        else:
            print("   ❌ Server cannot find memory management functions")
            print("   🔍 Issue is in server indexing/search functionality")
        
        print("\n" + "="*80)

def main():
    """Main test execution"""
    tester = ServerTester(SERVER_URL)
    tester.run_comprehensive_tests(CODEBASE)

if __name__ == "__main__":
    main()
