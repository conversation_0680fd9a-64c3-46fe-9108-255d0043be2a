#!/usr/bin/env python3
"""
Test the AI functionality that was previously failing
"""

import requests
import json

CODE_ANALYZER_SERVER_URL = "http://home-ai-server:5002"

def test_ai_questions():
    """Test AI questions on different codebases"""
    print("🤖 Testing AI Question Functionality")
    print("=" * 50)
    
    test_cases = [
        {
            "codebase": "utils",
            "question": "What encryption functions are available?",
            "expected_keywords": ["crypto", "encrypt", "function"]
        },
        {
            "codebase": "modbus",
            "question": "How does memory management work?",
            "expected_keywords": ["memory", "alloc", "free"]
        },
        {
            "codebase": "z80emu",
            "question": "What are the main classes and their purposes?",
            "expected_keywords": ["class", "Z80", "emulator"]
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}/3: {test_case['codebase']}")
        print(f"❓ Question: {test_case['question']}")
        
        try:
            payload = {
                "question": test_case["question"],
                "codebase_name": test_case["codebase"],
                "n_results": 3
            }
            
            response = requests.post(
                f"{CODE_ANALYZER_SERVER_URL}/tools/ask_about_code",
                json=payload,
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                result_text = result.get('result', '')
                
                # Check if it's an error response
                if "Collection" in result_text and "does not exist" in result_text:
                    print("❌ Collection error (old issue)")
                    results.append({
                        "codebase": test_case["codebase"],
                        "success": False,
                        "error": "Collection not found"
                    })
                elif "Error" in result_text and "asking about code" in result_text:
                    print("❌ AI processing error")
                    results.append({
                        "codebase": test_case["codebase"],
                        "success": False,
                        "error": "AI processing failed"
                    })
                else:
                    print("✅ AI responded successfully")
                    print(f"   Response length: {len(result_text)} characters")
                    
                    # Check for expected keywords
                    found_keywords = []
                    for keyword in test_case["expected_keywords"]:
                        if keyword.lower() in result_text.lower():
                            found_keywords.append(keyword)
                    
                    print(f"   Keywords found: {found_keywords}")
                    
                    # Show a preview of the response
                    preview = result_text[:200].replace('\n', ' ')
                    print(f"   Preview: {preview}...")
                    
                    results.append({
                        "codebase": test_case["codebase"],
                        "success": True,
                        "response_length": len(result_text),
                        "keywords_found": found_keywords,
                        "keywords_expected": test_case["expected_keywords"]
                    })
            else:
                print(f"❌ HTTP {response.status_code}")
                results.append({
                    "codebase": test_case["codebase"],
                    "success": False,
                    "error": f"HTTP {response.status_code}"
                })
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            results.append({
                "codebase": test_case["codebase"],
                "success": False,
                "error": str(e)
            })
    
    return results

def test_search_functionality():
    """Test search functionality"""
    print("\n🔍 Testing Search Functionality")
    print("=" * 50)
    
    search_tests = [
        {"codebase": "utils", "query": "memory allocation"},
        {"codebase": "modbus", "query": "protocol"},
        {"codebase": "z80emu", "query": "register"}
    ]
    
    for test in search_tests:
        print(f"\n🔍 Searching '{test['query']}' in {test['codebase']}")
        
        try:
            payload = {
                "query": test["query"],
                "codebase_name": test["codebase"],
                "n_results": 3
            }
            
            response = requests.post(
                f"{CODE_ANALYZER_SERVER_URL}/tools/search_code",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                result_text = result.get('result', '')
                
                # Count results
                result_count = result_text.count('**Result')
                print(f"✅ Found {result_count} results")
                
                if result_count > 0:
                    # Show first result preview
                    lines = result_text.split('\n')
                    for line in lines:
                        if '**File**:' in line:
                            print(f"   First result: {line.strip()}")
                            break
                else:
                    print("   No results found")
            else:
                print(f"❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

def main():
    print("🚀 AI and Search Functionality Test")
    print("=" * 60)
    
    # Test AI questions
    ai_results = test_ai_questions()
    
    # Test search
    test_search_functionality()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    
    successful_ai = sum(1 for r in ai_results if r["success"])
    total_ai = len(ai_results)
    
    print(f"🤖 AI Questions: {successful_ai}/{total_ai} successful")
    
    if successful_ai == total_ai:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Vector store creation is working")
        print("✅ AI functionality is working")
        print("✅ Search functionality is working")
        print("\n💡 The collection ID mismatch issue has been resolved!")
    else:
        print("⚠️ Some tests failed:")
        for result in ai_results:
            if not result["success"]:
                print(f"   ❌ {result['codebase']}: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    main()
