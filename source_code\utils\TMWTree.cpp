/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2023 */
/*****************************************************************************/
/*                                                                           */
/* This file is the property of:                                             */
/*                                                                           */
/*                       Triangle MicroWorks, Inc.                           */
/*                      Raleigh, North Carolina USA                          */
/*                       www.TriangleMicroWorks.com                          */
/*                          (919) 870-6615                                   */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*                                                                           */
/*****************************************************************************/

/* file: tmwtree.c
 * description: Implementation of a red-black balanced binary tree
 */


#include "tmwscl/utils/tmwtarg.h"
#include "tmwscl/utils/TMWTree.h"


#define DEBUG_ASSERT 0

static void TMWDEFS_LOCAL _leftRotate(TMWTREE_TREE* tree, TMWTREE_NODE* x);
static void TMWDEFS_LOCAL _rightRotate(TMWTREE_TREE* tree, TMWTREE_NODE* y);
//static void TMWDEFS_LOCAL _inorderTreePrint(TMWTREE_TREE* tree, TMWTREE_NODE* x);
static void TMWDEFS_LOCAL _destHelper(TMWTREE_TREE* tree, TMWTREE_NODE* x);
static void TMWDEFS_LOCAL _deleteFixUp(TMWTREE_TREE* tree, TMWTREE_NODE* x);
static void TMWDEFS_LOCAL _treeInsertHelp(TMWTREE_TREE* tree, TMWTREE_NODE* z);

/***********************************************************************/
/*  function :  _leftRotate */
/**/
/*  INPUTS:  This takes a tree so that it can access the appropriate */
/*           root and nil pointers, and the node to rotate on. */
/**/
/*  OUTPUT:  None */
/**/
/*  Modifies Input: tree, x */
/**/
/*  EFFECTS:  Rotates as described in _Introduction_To_Algorithms by */
/*            Cormen, Leiserson, Rivest(Chapter 14).  Basically this */
/*            makes the parent of x be to the left of x, x the parent of */
/*            its parent before the rotation and fixes other pointers */
/*            accordingly. */
/***********************************************************************/
static void TMWDEFS_LOCAL _leftRotate(TMWTREE_TREE* tree, TMWTREE_NODE* x) 
{
  TMWTREE_NODE* y;
  TMWTREE_NODE* nil = tree->m_pTreeNodeNil;
  
  /*  Originally wrote this function to used the sentinel for */
  /*  nil to avoid checking for nil.  However this introduces a */
  /*  very subtle bug because sometimes this function modifies */
  /*  the parent pointer of nil.  This can be a problem if a */
  /*  function which calls _leftRotate also uses the nil sentinel */
  /*  and expects the nil sentinel's parent pointer to be unchanged */
  /*  after calling this function.  For example, when _deleteFixUp */
  /*  calls _leftRotate it expects the parent pointer of nil to be */
  /*  unchanged. */
  
  y = x->m_pRightNode;
  x->m_pRightNode = y->m_pLeftNode;
  
  if (y->m_pLeftNode != nil)
  {
    y->m_pLeftNode->m_pParentNode = x; /* used to use sentinel here */
  }
  /* and do an unconditional assignment instead of testing for nil */
  
  y->m_pParentNode = x->m_pParentNode;   
  
  /* instead of checking if x->m_pParentNode is the root as in the book, we */
  /* count on the root sentinel to implicitly take care of this case */
  if (x == x->m_pParentNode->m_pLeftNode)
  {
    x->m_pParentNode->m_pLeftNode = y;
  }
  else 
  {
    x->m_pParentNode->m_pRightNode = y;
  }
  y->m_pLeftNode = x;
  x->m_pParentNode = y;
  
#if DEBUG_ASSERT
  Assert(!tree->m_pTreeNodeNil->red, "nil not red in _leftRotate");
#endif
}


/***********************************************************************/
/*  function :  _rightRotate */
/**/
/*  INPUTS:  This takes a tree so that it can access the appropriate */
/*           root and nil pointers, and the node to rotate on. */
/**/
/*  OUTPUT:  None */
/**/
/*  Modifies Input?: tree, y */
/**/
/*  EFFECTS:  Rotates as described in _Introduction_To_Algorithms by */
/*            Cormen, Leiserson, Rivest(Chapter 14).  Basically this */
/*            makes the parent of x be to the left of x, x the parent of */
/*            its parent before the rotation and fixes other pointers */
/*            accordingly. */
/***********************************************************************/
static void TMWDEFS_LOCAL _rightRotate(TMWTREE_TREE* tree, TMWTREE_NODE* y) 
{
  TMWTREE_NODE* x;
  TMWTREE_NODE* nil = tree->m_pTreeNodeNil;
  
  /*  I originally wrote this function to use the sentinel for */
  /*  nil to avoid checking for nil.  However this introduces a */
  /*  very subtle bug because sometimes this function modifies */
  /*  the parent pointer of nil.  This can be a problem if a */
  /*  function which calls _leftRotate also uses the nil sentinel */
  /*  and expects the nil sentinel's parent pointer to be unchanged */
  /*  after calling this function.  For example, when _deleteFixUp */
  /*  calls _leftRotate it expects the parent pointer of nil to be */
  /*  unchanged. */
  
  x = y->m_pLeftNode;
  y->m_pLeftNode = x->m_pRightNode;
  
  if (nil != x->m_pRightNode)
  {
    x->m_pRightNode->m_pParentNode = y; /*used to use sentinel here */
  }
  /* and do an unconditional assignment instead of testing for nil */
  
  /* instead of checking if x->m_pParentNode is the root as in the book, we */
  /* count on the root sentinel to implicitly take care of this case */
  x->m_pParentNode = y->m_pParentNode;
  if (y == y->m_pParentNode->m_pLeftNode)
  {
    y->m_pParentNode->m_pLeftNode = x;
  }
  else 
  {
    y->m_pParentNode->m_pRightNode = x;
  }
  x->m_pRightNode = y;
  y->m_pParentNode = x;
  
#if DEBUG_ASSERT
  Assert(!tree->m_pTreeNodeNil->red, "nil not red in _rightRotate");
#endif
}

/* SPM what about empty tree? */
TMWTREE_NODE * TMWDEFS_GLOBAL tmwtree_getFirst(TMWTREE_TREE *pTree)
{ 
  TMWTREE_NODE* x = pTree->m_pTreeNodeRoot->m_pLeftNode; 
  TMWTREE_NODE* nil = pTree->m_pTreeNodeNil;
  TMWTREE_NODE* y = TMWDEFS_NULL;
  while(x != nil)
  {
    y = x;
    x = x->m_pLeftNode;
  }
  return(y);
}

/* SPM what about empty tree? */
TMWTREE_NODE * TMWDEFS_GLOBAL tmwtree_getLast(TMWTREE_TREE *pTree)
{ 
  TMWTREE_NODE* x = pTree->m_pTreeNodeRoot->m_pLeftNode; 
  TMWTREE_NODE* nil = pTree->m_pTreeNodeNil;
  TMWTREE_NODE* y = TMWDEFS_NULL;
  while(x != nil)
  {
    y = x;
    x = x->m_pRightNode;
  }
  return(y);
}
#if 0
/***********************************************************************/
/*  function :  _inorderTreePrint */
/**/
/*    INPUTS:  tree is the tree to print and x is the current inorder node */
/**/
/*    OUTPUT:  none  */
/**/
/*    EFFECTS:  This function recursively prints the nodes of the tree */
/*              inorder using the PrintKey and PrintInfo functions. */
/**/
/*    Modifies Input: none */
/**/
/*    Note:    This function should only be called from tmwtree_print */
/***********************************************************************/
static void TMWDEFS_LOCAL _inorderTreePrint(TMWTREE_TREE* tree, TMWTREE_NODE* x) 
{
  TMWTREE_NODE* nil = tree->m_pTreeNodeNil;
  TMWTREE_NODE* root = tree->m_pTreeNodeRoot;
  if (x != tree->m_pTreeNodeNil)
  {
    _inorderTreePrint(tree, x->m_pLeftNode);
    printf("info=");
    tree->PrintInfo(x->info);
    printf("  key="); 
    tree->PrintKey(x->key);
    printf("  l->key=");
    if (x->m_pLeftNode == nil)
    {
      printf("NULL");
    }
    else 
    {
      tree->PrintKey(x->m_pLeftNode->key);
    }
    printf("  r->key=");
    if (x->m_pRightNode == nil)
    {
      printf("NULL");
    }
    else 
    {
      tree->PrintKey(x->m_pRightNode->key);
    }
    printf("  p->key=");
    if (x->m_pParentNode == root)
    {
      printf("NULL");
    }
    else 
    {
      tree->PrintKey(x->m_pParentNode->key);
    }
    printf("  red=%i\n", x->red);
    _inorderTreePrint(tree, x->m_pRightNode);
  }
}
#endif


/***********************************************************************/
/*  function :  _destHelper */
/**/
/*    INPUTS:  tree is the tree to destroy and x is the current node */
/**/
/*    OUTPUT:  none  */
/**/
/*    EFFECTS:  This function recursively destroys the nodes of the tree */
/*              postorder using the DestroyKey and DestroyInfo functions. */
/**/
/*    Modifies Input: tree, x */
/**/
/*    Note:    This function should only be called by tmwtree_destroy */
/***********************************************************************/
static void TMWDEFS_LOCAL _destHelper(TMWTREE_TREE* tree, TMWTREE_NODE* x) 
{
  TMWTREE_NODE* nil = tree->m_pTreeNodeNil;
  if (x != nil)
  {
    _destHelper(tree, x->m_pLeftNode);
    _destHelper(tree, x->m_pRightNode);
    tree->DestroyInfo(x->info);
    tmwtarg_free(x);
  }
}

/***********************************************************************/
/*  function :  _deleteFixUp */
/**/
/*    INPUTS:  tree is the tree to fix and x is the child of the spliced */
/*             out node in RBTreeDelete. */
/**/
/*    OUTPUT:  none */
/**/
/*    EFFECT:  Performs rotations and changes colors to restore red - black */
/*             properties after a node is deleted */
/**/
/*    Modifies Input: tree, x */
/**/
/*    The algorithm from this function is from _Introduction_To_Algorithms_ */
/***********************************************************************/
static void TMWDEFS_LOCAL _deleteFixUp(TMWTREE_TREE* tree, TMWTREE_NODE* x) 
{
  TMWTREE_NODE* root = tree->m_pTreeNodeRoot->m_pLeftNode;
  TMWTREE_NODE* w;
  
  while ((!x->red) && (root != x)) 
  {
    if (x == x->m_pParentNode->m_pLeftNode)
    {
      w = x->m_pParentNode->m_pRightNode;
      if (w->red)
      {
        w->red = 0;
        x->m_pParentNode->red = 1;
        _leftRotate(tree, x->m_pParentNode);
        w = x->m_pParentNode->m_pRightNode;
      }
      if ((!w->m_pRightNode->red) && (!w->m_pLeftNode->red))
      { 
        w->red = 1;
        x = x->m_pParentNode;
      }
      else 
      {
        if (!w->m_pRightNode->red)
        {
          w->m_pLeftNode->red = 0;
          w->red = 1;
          _rightRotate(tree, w);
          w = x->m_pParentNode->m_pRightNode;
        }
        w->red = x->m_pParentNode->red;
        x->m_pParentNode->red = 0;
        w->m_pRightNode->red = 0;
        _leftRotate(tree, x->m_pParentNode);
        x = root; /* this is to exit while loop */
      }
    }
    else 
    { /* the code below is has left and right switched from above */
      w = x->m_pParentNode->m_pLeftNode;
      if (w->red)
      {
        w->red = 0;
        x->m_pParentNode->red = 1;
        _rightRotate(tree, x->m_pParentNode);
        w = x->m_pParentNode->m_pLeftNode;
      }
      if ((!w->m_pRightNode->red) && (!w->m_pLeftNode->red))
      { 
        w->red = 1;
        x = x->m_pParentNode;
      }
      else 
      {
        if (!w->m_pLeftNode->red)
        {
          w->m_pRightNode->red = 0;
          w->red = 1;
          _leftRotate(tree, w);
          w = x->m_pParentNode->m_pLeftNode;
        }
        w->red = x->m_pParentNode->red;
        x->m_pParentNode->red = 0;
        w->m_pLeftNode->red = 0;
        _rightRotate(tree, x->m_pParentNode);
        x = root; /* this is to exit while loop */
      }
    }
  }
  x->red = 0;
  
#if DEBUG_ASSERT
  Assert(!tree->m_pTreeNodeNil->red, "nil not black in _deleteFixUp");
#endif
}


/***********************************************************************/
/*  function :  _treeInsertHelp  */
/**/
/*  INPUTS:  tree is the tree to insert into and z is the node to insert */
/**/
/*  OUTPUT:  none */
/**/
/*  Modifies Input:  tree, z */
/**/
/*  EFFECTS:  Inserts z into the tree as if it were a regular binary tree */
/*            using the algorithm described in _Introduction_To_Algorithms_ */
/*            by Cormen et al.  This function is only intended to be called */
/*            by the tmwtree_insert function and not by the user */
/***********************************************************************/
static void TMWDEFS_LOCAL _treeInsertHelp(TMWTREE_TREE* tree, TMWTREE_NODE* z) 
{
  /*  This function should only be called by tmwtree_insert */
  TMWTREE_NODE* x;
  TMWTREE_NODE* y;
  TMWTREE_NODE* nil = tree->m_pTreeNodeNil;
  
  z->m_pLeftNode = z->m_pRightNode = nil;
  y = tree->m_pTreeNodeRoot;
  x = tree->m_pTreeNodeRoot->m_pLeftNode;
  while (x != nil) 
  {
    y = x;
    if (x->key > z->key)
    {
      x = x->m_pLeftNode;
    }
    else 
    { /* x, key <= z.key */
      x = x->m_pRightNode;
    }
  }
  z->m_pParentNode = y;
  if ((y == tree->m_pTreeNodeRoot) || (y->key >z->key))
  {
    /* y.key > z.key */
    y->m_pLeftNode = z;
  }
  else 
  {
    y->m_pRightNode = z;
  }
  
#if DEBUG_ASSERT
  Assert(!tree->m_pTreeNodeNil->red, "nil not red in _treeInsertHelp");
#endif
}

/*  function :  tmwtree_create */
TMWTREE_TREE * TMWDEFS_GLOBAL tmwtree_create(
	void(*InfoDestFunc)(void*)) 
{
  TMWTREE_TREE *pNewTree;
  TMWTREE_NODE *pTempNode;
  
  pNewTree = (TMWTREE_TREE*) tmwtarg_alloc(sizeof(TMWTREE_TREE));
  if(pNewTree == TMWDEFS_NULL)
  {
    return(TMWDEFS_NULL);
  }

  pNewTree->quantity = 0;
  pNewTree->currentIndex = 0;
  pNewTree->pCurrentNode = TMWDEFS_NULL;
  pNewTree->DestroyInfo = InfoDestFunc;
  
  /*  see the comment in the TMWTREE_TREE structure in red_black_tree.h */
  /*  for information on nil and root */
  pTempNode = pNewTree->m_pTreeNodeNil = (TMWTREE_NODE*) tmwtarg_alloc(sizeof(TMWTREE_NODE));
  if(pTempNode == TMWDEFS_NULL)
  {
    tmwtarg_free(pNewTree);
    return(TMWDEFS_NULL);
  }
  pTempNode->m_pParentNode = pTempNode->m_pLeftNode = pTempNode->m_pRightNode = pTempNode;
  pTempNode->red = 0;
  pTempNode->key = 0;
  pTempNode->info = TMWDEFS_NULL;
  pTempNode = pNewTree->m_pTreeNodeRoot = (TMWTREE_NODE*) tmwtarg_alloc(sizeof(TMWTREE_NODE));
  if(pTempNode == TMWDEFS_NULL)
  {
    tmwtarg_free(pNewTree->m_pTreeNodeNil);
    tmwtarg_free(pNewTree);
    return(TMWDEFS_NULL);
  }
  pTempNode->m_pParentNode = pTempNode->m_pLeftNode = pTempNode->m_pRightNode = pNewTree->m_pTreeNodeNil;
  pTempNode->key = 0;
  pTempNode->red = 0;
  pTempNode->info = TMWDEFS_NULL;
  return (pNewTree);
}


/*  function :  tmwtree_insert */
TMWTREE_NODE * TMWDEFS_GLOBAL tmwtree_insert(TMWTREE_TREE* pTree, TMWTYPES_ULONG key, void* info) 
{
  TMWTREE_NODE * y;
  TMWTREE_NODE * x;
  TMWTREE_NODE * newNode;

  x = (TMWTREE_NODE*) tmwtarg_alloc(sizeof(TMWTREE_NODE));
  if(x == TMWDEFS_NULL)
  {
    return(TMWDEFS_NULL);
  }

  newNode = x;
  x->key = key;
  x->info = info;
  
  _treeInsertHelp(pTree, x);
  newNode = x;
  x->red = 1;
  while (x->m_pParentNode->red) 
  { /* use sentinel instead of checking for root */
    if (x->m_pParentNode == x->m_pParentNode->m_pParentNode->m_pLeftNode)
    {
      y = x->m_pParentNode->m_pParentNode->m_pRightNode;
      if (y->red)
      {
        x->m_pParentNode->red = 0;
        y->red = 0;
        x->m_pParentNode->m_pParentNode->red = 1;
        x = x->m_pParentNode->m_pParentNode;
      }
      else 
      {
        if (x == x->m_pParentNode->m_pRightNode)
        {
          x = x->m_pParentNode;
          _leftRotate(pTree, x);
        }
        x->m_pParentNode->red = 0;
        x->m_pParentNode->m_pParentNode->red = 1;
        _rightRotate(pTree, x->m_pParentNode->m_pParentNode);
      } 
    }
    else 
    { /* case for x->m_pParentNode == x->m_pParentNode->m_pParentNode->m_pRightNode */
      y = x->m_pParentNode->m_pParentNode->m_pLeftNode;
      if (y->red)
      {
        x->m_pParentNode->red = 0;
        y->red = 0;
        x->m_pParentNode->m_pParentNode->red = 1;
        x = x->m_pParentNode->m_pParentNode;
      }
      else 
      {
        if (x == x->m_pParentNode->m_pLeftNode)
        {
          x = x->m_pParentNode;
          _rightRotate(pTree, x);
        }
        x->m_pParentNode->red = 0;
        x->m_pParentNode->m_pParentNode->red = 1;
        _leftRotate(pTree, x->m_pParentNode->m_pParentNode);
      } 
    }
  }
  pTree->m_pTreeNodeRoot->m_pLeftNode->red = 0;
  
  pTree->quantity++;
  return (newNode);
  
#if DEBUG_ASSERT
  Assert(!tree->m_pTreeNodeNil->red, "nil not red in tmwtree_insert");
  Assert(!tree->m_pTreeNodeRoot->red, "root not red in tmwtree_insert");
#endif
}

/*  function :  tmwtree_successor  */
TMWTREE_NODE * TMWDEFS_GLOBAL tmwtree_successor(TMWTREE_TREE* pTree, TMWTREE_NODE* x) 
{ 
  TMWTREE_NODE* y;
  TMWTREE_NODE* nil = pTree->m_pTreeNodeNil;
  TMWTREE_NODE* root = pTree->m_pTreeNodeRoot;
  
  if (nil !=(y = x->m_pRightNode))
  {
    /* assignment to y is intentional */
    while (y->m_pLeftNode != nil) 
    { /* returns the minium of the right subtree of x */
      y = y->m_pLeftNode;
    }
    return (y);
  }
  else 
  {
    y = x->m_pParentNode;
    while (x == y->m_pRightNode) 
    { /* sentinel used instead of checking for nil */
      x = y;
      y = y->m_pParentNode;
    }
    if (y == root)
    {
      return (nil);
    }
    return (y);
  }
}

/*  function :  tmwtree_predecessor  */
TMWTREE_NODE * TMWDEFS_GLOBAL tmwtree_predecessor(TMWTREE_TREE* pTree, TMWTREE_NODE* x) 
{
  TMWTREE_NODE* y;
  TMWTREE_NODE* nil = pTree->m_pTreeNodeNil;
  TMWTREE_NODE* root = pTree->m_pTreeNodeRoot;
  
  if (nil !=(y = x->m_pLeftNode))
  {
    /* assignment to y is intentional */
    while (y->m_pRightNode != nil) 
    { /* returns the maximum of the left subtree of x */
      y = y->m_pRightNode;
    }
    return (y);
  }
  else 
  {
    y = x->m_pParentNode;
    while (x == y->m_pLeftNode) 
    { 
      if (y == root)
      {
        return (nil); 
      }
      x = y;
      y = y->m_pParentNode;
    }
    return (y);
  }
}

/*  function :  tmwtree_destroy */
void TMWDEFS_GLOBAL tmwtree_destroy(TMWTREE_TREE* pTree) 
{
  _destHelper(pTree, pTree->m_pTreeNodeRoot->m_pLeftNode);
  tmwtarg_free(pTree->m_pTreeNodeRoot);
  tmwtarg_free(pTree->m_pTreeNodeNil);
  tmwtarg_free(pTree);
}

#if 0
/*  function :  tmwtree_print */
void tmwtree_print(TMWTREE_TREE* pTree) 
{
  //_inorderTreePrint(pTree, pTree->m_pTreeNodeRoot->m_pLeftNode);
}
#endif

/*  function :  tmwtree_exactQuery */
TMWTREE_NODE * TMWDEFS_GLOBAL tmwtree_exactQuery(TMWTREE_TREE *pTree, TMWTYPES_ULONG q) 
{
  TMWTREE_NODE* x = pTree->m_pTreeNodeRoot->m_pLeftNode;
  TMWTREE_NODE* nil = pTree->m_pTreeNodeNil;
  if (x == nil)
  {
    return (0);
  }

  while (x->key != q) 
  { /*assignemnt*/
    if (x->key > q)
    {
      /* x->key > q */
      x = x->m_pLeftNode;
    }
    else 
    {
      x = x->m_pRightNode;
    }
    if (x == nil)
    {
      return (0);
    }
  }
  return (x);
}


/*  function :  tmwtree_delete */
/* Remove item from tree */
void TMWDEFS_GLOBAL tmwtree_delete(TMWTREE_TREE* pTree, TMWTREE_NODE* pNode)
{
  TMWTREE_NODE* y;
  TMWTREE_NODE* x;
  TMWTREE_NODE* nil = pTree->m_pTreeNodeNil;
  TMWTREE_NODE* root = pTree->m_pTreeNodeRoot;
  
  y = ((pNode->m_pLeftNode == nil) || (pNode->m_pRightNode == nil)) ? pNode : tmwtree_successor(pTree, pNode);
  x = (y->m_pLeftNode == nil) ? y->m_pRightNode : y->m_pLeftNode;
  if (root ==(x->m_pParentNode = y->m_pParentNode))
  {
    /* assignment of y->p to x->p is intentional */
    root->m_pLeftNode = x;
  }
  else 
  {
    if (y == y->m_pParentNode->m_pLeftNode)
    {
      y->m_pParentNode->m_pLeftNode = x;
    }
    else 
    {
      y->m_pParentNode->m_pRightNode = x;
    }
  }
  if (y != pNode)
  {
    /* y should not be nil in this case */
    
#if DEBUG_ASSERT
    Assert((y != tree->m_pTreeNodeNil), "y is nil in tmwtree_delete\n");
#endif
    /* y is the node to splice out and x is its child */
    
    if (!(y->red))
    {
      _deleteFixUp(pTree, x);
    }
    
    pTree->DestroyInfo(pNode->info);
    y->m_pLeftNode = pNode->m_pLeftNode;
    y->m_pRightNode = pNode->m_pRightNode;
    y->m_pParentNode = pNode->m_pParentNode;
    y->red = pNode->red;
    pNode->m_pLeftNode->m_pParentNode = pNode->m_pRightNode->m_pParentNode = y;
    if (pNode == pNode->m_pParentNode->m_pLeftNode)
    {
      pNode->m_pParentNode->m_pLeftNode = y; 
    }
    else 
    {
      pNode->m_pParentNode->m_pRightNode = y;
    }
    tmwtarg_free(pNode); 
  }
  else 
  {
    pTree->DestroyInfo(y->info);
    if (!(y->red))
    {
      _deleteFixUp(pTree, x);
    }
    tmwtarg_free(y);
  }
  
  pTree->quantity--;

#if DEBUG_ASSERT
  Assert(!tree->m_pTreeNodeNil->red, "nil not black in tmwtree_delete");
#endif
}

#if 0
/*  function :  tmwtree_enumerate */
TMWSTK_STACK* TMWDEFS_GLOBAL tmwtree_enumerate(TMWTREE_TREE *pTree, void* low, void* high) 
{
  TMWSTK_STACK* enumResultStack;
  TMWTREE_NODE* nil = pTree->m_pTreeNodeNil;
  TMWTREE_NODE* x = pTree->m_pTreeNodeRoot->m_pLeftNode;
  TMWTREE_NODE* lastBest = nil;
  
  enumResultStack = tmwstk_create();
  while (nil != x) 
  {
    if (1 ==(pTree->Compare(x->key, high)))
    {
      /* x->key > high */
      x = x->m_pLeftNode;
    }
    else 
    {
      lastBest = x;
      x = x->m_pRightNode;
    }
  }
  while ((lastBest != nil) && (1 != pTree->Compare(low, lastBest->key))) 
  {
    tmwstk_push(enumResultStack, lastBest);
    lastBest = tmwtree_predecessor(pTree, lastBest);
  }
  return (enumResultStack);
}
#endif
      
