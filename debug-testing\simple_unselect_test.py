#!/usr/bin/env python3
"""
Simple test to verify unselect functionality
"""

import requests
import json
import time

def call_api(query):
    """Make a simple API call"""
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    model = "llama3:latest"
    tool_id = "code_analyzer_tool"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model,
        "messages": [{"role": "user", "content": query}],
        "tool_ids": [tool_id],
        "stream": False
    }
    
    print(f"\n🔧 Query: '{query}'")
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=120
        )
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            choices = result.get("choices", [])
            
            if choices:
                content = choices[0].get("message", {}).get("content", "")
                
                print(f"   Time: {end_time - start_time:.2f}s")
                print(f"   Length: {len(content)} chars")
                print(f"   Content: {content[:300]}...")
                
                # Key indicators
                has_tmwmem = "tmwmem" in content.lower()
                has_no_codebase = "no codebase selected" in content.lower()
                has_context = "context" in content.lower()
                
                print(f"   Has tmwmem: {has_tmwmem}")
                print(f"   Has 'no codebase': {has_no_codebase}")
                print(f"   Has context: {has_context}")
                
                return content
            else:
                print("   ❌ No choices")
                return None
        else:
            print(f"   ❌ HTTP {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return None

def main():
    """Simple unselect test"""
    
    print("🚀 SIMPLE UNSELECT TEST")
    print("="*50)
    
    # Step 1: Unselect
    print("\n📋 STEP 1: Unselect codebase")
    call_api("unselect codebase")
    time.sleep(3)
    
    # Step 2: Query (should fail or be generic)
    print("\n📋 STEP 2: Query after unselect")
    call_api("tmwmem_alloc")
    time.sleep(3)
    
    # Step 3: Select
    print("\n📋 STEP 3: Select codebase")
    call_api("select utils codebase")
    time.sleep(3)
    
    # Step 4: Query (should work)
    print("\n📋 STEP 4: Query after select")
    call_api("tmwmem_alloc")
    
    print(f"\n{'='*50}")
    print("🎯 Test complete!")

if __name__ == "__main__":
    main()
