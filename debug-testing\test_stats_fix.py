#!/usr/bin/env python3
"""
Test the stats routing fix
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_stats_fix():
    """Test that stats queries now return real data"""
    print("🧪 Testing Stats Routing Fix")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test the specific failing query
    query = "get stats for utils"
    
    print(f"🔍 Testing: '{query}'")
    print("-" * 30)
    
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": query}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 1000
            },
            timeout=90
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            print(f"✅ Response received ({len(content)} chars)")
            
            # Check for real codebase data
            real_indicators = [
                "Total chunks: 479",
                "Unique files: 43", 
                "Languages:",
                "C: 460",
                "CPP: 15",
                "CSHARP: 4",
                "Last updated: 2025-06-27"
            ]
            
            # Check for generic/fake data
            generic_indicators = [
                "_add_chunk_clarification",
                "Number of runs",
                "Average run time",
                "Memory usage",
                "Peak memory usage"
            ]
            
            found_real = [ind for ind in real_indicators if ind in content]
            found_generic = [ind for ind in generic_indicators if ind in content]
            
            print(f"\n📋 Analysis:")
            if found_real:
                print(f"🎉 REAL DATA FOUND: {found_real[:3]}")
                success = True
            else:
                print("❌ NO REAL DATA FOUND")
                success = False
                
            if found_generic:
                print(f"⚠️ Still has generic data: {found_generic}")
                success = False
            
            print(f"\n📄 Full Response:")
            print("-" * 40)
            print(content)
            print("-" * 40)
            
            return success
            
        else:
            print(f"❌ HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function"""
    print("🔧 Stats Routing Fix Test")
    print("=" * 60)
    print("Testing that 'get stats for utils' now returns real codebase data")
    
    success = test_stats_fix()
    
    print(f"\n🎯 RESULT:")
    if success:
        print("🎉 SUCCESS: Stats routing fix is working!")
        print("✅ Real codebase statistics returned")
        print("✅ No more generic tool performance data")
        print("✅ Ready for auto-tester validation")
    else:
        print("❌ ISSUE: Fix not working yet")
        print("🔧 Action needed:")
        print("1. Update tool in OpenWebUI with the fixed version")
        print("2. Restart OpenWebUI if needed")
        print("3. Re-run this test")
    
    print(f"\n🎯 Expected real data:")
    print("• Total chunks: 479")
    print("• Unique files: 43")
    print("• Languages: C: 460, CPP: 15, CSHARP: 4")
    print("• Last updated: 2025-06-27T16:10:34.007731")

if __name__ == "__main__":
    main()
