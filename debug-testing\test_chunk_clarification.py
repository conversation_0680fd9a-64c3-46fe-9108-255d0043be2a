#!/usr/bin/env python3
"""
Test the chunk clarification fix
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_chunk_clarification():
    """Test that statistics now include clarification about chunks"""
    print("🧪 Testing Chunk Clarification Fix")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test queries that request statistics
    test_queries = [
        "show details about the utils code base",
        "get statistics for utils codebase", 
        "what are the metrics for utils?",
        "show me utils codebase stats",
    ]
    
    clarification_found = 0
    total_tests = len(test_queries)
    
    for query in test_queries:
        print(f"\n🔍 Testing: '{query}'")
        print("-" * 40)
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": f"select codebase utils\n{query}"}],
                    "tool_ids": ["code_analyzer_tool"],
                    "stream": False,
                    "max_tokens": 1000
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                print(f"✅ Response received ({len(content)} chars)")
                
                # Check for clarification about chunks
                clarification_indicators = [
                    "chunks are text segments",
                    "splitting source files",
                    "vector search",
                    "one source file may contain multiple chunks",
                    "text segments created by splitting",
                    "search granularity"
                ]
                
                found_clarifications = [ind for ind in clarification_indicators if ind.lower() in content.lower()]
                
                if found_clarifications:
                    print(f"🎉 CLARIFICATION FOUND: {found_clarifications[:2]}")
                    clarification_found += 1
                else:
                    print("⚠️ No chunk clarification found")
                
                # Check for problematic interpretations
                problematic_phrases = [
                    "chunks.*indicates.*files",
                    "chunks.*number of.*files", 
                    "chunks.*source code files",
                    "479.*files",
                    "479.*modules"
                ]
                
                import re
                issues = []
                for phrase in problematic_phrases:
                    if re.search(phrase, content, re.IGNORECASE):
                        issues.append(phrase)
                
                if issues:
                    print(f"❌ Still has problematic interpretation: {issues}")
                else:
                    print("✅ No problematic chunk=files interpretation")
                
                # Show relevant parts
                lines = content.split('\n')
                chunk_lines = [line for line in lines if 'chunk' in line.lower()]
                if chunk_lines:
                    print(f"Chunk-related lines:")
                    for line in chunk_lines[:3]:
                        print(f"  → {line.strip()}")
                
            else:
                print(f"❌ HTTP {response.status_code}: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return clarification_found, total_tests

def main():
    """Main test function"""
    print("🔧 Chunk vs Files Clarification Test")
    print("=" * 70)
    print("Testing that statistics now clarify what chunks mean")
    
    # Test the clarification
    found, total = test_chunk_clarification()
    
    # Summary
    print(f"\n📊 SUMMARY")
    print("=" * 50)
    
    success_rate = (found / total * 100) if total > 0 else 0
    
    print(f"📈 Clarification success rate: {found}/{total} ({success_rate:.1f}%)")
    
    if found >= total * 0.75:  # 75% or better
        print("🎉 EXCELLENT: Chunk clarification working well!")
        print("✅ Users will understand chunks ≠ files")
        print("✅ Proper RAG/vector database terminology")
    elif found > 0:
        print("👍 GOOD: Some clarification present")
        print("⚠️ May need to strengthen the clarification")
    else:
        print("❌ ISSUE: No clarification found")
        print("🔧 Need to update tool in OpenWebUI")
    
    print(f"\n🎯 Expected clarification should explain:")
    print("• Chunks = text segments from splitting files")
    print("• Used for vector search and embedding")
    print("• One file can have multiple chunks")
    print("• Different from actual file count")
    
    if found > 0:
        print(f"\n✅ Action: Tool fix is working!")
        print("Users will now understand the difference between chunks and files.")
    else:
        print(f"\n🔧 Action needed:")
        print("1. Update tool in OpenWebUI with the fixed version")
        print("2. Restart OpenWebUI to ensure changes take effect")

if __name__ == "__main__":
    main()
