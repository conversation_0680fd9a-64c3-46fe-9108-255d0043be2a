﻿<UserControl x:Class="Essenbee.Z80.Debugger.ControlPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:controls="clr-namespace:Essenbee.Z80.Debugger"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <StackPanel Orientation="Horizontal">
            <Button x:Name="Step" 
                        Content="Step" 
                        Width="50" 
                        Height="25" 
                        Margin="10,5,5,5" 
                        FontWeight="Bold"
                        Command="{Binding StepCommand}" />
            <Button x:Name="Load" 
                        Content="Load" 
                        Width="50" 
                        Height="25" 
                        Margin="5" 
                        FontWeight="Bold"
                        Command="{Binding LoadCommand}" />
            <Button x:Name="Rom" 
                        Content="Load ROM" 
                        Width="75" 
                        Height="25" 
                        Margin="5" 
                        FontWeight="Bold"
                        Command="{Binding LoadRomCommand}" />
        </StackPanel>
    </Grid>
</UserControl>
