#!/usr/bin/env python3
"""
Test all three fixes: intent detection, codebase selection, and stats routing
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_codebase_selection_fix():
    """Test that codebase selection actually works"""
    print("🧪 Testing Codebase Selection Fix")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    query = "select codebase utils"
    
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": query}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 800
            },
            timeout=90
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            # Check for successful selection indicators
            success_indicators = [
                "selected",
                "utils",
                "codebase",
                "ready",
                "enhanced"
            ]
            
            # Check for failure indicators
            failure_indicators = [
                "I see what you're getting at",
                "run the following command",
                "code_analyzer_tools/select_cod"
            ]
            
            found_success = [ind for ind in success_indicators if ind.lower() in content.lower()]
            found_failure = [ind for ind in failure_indicators if ind in content]
            
            if found_success and not found_failure:
                print("✅ CODEBASE SELECTION WORKING")
                return True
            else:
                print("❌ CODEBASE SELECTION STILL BROKEN")
                if found_failure:
                    print(f"   Failure indicators: {found_failure}")
                return False
                
        else:
            print(f"❌ HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_stats_routing_fix():
    """Test that stats queries return real codebase data"""
    print(f"\n🧪 Testing Stats Routing Fix")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    queries = [
        "get stats for utils",
        "get statistics for utils"
    ]
    
    success_count = 0
    
    for query in queries:
        print(f"\n🔍 Testing: '{query}'")
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": query}],
                    "tool_ids": ["code_analyzer_tool"],
                    "stream": False,
                    "max_tokens": 1000
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                # Check for real codebase data
                real_indicators = [
                    "Total chunks: 479",
                    "Unique files: 43",
                    "Basic Codebase Statistics",
                    "C: 460",
                    "Last updated: 2025-06-27"
                ]
                
                # Check for "No codebase selected" message
                no_codebase_msg = "No codebase selected for statistics"
                
                found_real = [ind for ind in real_indicators if ind in content]
                
                if found_real:
                    print("✅ REAL CODEBASE DATA RETURNED")
                    success_count += 1
                elif no_codebase_msg in content:
                    print("⚠️ 'No codebase selected' message (expected if no selection)")
                else:
                    print("❌ Still getting generic response")
                
                print(f"Preview: {content[:150]}...")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return success_count, len(queries)

def test_no_codebase_message():
    """Test that 'No codebase selected' message appears when appropriate"""
    print(f"\n🧪 Testing 'No Codebase Selected' Message")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test with just "get stats" (no codebase specified)
    query = "get stats"
    
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": query}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 800
            },
            timeout=90
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            if "No codebase selected for statistics" in content:
                print("✅ 'No codebase selected' message working")
                return True
            else:
                print("❌ Expected 'No codebase selected' message not found")
                print(f"Got: {content[:200]}...")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function"""
    print("🔧 Testing All Three Fixes")
    print("=" * 70)
    print("1. Codebase selection should work")
    print("2. Stats queries should return real data")
    print("3. 'No codebase selected' message should appear when appropriate")
    
    # Test all fixes
    selection_works = test_codebase_selection_fix()
    stats_success, stats_total = test_stats_routing_fix()
    no_codebase_works = test_no_codebase_message()
    
    # Summary
    print(f"\n📊 SUMMARY")
    print("=" * 50)
    print(f"✅ Codebase selection: {'WORKING' if selection_works else 'BROKEN'}")
    print(f"✅ Stats routing: {stats_success}/{stats_total} working")
    print(f"✅ No codebase message: {'WORKING' if no_codebase_works else 'BROKEN'}")
    
    total_score = (
        (1 if selection_works else 0) +
        (stats_success / stats_total if stats_total > 0 else 0) +
        (1 if no_codebase_works else 0)
    )
    
    print(f"\n🎯 OVERALL SCORE: {total_score:.1f}/3.0")
    
    if total_score >= 2.5:
        print("🎉 EXCELLENT: Most fixes working!")
    elif total_score >= 1.5:
        print("👍 GOOD: Significant improvement")
    else:
        print("❌ ISSUE: Fixes need more work")
        print("🔧 Make sure tool is updated in OpenWebUI")

if __name__ == "__main__":
    main()
