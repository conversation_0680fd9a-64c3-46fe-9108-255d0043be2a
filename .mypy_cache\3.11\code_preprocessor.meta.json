{"data_mtime": 1752116070, "dep_lines": [7, 8, 9, 10, 11, 14, 15, 16, 278, 328, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 10, 5, 5, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["os", "asyncio", "pathlib", "typing", "logging", "framework_integration", "language_registry", "chunk_system", "semantic_patterns", "re", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "asyncio.events", "datetime", "enum", "io", "language_framework", "types", "typing_extensions"], "hash": "21b127a3a6c7f214794919967556e95277e2c251", "id": "code_preprocessor", "ignore_all": false, "interface_hash": "9e26b96ec444c7e4a391f43a6d14e95c0cbcf3bf", "mtime": 1752116069, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\code_preprocessor.py", "plugin_data": null, "size": 15485, "suppressed": [], "version_id": "1.15.0"}