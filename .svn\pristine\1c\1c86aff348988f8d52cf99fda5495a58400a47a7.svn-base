#!/usr/bin/env python3
"""
Quick script to check server version and available endpoints
"""

import requests
import json

CODE_ANALYZER_SERVER_URL = "http://home-ai-server:5002"

def check_server_version():
    """Check what version the server is actually running"""
    print("🔍 Checking server version and available endpoints...")
    
    try:
        # Check root endpoint for version info
        response = requests.get(f"{CODE_ANALYZER_SERVER_URL}/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Server is running version: {data.get('version', 'unknown')}")
            print(f"   Message: {data.get('message', 'unknown')}")
            return data.get('version', 'unknown')
        else:
            print(f"❌ Root endpoint returned status {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Failed to check server version: {e}")
        return None

def test_status_endpoint():
    """Test if the /status endpoint exists"""
    print("\n🧪 Testing /status endpoint...")
    
    try:
        response = requests.get(f"{CODE_ANALYZER_SERVER_URL}/status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ /status endpoint is available")
            print(f"   Status: {data.get('status', 'unknown')}")
            print(f"   Service: {data.get('service', 'unknown')}")
            return True
        elif response.status_code == 404:
            print("❌ /status endpoint not found (404)")
            print("   This means the server is running an older version")
            return False
        else:
            print(f"❌ /status endpoint returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Failed to test /status endpoint: {e}")
        return False

def check_openapi_endpoints():
    """Check what endpoints are actually available via OpenAPI"""
    print("\n📋 Checking available endpoints via OpenAPI...")
    
    try:
        response = requests.get(f"{CODE_ANALYZER_SERVER_URL}/openapi.json", timeout=10)
        if response.status_code == 200:
            data = response.json()
            paths = data.get('paths', {})
            
            print(f"✅ Found {len(paths)} endpoints:")
            for path in sorted(paths.keys()):
                methods = list(paths[path].keys())
                print(f"   {path} ({', '.join(methods).upper()})")
            
            # Check specifically for /status
            if '/status' in paths:
                print("\n✅ /status endpoint is defined in OpenAPI")
            else:
                print("\n❌ /status endpoint is NOT defined in OpenAPI")
                print("   This confirms the server is running an older version")
            
            return paths
        else:
            print(f"❌ OpenAPI endpoint returned status {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Failed to check OpenAPI endpoints: {e}")
        return None

def main():
    print("🚀 Server Version and Endpoint Checker")
    print("=" * 50)
    
    version = check_server_version()
    status_available = test_status_endpoint()
    endpoints = check_openapi_endpoints()
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY:")
    print("=" * 50)
    
    if version:
        print(f"Server Version: {version}")
        if version == "2.1.0":
            print("⚠️  Server is running an OLDER version")
            print("   The current code has version 3.2.0")
            print("   Server needs to be restarted to pick up new code")
        elif version == "3.2.0":
            print("✅ Server is running the LATEST version")
        else:
            print(f"❓ Unknown version: {version}")
    
    if not status_available:
        print("❌ /status endpoint is missing")
        print("   This is expected for version 2.1.0")
        print("   The endpoint was added in version 3.0.0")
    else:
        print("✅ /status endpoint is working")
    
    print("\n🔧 SOLUTION:")
    if not status_available and version == "2.1.0":
        print("To fix the /status endpoint:")
        print("1. Restart the Code Analysis server to pick up the updated code")
        print("2. The server should then report version 3.2.0")
        print("3. The /status endpoint should become available")
        print("\nDocker restart command:")
        print("   docker-compose restart code-analysis-server")
        print("   # or")
        print("   docker restart <container-name>")
    else:
        print("No action needed - server is up to date")

if __name__ == "__main__":
    main()
