[{"test": "Initial Status", "success": false, "error": "Exception: 'Tools' object has no attribute 'get_status'", "duration": 0, "pre_state": {"current_codebase": "", "server_url": "http://home-ai-server.local:5002"}, "post_state": {}, "response_length": 0}, {"test": "Select Utils", "success": true, "error": null, "duration": 1.0339851379394531, "pre_state": {"current_codebase": "", "server_url": "http://home-ai-server.local:5002"}, "post_state": {"current_codebase": "utils", "server_url": "http://home-ai-server.local:5002"}, "response_length": 393}, {"test": "Check Status After Selection", "success": false, "error": "Exception: 'Tools' object has no attribute 'get_status'", "duration": 0, "pre_state": {"current_codebase": "utils", "server_url": "http://home-ai-server.local:5002"}, "post_state": {}, "response_length": 0}, {"test": "Working Query 1", "success": true, "error": null, "duration": 0.41072535514831543, "pre_state": {"current_codebase": "utils", "server_url": "http://home-ai-server.local:5002"}, "post_state": {"current_codebase": "utils", "server_url": "http://home-ai-server.local:5002"}, "response_length": 1507}, {"test": "Check Status After Working Query", "success": false, "error": "Exception: 'Tools' object has no attribute 'get_status'", "duration": 0, "pre_state": {"current_codebase": "utils", "server_url": "http://home-ai-server.local:5002"}, "post_state": {}, "response_length": 0}, {"test": "Generic Query 1", "success": true, "error": null, "duration": 0.8844559192657471, "pre_state": {"current_codebase": "utils", "server_url": "http://home-ai-server.local:5002"}, "post_state": {"current_codebase": "utils", "server_url": "http://home-ai-server.local:5002"}, "response_length": 5314}, {"test": "Check Status After Generic Query", "success": false, "error": "Exception: 'Tools' object has no attribute 'get_status'", "duration": 0, "pre_state": {"current_codebase": "utils", "server_url": "http://home-ai-server.local:5002"}, "post_state": {}, "response_length": 0}, {"test": "Working Query 2", "success": true, "error": null, "duration": 0.4858713150024414, "pre_state": {"current_codebase": "utils", "server_url": "http://home-ai-server.local:5002"}, "post_state": {"current_codebase": "utils", "server_url": "http://home-ai-server.local:5002"}, "response_length": 985}, {"test": "Check Status After Working Query 2", "success": false, "error": "Exception: 'Tools' object has no attribute 'get_status'", "duration": 0, "pre_state": {"current_codebase": "utils", "server_url": "http://home-ai-server.local:5002"}, "post_state": {}, "response_length": 0}, {"test": "Generic Query 2", "success": false, "error": "Error in response", "duration": 0.7720975875854492, "pre_state": {"current_codebase": "utils", "server_url": "http://home-ai-server.local:5002"}, "post_state": {"current_codebase": "utils", "server_url": "http://home-ai-server.local:5002"}, "response_length": 227}, {"test": "Check Status After Generic Query 2", "success": false, "error": "Exception: 'Tools' object has no attribute 'get_status'", "duration": 0, "pre_state": {"current_codebase": "utils", "server_url": "http://home-ai-server.local:5002"}, "post_state": {}, "response_length": 0}, {"test": "Direct Function Call", "success": false, "error": "Error in response", "duration": 0.43921494483947754, "pre_state": {"current_codebase": "utils", "server_url": "http://home-ai-server.local:5002"}, "post_state": {"current_codebase": "utils", "server_url": "http://home-ai-server.local:5002"}, "response_length": 86}, {"test": "Final Status", "success": false, "error": "Exception: 'Tools' object has no attribute 'get_status'", "duration": 0, "pre_state": {"current_codebase": "utils", "server_url": "http://home-ai-server.local:5002"}, "post_state": {}, "response_length": 0}]