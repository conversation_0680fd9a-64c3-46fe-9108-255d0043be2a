#!/usr/bin/env python3
"""
Test script to verify codebase selection sync between OpenWebUI tool and RAG server.
"""

import requests
import json
import sys
from pathlib import Path

# Add the current directory to Python path to import the tool
sys.path.append(str(Path(__file__).parent))

from open_webui_code_analyzer_tool import Tools

async def test_codebase_sync():
    """Test codebase selection synchronization"""
    print("🧪 Testing Codebase Selection Synchronization")
    print("=" * 60)
    
    # Initialize the tool with correct server URL for external testing
    tool = Tools()
    # Override the server URL for external testing
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    
    # Check initial status
    print("\n1. Checking initial status...")
    try:
        health_response = requests.get(f"{tool.valves.code_analyzer_server_url}/health", timeout=10)
        if health_response.status_code == 200:
            health_data = health_response.json()
            server_current = health_data.get("current_codebase", "None")
            tool_current = tool.valves.current_codebase or "None"
            
            print(f"   Server current codebase: {server_current}")
            print(f"   Tool cached codebase: {tool_current}")
            print(f"   Sync status: {'✅ Synced' if server_current == tool_current else '⚠️ Out of sync'}")
        else:
            print(f"   ❌ Server health check failed: {health_response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Error checking server health: {e}")
        return False
    
    # List available codebases
    print("\n2. Listing available codebases...")
    try:
        codebases_response = requests.post(f"{tool.valves.code_analyzer_server_url}/tools/list_codebases", json={}, timeout=10)
        if codebases_response.status_code == 200:
            # Parse the result from the tool response format
            response_data = codebases_response.json()
            result_text = response_data.get("result", "")
            # Extract codebase names from the result text (format: **🚀 codebase_name**)
            codebases = []
            import re
            matches = re.findall(r'\*\*🚀 ([^*]+)\*\*', result_text)
            codebases = [match.strip() for match in matches]
            print(f"   Available codebases: {codebases}")
            
            if not codebases:
                print("   ⚠️ No codebases available for testing")
                return False
                
            test_codebase = codebases[0]  # Use first available codebase
            print(f"   Using '{test_codebase}' for testing")
        else:
            print(f"   ❌ Failed to list codebases: {codebases_response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Error listing codebases: {e}")
        return False
    
    # Test codebase selection
    print(f"\n3. Testing codebase selection with '{test_codebase}'...")
    try:
        # Select codebase using the tool
        result = await tool.select_codebase(test_codebase)
        print(f"   Selection result: {result}")
        
        # Verify sync after selection
        health_response = requests.get(f"{tool.valves.code_analyzer_server_url}/health", timeout=10)
        if health_response.status_code == 200:
            health_data = health_response.json()
            server_current = health_data.get("current_codebase", "None")
            tool_current = tool.valves.current_codebase or "None"
            
            print(f"   Server current codebase: {server_current}")
            print(f"   Tool cached codebase: {tool_current}")
            print(f"   Sync status: {'✅ Synced' if server_current == tool_current else '⚠️ Out of sync'}")
            
            if server_current != test_codebase:
                print(f"   ⚠️ Warning: Expected '{test_codebase}', but server has '{server_current}'")
        
    except Exception as e:
        print(f"   ❌ Error during codebase selection: {e}")
        return False
    
    # Test get_code_context with sync logic
    print(f"\n4. Testing get_code_context with sync logic...")
    try:
        # Test with a simple query
        context_result = await tool.get_code_context("error handling", test_codebase)
        print(f"   Context result length: {len(context_result)} characters")
        
        if "❌" in context_result:
            print(f"   ⚠️ Error in context retrieval: {context_result[:200]}...")
        else:
            print("   ✅ Context retrieved successfully")
            
    except Exception as e:
        print(f"   ❌ Error during context retrieval: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 Codebase sync test completed!")
    return True

if __name__ == "__main__":
    import asyncio
    
    try:
        success = asyncio.run(test_codebase_sync())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
