#!/usr/bin/env python3
"""
Test the fixed chunk counting logic.
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path to import the tool
sys.path.append(str(Path(__file__).parent))

from open_webui_code_analyzer_tool import Tools

class MockEventEmitter:
    """Mock event emitter to capture status messages"""
    def __init__(self):
        self.events = []
    
    async def __call__(self, event):
        self.events.append(event)
        print(f"📡 Event: {event}")

async def test_fixed_chunk_count():
    """Test the fixed chunk counting logic"""
    print("🔍 Testing Fixed Chunk Count Logic")
    print("=" * 50)
    
    # Initialize the tool
    tool = Tools()
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    
    # Ensure utils is selected
    print("1. Selecting utils codebase...")
    await tool.select_codebase("utils")
    
    # Create mock event emitter
    event_emitter = MockEventEmitter()
    
    # Test with a query we know should return multiple chunks
    print("\n2. Testing with 'memory allocation patterns'...")
    try:
        result = await tool.get_code_context(
            "memory allocation patterns", 
            codebase_name="utils",
            n_results=15,
            __event_emitter__=event_emitter
        )
        
        print(f"   Result length: {len(result)} characters")
        print(f"   Events captured: {len(event_emitter.events)}")
        
        # Check the final event for chunk count
        if event_emitter.events:
            final_event = event_emitter.events[-1]
            description = final_event.get('data', {}).get('description', '')
            print(f"   Final status: {description}")
            
            if 'chunk' in description.lower():
                print("   ✅ SUCCESS: Chunk count properly reported!")
            else:
                print("   ⚠️ No chunk count in final status")
        
        # Manual verification
        import re
        context_matches = re.findall(r"Context \d+:", result)
        print(f"   Manual count (Context X:): {len(context_matches)}")
        
        if len(context_matches) > 1:
            print(f"   🎉 CONFIRMED: Found {len(context_matches)} chunks!")
        
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Fixed chunk count test completed!")

if __name__ == "__main__":
    try:
        asyncio.run(test_fixed_chunk_count())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
