{".class": "MypyFile", "_fullname": "gpu_infrastructure", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BasicGPUManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "gpu_infrastructure.BasicGPUManager", "name": "BasicGPUManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "gpu_infrastructure.BasicGPUManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "gpu_infrastructure", "mro": ["gpu_infrastructure.BasicGPUManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "gpu_infrastructure.BasicGPUManager.__init__", "name": "__init__", "type": null}}, "check_gpu_basic_availability": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "gpu_host"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "gpu_infrastructure.BasicGPUManager.check_gpu_basic_availability", "name": "check_gpu_basic_availability", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "gpu_host"], "arg_types": ["gpu_infrastructure.BasicGPUManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_gpu_basic_availability of BasicGPUManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bool"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "discover_available_gpus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "gpu_infrastructure.BasicGPUManager.discover_available_gpus", "name": "discover_available_gpus", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["gpu_infrastructure.BasicGPUManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "discover_available_gpus of BasicGPUManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_gpu_specification": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "gpu_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "gpu_infrastructure.BasicGPUManager.get_gpu_specification", "name": "get_gpu_specification", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "gpu_type"], "arg_types": ["gpu_infrastructure.BasicGPUManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_gpu_specification of BasicGPUManager", "ret_type": {".class": "UnionType", "items": ["gpu_infrastructure.GPUSpecification", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_supported_gpu_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "gpu_infrastructure.BasicGPUManager.get_supported_gpu_types", "name": "get_supported_gpu_types", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["gpu_infrastructure.BasicGPUManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_supported_gpu_types of BasicGPUManager", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "known_gpu_types": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "gpu_infrastructure.BasicGPUManager.known_gpu_types", "name": "known_gpu_types", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "gpu_infrastructure.BasicGPUManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "gpu_infrastructure.BasicGPUManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BasicProcessingCoordinator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "gpu_infrastructure.BasicProcessingCoordinator", "name": "BasicProcessingCoordinator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "gpu_infrastructure.BasicProcessingCoordinator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "gpu_infrastructure", "mro": ["gpu_infrastructure.BasicProcessingCoordinator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "gpu_infrastructure.BasicProcessingCoordinator.__init__", "name": "__init__", "type": null}}, "default_models": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "gpu_infrastructure.BasicProcessingCoordinator.default_models", "name": "default_models", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "estimate_processing_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "chunk_count", "gpu_selection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "gpu_infrastructure.BasicProcessingCoordinator.estimate_processing_time", "name": "estimate_processing_time", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "chunk_count", "gpu_selection"], "arg_types": ["gpu_infrastructure.BasicProcessingCoordinator", "builtins.int", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "estimate_processing_time of BasicProcessingCoordinator", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.float"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_processing_recommendations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chunk_count"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "gpu_infrastructure.BasicProcessingCoordinator.get_processing_recommendations", "name": "get_processing_recommendations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunk_count"], "arg_types": ["gpu_infrastructure.BasicProcessingCoordinator", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_processing_recommendations of BasicProcessingCoordinator", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gpu_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "gpu_infrastructure.BasicProcessingCoordinator.gpu_manager", "name": "gpu_manager", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "select_basic_processing_gpu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "gpu_infrastructure.BasicProcessingCoordinator.select_basic_processing_gpu", "name": "select_basic_processing_gpu", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["gpu_infrastructure.BasicProcessingCoordinator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "select_basic_processing_gpu of BasicProcessingCoordinator", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "gpu_infrastructure.BasicProcessingCoordinator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "gpu_infrastructure.BasicProcessingCoordinator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "GPUProcessingStage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "gpu_infrastructure.GPUProcessingStage", "name": "GPUProcessingStage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "gpu_infrastructure.GPUProcessingStage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "gpu_infrastructure", "mro": ["gpu_infrastructure.GPUProcessingStage", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "coordinator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "gpu_infrastructure.GPUProcessingStage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "coordinator"], "arg_types": ["gpu_infrastructure.GPUProcessingStage", "gpu_infrastructure.BasicProcessingCoordinator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GPUProcessingStage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "coordinator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "gpu_infrastructure.GPUProcessingStage.coordinator", "name": "coordinator", "type": "gpu_infrastructure.BasicProcessingCoordinator"}}, "prepare_gpu_processing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chunk_count"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "gpu_infrastructure.GPUProcessingStage.prepare_gpu_processing", "name": "prepare_gpu_processing", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunk_count"], "arg_types": ["gpu_infrastructure.GPUProcessingStage", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_gpu_processing of GPUProcessingStage", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "gpu_infrastructure.GPUProcessingStage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "gpu_infrastructure.GPUProcessingStage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GPUSpecification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "gpu_infrastructure.GPUSpecification", "name": "GPUSpecification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "gpu_infrastructure.GPUSpecification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 27, "name": "gpu_type", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 28, "name": "architecture", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 29, "name": "vram", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 30, "name": "tier", "type": "gpu_infrastructure.GPUTier"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 31, "name": "processing_speed", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 32, "name": "batch_size", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 33, "name": "parallel_requests", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 34, "name": "cost_per_hour", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 35, "name": "reliability_score", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 36, "name": "shared_resource", "type": "builtins.bool"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "gpu_infrastructure", "mro": ["gpu_infrastructure.GPUSpecification", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "gpu_infrastructure.GPUSpecification.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "gpu_type", "architecture", "vram", "tier", "processing_speed", "batch_size", "parallel_requests", "cost_per_hour", "reliability_score", "shared_resource"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "gpu_infrastructure.GPUSpecification.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "gpu_type", "architecture", "vram", "tier", "processing_speed", "batch_size", "parallel_requests", "cost_per_hour", "reliability_score", "shared_resource"], "arg_types": ["gpu_infrastructure.GPUSpecification", "builtins.str", "builtins.str", "builtins.str", "gpu_infrastructure.GPUTier", "builtins.float", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GPUSpecification", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "gpu_infrastructure.GPUSpecification.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "gpu_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "architecture"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vram"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tier"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "processing_speed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "batch_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "parallel_requests"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cost_per_hour"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "reliability_score"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shared_resource"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["gpu_type", "architecture", "vram", "tier", "processing_speed", "batch_size", "parallel_requests", "cost_per_hour", "reliability_score", "shared_resource"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "gpu_infrastructure.GPUSpecification.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["gpu_type", "architecture", "vram", "tier", "processing_speed", "batch_size", "parallel_requests", "cost_per_hour", "reliability_score", "shared_resource"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "gpu_infrastructure.GPUTier", "builtins.float", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of GPUSpecification", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "gpu_infrastructure.GPUSpecification.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["gpu_type", "architecture", "vram", "tier", "processing_speed", "batch_size", "parallel_requests", "cost_per_hour", "reliability_score", "shared_resource"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "gpu_infrastructure.GPUTier", "builtins.float", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of GPUSpecification", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "architecture": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "gpu_infrastructure.GPUSpecification.architecture", "name": "architecture", "type": "builtins.str"}}, "batch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "gpu_infrastructure.GPUSpecification.batch_size", "name": "batch_size", "type": "builtins.int"}}, "cost_per_hour": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "gpu_infrastructure.GPUSpecification.cost_per_hour", "name": "cost_per_hour", "type": "builtins.float"}}, "gpu_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "gpu_infrastructure.GPUSpecification.gpu_type", "name": "gpu_type", "type": "builtins.str"}}, "parallel_requests": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "gpu_infrastructure.GPUSpecification.parallel_requests", "name": "parallel_requests", "type": "builtins.int"}}, "processing_speed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "gpu_infrastructure.GPUSpecification.processing_speed", "name": "processing_speed", "type": "builtins.float"}}, "reliability_score": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "gpu_infrastructure.GPUSpecification.reliability_score", "name": "reliability_score", "type": "builtins.float"}}, "shared_resource": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "gpu_infrastructure.GPUSpecification.shared_resource", "name": "shared_resource", "type": "builtins.bool"}}, "tier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "gpu_infrastructure.GPUSpecification.tier", "name": "tier", "type": "gpu_infrastructure.GPUTier"}}, "vram": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "gpu_infrastructure.GPUSpecification.vram", "name": "vram", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "gpu_infrastructure.GPUSpecification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "gpu_infrastructure.GPUSpecification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GPUTier": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "gpu_infrastructure.GPUTier", "name": "GPUTier", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "gpu_infrastructure.GPUTier", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "gpu_infrastructure", "mro": ["gpu_infrastructure.GPUTier", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "BASIC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "gpu_infrastructure.GPUTier.BASIC", "name": "BASIC", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "basic"}, "type_ref": "builtins.str"}}}, "EXCELLENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "gpu_infrastructure.GPUTier.EXCELLENT", "name": "EXCELLENT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "excellent"}, "type_ref": "builtins.str"}}}, "GOOD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "gpu_infrastructure.GPUTier.GOOD", "name": "GOOD", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "good"}, "type_ref": "builtins.str"}}}, "PREMIUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "gpu_infrastructure.GPUTier.PREMIUM", "name": "PREMIUM", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "premium"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "gpu_infrastructure.GPUTier.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "gpu_infrastructure.GPUTier", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "gpu_infrastructure.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "gpu_infrastructure.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "gpu_infrastructure.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "gpu_infrastructure.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "gpu_infrastructure.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "gpu_infrastructure.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "aiohttp": {".class": "SymbolTableNode", "cross_ref": "aiohttp", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "create_basic_gpu_infrastructure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "gpu_infrastructure.create_basic_gpu_infrastructure", "name": "create_basic_gpu_infrastructure", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_basic_gpu_infrastructure", "ret_type": {".class": "TupleType", "implicit": false, "items": ["gpu_infrastructure.BasicGPUManager", "gpu_infrastructure.BasicProcessingCoordinator"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "gpu_infrastructure.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\gpu_infrastructure.py"}