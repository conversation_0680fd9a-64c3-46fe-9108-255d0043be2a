#!/usr/bin/env python3
"""
Test if the tool is using the updated code with help intent detection
"""

import requests
import json

# Configuration
OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_tool_version():
    """Test if the tool has the updated help intent detection"""
    
    session = requests.Session()
    session.headers.update({
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    })
    
    print("🔍 Testing Tool Version/Update Status")
    print("=" * 50)
    
    # Test a query that should definitely work if the tool is loaded
    test_queries = [
        ("list codebases", "Should trigger codebase_management intent"),
        ("get stats for utils", "Should trigger codebase_management intent"),
        ("help", "Should trigger help intent (if updated)")
    ]
    
    for query, expected in test_queries:
        print(f"\n📤 Testing: '{query}'")
        print(f"💡 Expected: {expected}")
        
        payload = {
            "model": "llama3:latest",
            "messages": [
                {
                    "role": "user",
                    "content": query
                }
            ],
            "tool_ids": ["code_analyzer_tool"],
            "stream": False,
            "temperature": 0.7,
            "max_tokens": 200  # Short response to avoid timeout
        }
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json=payload,
                timeout=30  # Shorter timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                if query == "list codebases":
                    if "Available Codebases" in content or "utils" in content:
                        print("✅ Tool is working - codebase management detected")
                    else:
                        print("❌ Tool may not be working")
                        
                elif query == "get stats for utils":
                    if "479" in content or "documents" in content.lower():
                        print("✅ Tool is working - stats query detected")
                    else:
                        print("❌ Tool may not be working")
                        
                elif query == "help":
                    if "Enhanced Multi-Language Code Analysis System" in content:
                        print("✅ HELP INTENT IS WORKING! Tool updated successfully")
                    elif len(content) < 100:
                        print("⏳ TIMEOUT/SHORT RESPONSE - Help intent likely not working")
                    else:
                        print("❌ Help intent not working - got generic LLM response")
                        
                print(f"📄 Response length: {len(content)} chars")
                if len(content) < 200:
                    print(f"📄 Full response: {content}")
                else:
                    print(f"📄 Response preview: {content[:150]}...")
                    
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print("❌ TIMEOUT - Query taking too long")
            if query == "help":
                print("💡 This confirms help intent is not working (falling to LLM)")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("📊 DIAGNOSIS:")
    print("If 'list codebases' works but 'help' times out:")
    print("  → Tool is loaded but help intent detection not working")
    print("If all queries timeout:")
    print("  → Tool may not be loaded or server issues")

if __name__ == "__main__":
    test_tool_version()
