{"runtimeTarget": {"name": ".NETStandard,Version=v2.0/", "signature": ""}, "compilationOptions": {}, "targets": {".NETStandard,Version=v2.0": {}, ".NETStandard,Version=v2.0/": {"Essenbee.Z80/1.0.1": {"dependencies": {"Microsoft.CodeAnalysis.FxCopAnalyzers": "2.9.8", "NETStandard.Library": "2.0.3"}, "runtime": {"Essenbee.Z80.dll": {}}}, "Microsoft.CodeAnalysis.FxCopAnalyzers/2.9.8": {"dependencies": {"Microsoft.CodeAnalysis.VersionCheckAnalyzer": "2.9.8", "Microsoft.CodeQuality.Analyzers": "2.9.8", "Microsoft.NetCore.Analyzers": "2.9.8", "Microsoft.NetFramework.Analyzers": "2.9.8"}}, "Microsoft.CodeAnalysis.VersionCheckAnalyzer/2.9.8": {}, "Microsoft.CodeQuality.Analyzers/2.9.8": {}, "Microsoft.NetCore.Analyzers/2.9.8": {}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NetFramework.Analyzers/2.9.8": {}, "NETStandard.Library/2.0.3": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}}}}, "libraries": {"Essenbee.Z80/1.0.1": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.CodeAnalysis.FxCopAnalyzers/2.9.8": {"type": "package", "serviceable": true, "sha512": "sha512-FZhO7S+xinFrcRvPyIxPbFgY0Jg3X+KNkawhg7wwoVxnT/ySdoO162dyIMXfQQ6/qCmjNFHmTDXiNlpPNvKNyQ==", "path": "microsoft.codeanalysis.fxcopanalyzers/2.9.8", "hashPath": "microsoft.codeanalysis.fxcopanalyzers.2.9.8.nupkg.sha512"}, "Microsoft.CodeAnalysis.VersionCheckAnalyzer/2.9.8": {"type": "package", "serviceable": true, "sha512": "sha512-iSjqkECKpagJPjzc7sMjD6rQWSznVYqTfUZ96yR0r0jDzA45TymBof350tlOfGDRvn4OrU/KKJav21/oZDttrA==", "path": "microsoft.codeanalysis.versioncheckanalyzer/2.9.8", "hashPath": "microsoft.codeanalysis.versioncheckanalyzer.2.9.8.nupkg.sha512"}, "Microsoft.CodeQuality.Analyzers/2.9.8": {"type": "package", "serviceable": true, "sha512": "sha512-KevAiJKuolGKK84jDHMVGJjBAigH/86xyCVtbOzh5avUYzW7jPErdJW708byKKMiVKTwv027mbKCNnnOgNQHsA==", "path": "microsoft.codequality.analyzers/2.9.8", "hashPath": "microsoft.codequality.analyzers.2.9.8.nupkg.sha512"}, "Microsoft.NetCore.Analyzers/2.9.8": {"type": "package", "serviceable": true, "sha512": "sha512-zbGttCZ8T5wJiBDhgIaFWTrYa/X7zbCnQ76PEu/B4gb64KY+yB9gjkndsKWR+2TbOId76PN7WrSQAn+W7gr5jQ==", "path": "microsoft.netcore.analyzers/2.9.8", "hashPath": "microsoft.netcore.analyzers.2.9.8.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NetFramework.Analyzers/2.9.8": {"type": "package", "serviceable": true, "sha512": "sha512-8fZYJqeKBW5uuKBact26IeOBogchn5Km85klHqHneRY7Jxp+ERtrw8zJVumNFUVL68pIcf4uKPOY7zfBQ7eY3A==", "path": "microsoft.netframework.analyzers/2.9.8", "hashPath": "microsoft.netframework.analyzers.2.9.8.nupkg.sha512"}, "NETStandard.Library/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "path": "netstandard.library/2.0.3", "hashPath": "netstandard.library.2.0.3.nupkg.sha512"}}}