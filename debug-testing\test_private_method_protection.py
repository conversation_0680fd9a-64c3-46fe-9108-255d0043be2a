#!/usr/bin/env python3
"""
Test that private methods are properly protected from OpenWebUI
"""

import sys
import os
import asyncio

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from open_webui_code_analyzer_tool import Tools

def test_private_method_protection():
    """Test that private methods can't be called directly"""
    print("🧪 Testing Private Method Protection")
    print("=" * 50)
    
    tool = Tools()
    
    # Test private methods that were causing issues
    private_methods_to_test = [
        '_determine_optimal_context_format',
        '_ensure_chunk_clarification',
        '_add_chunk_clarification',
        '_get_available_codebase_names',
        '_handle_management_query'
    ]
    
    for method_name in private_methods_to_test:
        print(f"\n🔍 Testing: {method_name}")
        
        try:
            # Try to get the method
            method = getattr(tool, method_name)
            
            # Try to call it
            if asyncio.iscoroutinefunction(method):
                # Async method
                result = asyncio.run(method())
            else:
                # Sync method
                result = method()
            
            print(f"Result: {result}")
            
            # Check if it's the error message
            if "not available for direct use" in str(result):
                print("✅ PROTECTED: Returns error message")
            else:
                print("❌ NOT PROTECTED: Method executed normally")
                
        except AttributeError as e:
            print(f"✅ PROTECTED: AttributeError - {e}")
        except TypeError as e:
            print(f"⚠️ PROTECTED: TypeError (missing args) - {e}")
        except Exception as e:
            print(f"❓ UNKNOWN: {type(e).__name__} - {e}")

def test_public_methods():
    """Test that public methods still work"""
    print(f"\n🧪 Testing Public Method Access")
    print("=" * 50)
    
    tool = Tools()
    
    # Test public methods
    public_methods = [
        'list_codebases',
        'select_codebase', 
        'get_codebase_stats',
        'get_code_context',
        'smart_code_context'
    ]
    
    for method_name in public_methods:
        print(f"\n🔍 Testing: {method_name}")
        
        try:
            method = getattr(tool, method_name)
            print(f"✅ ACCESSIBLE: Method found")
            
            # Check if it's callable
            if callable(method):
                print("✅ CALLABLE: Method is callable")
            else:
                print("❌ NOT CALLABLE: Method exists but not callable")
                
        except AttributeError as e:
            print(f"❌ NOT ACCESSIBLE: {e}")

def test_dir_method():
    """Test that __dir__ hides private methods"""
    print(f"\n🧪 Testing __dir__ Method (Method Discovery)")
    print("=" * 50)
    
    tool = Tools()
    
    # Get visible methods
    visible_methods = dir(tool)
    
    print(f"Total visible methods: {len(visible_methods)}")
    
    # Check for private methods in visible list
    private_methods_found = []
    for method in visible_methods:
        if method.startswith('_') and method in tool._private_methods:
            private_methods_found.append(method)
    
    if private_methods_found:
        print(f"❌ ISSUE: Private methods visible: {private_methods_found}")
    else:
        print("✅ GOOD: No private methods visible in dir()")
    
    # Show some visible methods
    public_methods = [m for m in visible_methods if not m.startswith('_')]
    print(f"Sample public methods: {public_methods[:10]}")

async def test_main_call_method():
    """Test that the main __call__ method still works"""
    print(f"\n🧪 Testing Main __call__ Method")
    print("=" * 50)
    
    tool = Tools()
    
    # Mock valves
    class MockValves:
        def __init__(self):
            self.CODE_ANALYZER_SERVER_URL = "http://home-ai-server.local:5002"
            self.current_codebase = None
    
    tool.valves = MockValves()
    
    try:
        result = await tool.__call__("list codebases")
        print(f"✅ WORKING: Main call method works")
        print(f"Result preview: {str(result)[:100]}...")
    except Exception as e:
        print(f"❌ ISSUE: Main call method error - {e}")

def main():
    """Main test function"""
    print("🔧 Private Method Protection Test")
    print("=" * 70)
    print("Testing that OpenWebUI can't call private methods directly")
    
    # Test private method protection
    test_private_method_protection()
    
    # Test public method access
    test_public_methods()
    
    # Test method discovery
    test_dir_method()
    
    # Test main call method
    asyncio.run(test_main_call_method())
    
    print(f"\n🎯 Summary:")
    print("Private methods should return error messages or raise AttributeError")
    print("Public methods should be accessible and callable")
    print("Main __call__ method should work normally")

if __name__ == "__main__":
    main()
