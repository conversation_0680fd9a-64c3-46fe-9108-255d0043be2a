.dropdown-menu{
    margin: 0;
    padding: 0;
    border-radius: $border-radius-extreme;
    z-index: 9000;
    @include box-shadow($dropdown-shadow);

    > li > a {
       padding: $padding-base-vertical $padding-base-horizontal;
       color: #333333;
    }
    > li > a:focus{
        outline: 0 !important;
    }

    > li:first-child > a{
       border-top-left-radius: $border-radius-extreme;
       border-top-right-radius: $border-radius-extreme;
    }

    > li:last-child > a{
        border-bottom-left-radius: $border-radius-extreme;
        border-bottom-right-radius: $border-radius-extreme;
    }

    > li > a:hover,
    > li > a:focus {
        background-color: $smoke-bg;
        color: #333333;
        opacity: 1;
        text-decoration: none;
    }

    > .active > a{
        &,
        &:focus,
        &:hover{
            background-color: $info-bg;
        }
    }
}


//fix bug for the select items in btn-group
.btn-group.select{
    overflow: hidden;
}
.btn-group.select.open{
    overflow: visible;
}

