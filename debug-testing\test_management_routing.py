#!/usr/bin/env python3
"""
Test if management routing is working at all
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_management_routing():
    """Test if management queries are being routed correctly"""
    print("🧪 Testing Management Routing")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test queries that should definitely trigger management routing
    test_queries = [
        ("list codebases", "Should list all codebases"),
        ("select codebase utils", "Should select utils codebase"),
        ("get stats for utils", "Should get utils statistics"),
        ("show available codebases", "Should show available codebases"),
    ]
    
    for query, expected in test_queries:
        print(f"\n🔍 Testing: '{query}'")
        print(f"Expected: {expected}")
        print("-" * 40)
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": query}],
                    "tool_ids": ["code_analyzer_tool"],
                    "stream": False,
                    "max_tokens": 800
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                print(f"✅ Response received ({len(content)} chars)")
                
                # Check for management-specific responses
                management_indicators = [
                    "Here is the list of codebases:",
                    "utils",
                    "test_project", 
                    "z80emu",
                    "modbus",
                    "networking_project",
                    "No codebase selected for statistics.",
                    "Codebase utils selected",
                    "Total chunks: 479",
                    "Unique files: 43"
                ]
                
                # Check for generic LLM responses
                generic_indicators = [
                    "I'll help you",
                    "Here are some",
                    "To get statistics",
                    "blast from the past",
                    "energy consumption",
                    "Lines of Code (LOC)"
                ]
                
                found_management = [ind for ind in management_indicators if ind in content]
                found_generic = [ind for ind in generic_indicators if ind in content]
                
                if found_management and not found_generic:
                    print(f"🎉 MANAGEMENT ROUTING WORKING: {found_management[:2]}")
                    status = "management"
                elif found_generic:
                    print(f"❌ GENERIC LLM RESPONSE: {found_generic[:2]}")
                    status = "generic"
                elif "No codebase selected for statistics." in content:
                    print("⚠️ NO CODEBASE SELECTED MESSAGE")
                    status = "no_codebase"
                else:
                    print("❓ UNCLEAR RESPONSE TYPE")
                    status = "unclear"
                
                # Show preview
                preview = content[:150] + "..." if len(content) > 150 else content
                print(f"Preview: {preview}")
                
                # Special check for the "No codebase selected" message
                if query == "get stats for utils" and "No codebase selected for statistics." in content:
                    print("🎯 FOUND THE ISSUE: Codebase selection not working!")
                
            else:
                print(f"❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def test_codebase_selection_state():
    """Test if codebase selection is persisting"""
    print(f"\n🔍 Testing Codebase Selection State")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # First select a codebase
    print("Step 1: Selecting codebase...")
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "select codebase utils"}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 500
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"Selection response: {content[:100]}...")
        
    except Exception as e:
        print(f"Selection error: {e}")
    
    # Then try to get stats
    print("\nStep 2: Getting stats after selection...")
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "get stats"}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 800
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            if "No codebase selected for statistics." in content:
                print("❌ CODEBASE SELECTION NOT PERSISTING!")
                print("The selection doesn't carry over between requests")
            elif "Total chunks: 479" in content:
                print("✅ CODEBASE SELECTION WORKING!")
                print("Selection persisted and stats returned")
            else:
                print("⚠️ UNCLEAR: Different response type")
            
            print(f"Stats response: {content[:150]}...")
        
    except Exception as e:
        print(f"Stats error: {e}")

def main():
    """Main test function"""
    print("🔧 Management Routing Debug")
    print("=" * 60)
    print("Testing if management queries reach the tool at all")
    
    # Test management routing
    test_management_routing()
    
    # Test codebase selection persistence
    test_codebase_selection_state()
    
    print(f"\n🎯 Key Questions:")
    print("1. Are management queries reaching the tool?")
    print("2. Is codebase selection working?")
    print("3. Is the 'No codebase selected' message appearing?")

if __name__ == "__main__":
    main()
