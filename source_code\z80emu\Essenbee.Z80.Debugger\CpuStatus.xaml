﻿<UserControl x:Class="Essenbee.Z80.Debugger.CpuStatus"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:controls="clr-namespace:Essenbee.Z80.Debugger"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">

    <Grid>
        <StackPanel Grid.Row="0" Orientation="Vertical">

            <controls:CpuFlags />

            <StackPanel Orientation="Horizontal">
                <Label Content="AF:"
                       Width="25"
                       Margin="30,2,0,2"/>
                <TextBox x:Name="AF"
                             Margin="0,2,2,2"
                             Width="40"
                             VerticalAlignment="Center"
                             Text="{Binding AccuFlags}" />
                <Label Content="AF':"
                       Width="25"
                       Margin="2,2,0,2"/>
                <TextBox x:Name="ShadowAF"
                             Margin="0,2,2,2"
                             Width="40"
                             VerticalAlignment="Center"
                             Text="{Binding AccuFlagsPrime}" />
            </StackPanel>

            <StackPanel Orientation="Horizontal">
                <Label Content="HL:"
                       Width="25"
                       Margin="30,2,0,2"/>
                <TextBox x:Name="PrimeHL"
                             Margin="0,2,2,2"
                             Width="40"
                             VerticalAlignment="Center"
                             Text="{Binding HLPair}" />
                <Label Content="HL':"
                       Width="25"
                       Margin="2,2,0,2"/>
                <TextBox x:Name="AltHL"
                             Margin="0,2,2,2"
                             Width="40"
                             VerticalAlignment="Center"
                             Text="{Binding HLPairPrime}" />
            </StackPanel>

            <StackPanel Orientation="Horizontal">
                <Label Content="BC:"
                       Width="25"
                       Margin="30,2,0,2"/>
                <TextBox x:Name="PrimeBC"
                             Margin="0,2,2,2"
                             Width="40"
                             VerticalAlignment="Center"
                             Text="{Binding BCPair}" />
                <Label Content="BC':"
                       Width="25"
                       Margin="2,2,0,2"/>
                <TextBox x:Name="AltBC"
                             Margin="0,2,2,2"
                             Width="40"
                             VerticalAlignment="Center"
                             Text="{Binding BCPairPrime}" />
            </StackPanel>

            <StackPanel Orientation="Horizontal">
                <Label Content="DE:"
                       Width="25"
                       Margin="30,2,0,2"/>
                <TextBox x:Name="PrimeDE"
                             Margin="0,2,2,2"
                             Width="40"
                             VerticalAlignment="Center"
                             Text="{Binding DEPair}" />
                <Label Content="DE':"
                       Width="25"
                       Margin="2,2,0,2"/>
                <TextBox x:Name="AltDE"
                             Margin="0,2,2,2"
                             Width="40"
                             VerticalAlignment="Center"
                             Text="{Binding DEPairPrime}" />
            </StackPanel>

            <StackPanel Orientation="Horizontal">
                <Label Content="PC:"
                       Width="25"
                       Margin="30,2,0,2"/>
                <TextBox x:Name="PC"
                             Margin="0,2,2,2"
                             Width="40"
                             VerticalAlignment="Center"
                             Text="{Binding ProgramCounter}" />
                <Label Content="SP:"
                       Width="25"
                       Margin="2,2,0,2"/>
                <TextBox x:Name="StackPtr"
                             Margin="0,2,2,2"
                             Width="40"
                             VerticalAlignment="Center"
                             Text="{Binding StackPointer}" />
            </StackPanel>

            <StackPanel Orientation="Horizontal">
                <Label Content="IX:"
                       Width="25"
                       Margin="30,2,0,2"/>
                <TextBox x:Name="IX"
                             Margin="0,2,2,2"
                             Width="40"
                             VerticalAlignment="Center"
                             Text="{Binding IndexX}" />
                <Label Content="IY:"
                       Width="25"
                       Margin="2,2,0,2"/>
                <TextBox x:Name="IY"
                             Margin="0,2,2,2"
                             Width="40"
                             VerticalAlignment="Center"
                             Text="{Binding IndexY}" />
            </StackPanel>

            <StackPanel Orientation="Horizontal">
                <Label Content="I:"
                       Width="25"
                       Margin="30,2,0,2"/>
                <TextBox x:Name="I"
                             Margin="0,2,2,2"
                             Width="40"
                             VerticalAlignment="Center"
                             Text="{Binding InterruptVector}" />
                <Label Content="R:"
                       Width="25"
                       Margin="2,2,0,2"/>
                <TextBox x:Name="R"
                             Margin="0,2,2,2"
                             Width="40"
                             VerticalAlignment="Center"
                             Text="{Binding Refresh}" />

                <Label Content="IM:"
                       Width="25"
                       Margin="0,2,0,2"/>
                <TextBox x:Name="IM"
                         Margin="0,2,2,2"
                         Width="20"
                         IsReadOnly="true"
                         VerticalAlignment="Center"
                         Text="{Binding Mode}" />
            </StackPanel>


            <StackPanel Orientation="Horizontal">
                <Label Content="Q:"
                       Width="25"
                       Margin="30,2,0,2"/>
                <TextBox x:Name="Q"
                         Margin="0,2,2,2"
                         Width="20"
                         IsReadOnly="True"
                         VerticalAlignment="Center"
                         Text="{Binding QRegister}" />
                <Label Content="MEMPTR:"
                       Width="60"
                       Margin="20,2,15,2"/>
                <TextBox x:Name="MemPtr"
                         Margin="0,2,2,2"
                         IsReadOnly="True"
                         Width="40"
                         VerticalAlignment="Center"
                         Text="{Binding MemPointer}" />
            </StackPanel>
        </StackPanel>

    </Grid>
</UserControl>
