﻿<UserControl x:Class="Essenbee.Z80.Debugger.StackDisplay"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:controls="clr-namespace:Essenbee.Z80.Debugger"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <DataGrid x:Name="TheStack"
                  VerticalScrollBarVisibility="Visible"
                  HorizontalScrollBarVisibility="Hidden"
                  AutoGenerateColumns="False" 
                  IsReadOnly="True"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  RowBackground="LightGray"
                  SelectionMode="Single">
            <DataGrid.Columns>
                <DataGridTextColumn 
                        Header="Address" 
                        IsReadOnly="True" 
                        Width="100" 
                        Binding="{Binding Path=Key}" />
                <DataGridTextColumn 
                        Header="Contents" 
                        IsReadOnly="True" 
                        Width="100"
                        Binding="{Binding Path=Value}"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</UserControl>
