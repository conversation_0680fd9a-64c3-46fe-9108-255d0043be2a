// src/index.ts
import { startServer } from './server';

startServer();

// src/server.ts
import express from 'express';
import bodyParser from 'body-parser';
import { BookRouter } from './routes/book';
import { UserRouter } from './routes/user';
import { OrderRouter } from './routes/order';
import swaggerUi from 'swagger-ui-express';
import swaggerDocument from './docs/swagger.json';

export function startServer() {
  const app = express();
  app.use(bodyParser.json());
  app.use('/books', BookRouter);
  app.use('/users', UserRouter);
  app.use('/orders', OrderRouter);
  app.use('/docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument));

  app.listen(3000, () => {
    console.log('Server running on http://localhost:3000');
  });
}

// src/types/enums.ts
export enum UserRole {
  ADMIN = 'admin',
  CUSTOMER = 'customer',
}

export enum OrderStatus {
  PENDING = 'pending',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELED = 'canceled',
}

// src/types/utils.ts
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// src/models/user.ts
import { UserRole } from '../types/enums';

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
}

export class UserModel {
  private users: Map<string, User> = new Map();

  async add(user: User): Promise<User> {
    this.users.set(user.id, user);
    return user;
  }

  async get(id: string): Promise<User | undefined> {
    return this.users.get(id);
  }

  async list(): Promise<User[]> {
    return Array.from(this.users.values());
  }
}

// src/models/order.ts
import { OrderStatus } from '../types/enums';

export interface Order {
  id: string;
  userId: string;
  bookIds: string[];
  status: OrderStatus;
  createdAt: Date;
}

export class OrderModel {
  private orders: Map<string, Order> = new Map();

  async add(order: Order): Promise<Order> {
    this.orders.set(order.id, order);
    return order;
  }

  async list(): Promise<Order[]> {
    return Array.from(this.orders.values());
  }
}

// src/routes/user.ts
import express from 'express';
import { UserModel } from '../models/user';
import { UserRole } from '../types/enums';
import { v4 as uuidv4 } from 'uuid';

export const UserRouter = express.Router();
const model = new UserModel();

UserRouter.post('/', async (req, res) => {
  const user = {
    id: uuidv4(),
    name: req.body.name,
    email: req.body.email,
    role: req.body.role || UserRole.CUSTOMER,
  };
  const created = await model.add(user);
  res.status(201).json(created);
});

UserRouter.get('/', async (_req, res) => {
  const users = await model.list();
  res.json(users);
});

// src/routes/order.ts
import express from 'express';
import { OrderModel } from '../models/order';
import { OrderStatus } from '../types/enums';
import { v4 as uuidv4 } from 'uuid';

export const OrderRouter = express.Router();
const model = new OrderModel();

OrderRouter.post('/', async (req, res) => {
  const order = {
    id: uuidv4(),
    userId: req.body.userId,
    bookIds: req.body.bookIds,
    status: OrderStatus.PENDING,
    createdAt: new Date(),
  };
  const created = await model.add(order);
  res.status(201).json(created);
});

OrderRouter.get('/', async (_req, res) => {
  const orders = await model.list();
  res.json(orders);
});

// src/docs/swagger.json
{
  "openapi": "3.0.0",
  "info": {
    "title": "Bookstore API",
    "version": "1.0.0"
  },
  "paths": {
    "/books": {
      "get": {
        "summary": "List books",
        "responses": {
          "200": {
            "description": "A list of books"
          }
        }
      }
    },
    "/users": {
      "get": {
        "summary": "List users",
        "responses": {
          "200": {
            "description": "A list of users"
          }
        }
      }
    },
    "/orders": {
      "get": {
        "summary": "List orders",
        "responses": {
          "200": {
            "description": "A list of orders"
          }
        }
      }
    }
  }
}

// test/user.test.ts
import request from 'supertest';
import express from 'express';
import { UserRouter } from '../src/routes/user';

describe('User API', () => {
  const app = express();
  app.use(express.json());
  app.use('/users', UserRouter);

  it('should create a user', async () => {
    const res = await request(app).post('/users').send({
      name: 'John Doe',
      email: '<EMAIL>'
    });
    expect(res.statusCode).toEqual(201);
    expect(res.body.name).toEqual('John Doe');
  });
});

// test/order.test.ts
import request from 'supertest';
import express from 'express';
import { OrderRouter } from '../src/routes/order';

describe('Order API', () => {
  const app = express();
  app.use(express.json());
  app.use('/orders', OrderRouter);

  it('should create an order', async () => {
    const res = await request(app).post('/orders').send({
      userId: 'some-user-id',
      bookIds: ['book-1', 'book-2']
    });
    expect(res.statusCode).toEqual(201);
    expect(res.body.bookIds.length).toBeGreaterThan(0);
  });
});
