# Enhanced RAG Server for C/C++ Code Analysis

A comprehensive tool server that provides intelligent code analysis capabilities for C/C++ codebases using RAG (Retrieval-Augmented Generation) technology.

## Features

### 🚀 **Multi-Codebase Support**
- Manage multiple C/C++ projects independently
- Switch between codebases seamlessly
- Isolated vector databases for each project

### 🔍 **Intelligent Code Analysis**
- Semantic code search using vector embeddings
- AI-powered code explanations and insights
- Comprehensive preprocessing with tree-sitter parsing

### 🛠️ **Advanced Processing Pipeline**
- **Code Preprocessing**: Extract functions, classes, methods, structs, enums, templates
- **Vector Database Creation**: ChromaDB with automatic embeddings
- **Ollama Integration**: AI-powered analysis and explanations

## Directory Structure

```
openwebui_rag_server/
├── main.py                    # Enhanced RAG server with multi-codebase support
├── code_preprocessor.py       # C/C++ code parsing and chunk extraction
├── vector_db_creator.py       # ChromaDB collection creation and management
├── open-webui-rag-plugin.py   # OpenWebUI plugin with enhanced tools
├── Dockerfile                 # Container configuration
├── requirements.txt           # Python dependencies
└── README.md                  # This file
```

## Installation

### Prerequisites
- Python 3.10+
- Docker and Docker Compose
- Access to Ollama service

### Setup Steps

1. **Create the directory structure:**
```bash
mkdir -p openwebui_rag_server
cd openwebui_rag_server
```

2. **Copy all files to the directory**

3. **Prepare source code:**
```bash
mkdir -p ../source_code
# Place your C/C++ codebases in subdirectories:
# ../source_code/project1/
# ../source_code/project2/
# etc.
```

4. **Build and run:**
```bash
docker-compose up --build
```

## Usage

### 1. **List Available Codebases**
```python
list_codebases()
```
Shows all codebases in the source_code directory with their indexing status.

### 2. **Process a New Codebase**
```python
process_codebase("my_project", exclude_dirs=["build", "test"])
```
Analyzes source code and creates searchable vector database.

### 3. **Select Working Codebase**
```python
select_codebase("my_project")
```
Sets the active codebase for subsequent operations.

### 4. **Search Code**
```python
search_code("TCP connection", "my_project", filter_type="function")
```
Finds relevant code snippets using semantic search.

### 5. **Ask Questions**
```python
ask_about_code("How does error handling work?", "my_project")
```
Gets AI-powered explanations about code functionality.

### 6. **Get Statistics**
```python
get_codebase_stats("my_project")
```
View metrics about indexed code chunks and structure.

## Code Processing Features

### **Supported Code Structures**
- **Functions**: Complete function definitions with signatures
- **Classes**: C++ class definitions with methods (C++)
- **Methods**: Individual class member functions (C++)
- **Namespaces**: C++ namespace definitions (C++)
- **Structs**: Structure definitions
- **Enums**: Enumeration definitions
- **Typedefs**: Type definitions
- **Templates**: Template definitions (C++)
- **Headers**: Include files and macro definitions

### **File Support**
- **C Files**: `.c`, `.h`
- **C++ Files**: `.cpp`, `.cxx`, `.cc`, `.c++`, `.hpp`, `.hxx`, `.hh`

### **Processing Pipeline**
1. **Tree-sitter Parsing**: Accurate syntax analysis
2. **Chunk Extraction**: Semantic code segments
3. **Metadata Enhancement**: Rich context information
4. **Vector Embedding**: ChromaDB with semantic search
5. **Indexing**: Fast retrieval and filtering

## API Endpoints

### **Tool Endpoints (for OpenWebUI)**
- `POST /tools/list_codebases` - List available codebases
- `POST /tools/select_codebase` - Select active codebase
- `POST /tools/process_codebase` - Process and index codebase
- `POST /tools/search_code` - Search for code snippets
- `POST /tools/ask_about_code` - AI-powered code analysis
- `POST /tools/get_code_stats` - Get codebase statistics

### **Direct API Endpoints**
- `GET /health` - System health check
- `GET /stats` - Current codebase statistics
- `POST /search` - Direct search interface
- `POST /ask` - Direct AI query interface

## Configuration

### **Environment Variables**
```bash
OLLAMA_HOST=http://ollama:11434              # Ollama service URL
CHROMA_DB_BASE_PATH=./chroma_db              # ChromaDB storage path
SOURCE_CODE_BASE_PATH=./source_code          # Source code directory
```

### **Docker Compose Integration**
```yaml
openwebui-rag-server:
  container_name: openwebui-rag-server
  build:
    context: ./openwebui_rag_server
  ports:
    - "5002:5002"
  environment:
    - OLLAMA_HOST=http://ollama:11434
  volumes:
    - chroma_db:/app/chroma_db
    - ./source_code:/app/source_code:ro
  depends_on:
    - ollama
```

## Examples

### **Processing a New Codebase**
```python
# 1. Check what's available
list_codebases()

# 2. Process new codebase
process_codebase("protocol_stack", exclude_dirs=["build", "test", "docs"])

# 3. Select for analysis
select_codebase("protocol_stack")

# 4. Get overview
get_codebase_stats("protocol_stack")
```

### **Code Analysis Workflow**
```python
# Search for specific functionality
search_code("buffer management", "protocol_stack", filter_type="function")

# Ask architectural questions
ask_about_code("How is memory managed in this codebase?", "protocol_stack")

# Find specific implementations
search_code("TCP socket", "protocol_stack", filter_file="network")

# Understand design patterns
ask_about_code("What design patterns are used?", "protocol_stack")
```

### **Multi-Codebase Analysis**
```python
# Compare implementations across projects
select_codebase("project_a")
ask_about_code("How is logging implemented?", "project_a")

select_codebase("project_b") 
ask_about_code("How is logging implemented?", "project_b")

# Find similar functionality
search_code("error handling", "project_a", n_results=5)
search_code("error handling", "project_b", n_results=5)
```

## Troubleshooting

### **Common Issues**

**No results from search:**
- Verify codebase is processed: `get_codebase_stats("codebase_name")`
- Check codebase selection: `select_codebase("codebase_name")`
- Try broader search terms

**Processing fails:**
- Check source code directory structure
- Verify file permissions
- Review excluded directories list

**Slow responses:**
- Reduce `n_results` parameter
- Add more specific filters
- Check Ollama service performance

### **Health Checks**
```python
# System status
check_rag_system_status()

# Debug connectivity
debug_rag_request("test", "my_project", "search")
```

## Performance Tips

1. **Use filters** to narrow search scope
2. **Start with smaller `n_results`** for faster responses
3. **Process codebases incrementally** rather than all at once
4. **Use specific technical terms** in searches
5. **Leverage multiple codebases** for comparative analysis

## Integration with OpenWebUI

The enhanced RAG server integrates seamlessly with OpenWebUI through the provided plugin. Users can access all functionality through natural language commands in the chat interface.

**Plugin Features:**
- Multi-codebase management
- Intelligent code search
- AI-powered explanations
- Comprehensive help system
- Debug and troubleshooting tools

## License

[Add your license information here]