#!/usr/bin/env python3
"""
Test script to verify that the division by zero fixes work correctly
when processing empty or minimal codebases.
"""

from vector_db_creator import VectorDBCreator
from code_preprocessor import MultiLanguageCodeProcessor
import tempfile
import shutil
from pathlib import Path

def test_empty_codebase_processing():
    """Test processing an empty codebase to ensure no division by zero errors"""
    
    # Create temporary directory
    temp_dir = tempfile.mkdtemp()
    
    try:
        print("🧪 Testing division by zero fixes...")
        print("=" * 50)
        
        # Test 1: Empty directory
        print("\n1. Testing empty directory processing...")
        processor = MultiLanguageCodeProcessor(temp_dir)
        chunks = processor.process_repository(temp_dir)
        print(f"   ✅ Empty directory processed: {len(chunks)} chunks")
        
        # Test 2: Directory with empty file
        print("\n2. Testing directory with empty TypeScript file...")
        empty_ts_file = Path(temp_dir) / "empty.ts"
        empty_ts_file.write_text("")
        
        chunks = processor.process_repository(temp_dir)
        print(f"   ✅ Empty TypeScript file processed: {len(chunks)} chunks")

        # Test 3: Directory with minimal TypeScript content
        print("\n3. Testing directory with minimal TypeScript content...")
        minimal_ts_file = Path(temp_dir) / "minimal.ts"
        minimal_ts_file.write_text("// Just a comment\n")

        chunks = processor.process_repository(temp_dir)
        print(f"   ✅ Minimal TypeScript file processed: {len(chunks)} chunks")
        
        # Test 4: VectorDBCreator with empty chunks
        print("\n4. Testing VectorDBCreator with empty chunks...")
        creator = VectorDBCreator(use_ollama=False)
        
        # Test the print_enhanced_summary method with empty chunks
        try:
            creator.print_enhanced_summary([])
            print("   ✅ Enhanced summary with empty chunks completed")
        except ZeroDivisionError as e:
            print(f"   ❌ Division by zero error: {e}")
            return False
        except Exception as e:
            print(f"   ⚠️  Other error (not division by zero): {e}")
        
        # Test 5: Code preprocessor summary with empty chunks
        print("\n5. Testing code preprocessor summary with empty chunks...")
        try:
            processor.print_summary([])
            print("   ✅ Code preprocessor summary with empty chunks completed")
        except ZeroDivisionError as e:
            print(f"   ❌ Division by zero error: {e}")
            return False
        except Exception as e:
            print(f"   ⚠️  Other error (not division by zero): {e}")
        
        print("\n" + "=" * 50)
        print("🎉 All division by zero fixes are working correctly!")
        return True
        
    finally:
        # Cleanup
        shutil.rmtree(temp_dir)

def test_bookstore_scenario():
    """Test the specific bookstore scenario that was causing issues"""
    
    print("\n🏪 Testing bookstore-like scenario...")
    print("=" * 50)
    
    # Create temporary bookstore-like structure
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Create a minimal TypeScript file similar to what might be in bookstore
        bookstore_dir = Path(temp_dir) / "bookstore"
        bookstore_dir.mkdir()
        
        # Create a TypeScript file with minimal content
        ts_file = bookstore_dir / "types.ts"
        ts_file.write_text("""
// TypeScript interfaces for bookstore
export interface Book {
  id: string;
  title: string;
}
""")
        
        print(f"Created test bookstore at: {bookstore_dir}")
        print(f"TypeScript file content: {ts_file.read_text()}")
        
        # Process the bookstore directory
        processor = MultiLanguageCodeProcessor(str(bookstore_dir))
        chunks = processor.process_repository(str(bookstore_dir))
        
        print(f"✅ Bookstore processing completed: {len(chunks)} chunks")
        
        # Test summary generation
        if len(chunks) == 0:
            print("⚠️  No chunks generated - this simulates the original bookstore issue")
            processor.print_summary(chunks)
            print("✅ Summary generation handled empty chunks gracefully")
        else:
            print(f"✅ Generated {len(chunks)} chunks from bookstore")
            processor.print_summary(chunks)
        
        return True
        
    except Exception as e:
        print(f"❌ Error in bookstore scenario: {e}")
        return False
        
    finally:
        shutil.rmtree(temp_dir)

if __name__ == "__main__":
    print("🔧 Division by Zero Fix Verification")
    print("=" * 60)
    
    success1 = test_empty_codebase_processing()
    success2 = test_bookstore_scenario()
    
    if success1 and success2:
        print("\n🎉 ALL TESTS PASSED! Division by zero issues are fixed!")
        print("\n📋 Summary of fixes:")
        print("  ✅ vector_db_creator.py: Added len(chunks) > 0 checks")
        print("  ✅ code_preprocessor.py: Added len(chunks) > 0 checks")
        print("  ✅ All percentage calculations now safe")
        print("  ✅ Empty codebase processing now works")
        print("\n🚀 The bookstore indexing should now work!")
    else:
        print("\n⚠️  Some tests failed. Please review the output above.")
