#!/usr/bin/env python3
"""
Test the debug logging in the tool
"""

import sys
import os
import asyncio

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from open_webui_code_analyzer_tool import Tools

class MockValves:
    def __init__(self):
        self.CODE_ANALYZER_SERVER_URL = "http://home-ai-server.local:5002"
        self.request_timeout = 30
        self.auto_context_injection = True
        self.context_format = "detailed"
        self.current_codebase = None

async def test_debug_logging():
    """Test that debug logging shows the execution flow"""
    print("🧪 Testing Debug Logging")
    print("=" * 50)
    
    tool = Tools()
    tool.valves = MockValves()
    
    # Test the problematic query
    query = "get stats for utils"
    
    print(f"Testing query: '{query}'")
    print("-" * 40)
    
    try:
        result = await tool.__call__(query)
        
        print("FULL RESULT:")
        print("=" * 60)
        print(result)
        print("=" * 60)
        
        # Check what debug info we got
        if "🚀 PLUGIN ENTRY" in result:
            print("✅ Entry point debug working")
        else:
            print("❌ Entry point debug missing")
            
        if "🧠 INTENT DETECTED" in result:
            print("✅ Intent detection debug working")
        else:
            print("❌ Intent detection debug missing")
            
        if "🔀 ROUTING TO MANAGEMENT" in result:
            print("✅ Management routing debug working")
        else:
            print("❌ Management routing debug missing")
            
        if "🎯 EXTRACTED CODEBASE" in result:
            print("✅ Codebase extraction debug working")
        else:
            print("❌ Codebase extraction debug missing")
            
        if "Total chunks: 479" in result:
            print("✅ Real codebase data returned")
        else:
            print("❌ Still getting fake/generic data")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main test function"""
    print("🔧 Debug Logging Test")
    print("=" * 60)
    print("Testing debug output to trace execution flow")
    
    asyncio.run(test_debug_logging())

if __name__ == "__main__":
    main()
