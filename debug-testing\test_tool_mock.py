#!/usr/bin/env python3
"""
Mock testing environment for the OpenWebUI code analyzer tool
Tests the tool locally without the OpenWebUI framework
"""

import asyncio
import sys
import os

# Add the current directory to Python path so we can import the tool
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the tool
from open_webui_code_analyzer_tool import Tools

class MockEventEmitter:
    """Mock event emitter for testing"""
    def __init__(self, verbose=True):
        self.verbose = verbose
        self.events = []
    
    async def __call__(self, event):
        """Mock event emission"""
        if self.verbose:
            event_type = event.get("type", "unknown")
            data = event.get("data", {})
            description = data.get("description", "No description")
            done = data.get("done", False)
            status = "✅" if done else "🔄"
            print(f"   {status} Event: {description}")
        
        self.events.append(event)

class ToolTester:
    """Test harness for the OpenWebUI tool"""
    
    def __init__(self):
        self.tool = Tools()
        self.emitter = MockEventEmitter(verbose=True)
        
        # Override server URL to match our working server
        self.tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
        
        print(f"🔧 Tool initialized with server: {self.tool.valves.code_analyzer_server_url}")
        print(f"🔧 Caching enabled: {self.tool.valves.enable_caching}")
        print(f"🔧 Current codebase: '{self.tool.valves.current_codebase}'")
    
    async def test_basic_functions(self):
        """Test basic tool functions"""
        print("\n" + "="*60)
        print("🧪 TESTING BASIC TOOL FUNCTIONS")
        print("="*60)
        
        # Test 1: Server health
        print("\n🔧 Testing server health...")
        try:
            result = await self.tool.get_server_status(__event_emitter__=self.emitter)
            print(f"   Result: {result[:200]}...")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Test 2: List codebases
        print("\n🔧 Testing list codebases...")
        try:
            result = await self.tool.list_codebases(__event_emitter__=self.emitter)
            print(f"   Result: {result[:200]}...")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Test 3: Select codebase
        print("\n🔧 Testing select codebase...")
        try:
            result = await self.tool.select_codebase("utils", __event_emitter__=self.emitter)
            print(f"   Result: {result[:200]}...")
            print(f"   Tool codebase after selection: '{self.tool.valves.current_codebase}'")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    async def test_search_functions(self):
        """Test the search functions that are failing"""
        print("\n" + "="*60)
        print("🔍 TESTING SEARCH FUNCTIONS")
        print("="*60)
        
        # Ensure codebase is selected
        if not self.tool.valves.current_codebase:
            print("🔧 Selecting utils codebase first...")
            await self.tool.select_codebase("utils", __event_emitter__=self.emitter)
        
        # Test queries that work on server but fail in OpenWebUI
        test_queries = [
            "tmwmem_alloc",
            "tmwmem_free", 
            "TMWMEM_HEADER",
            "memory",
            "function"
        ]
        
        for query in test_queries:
            print(f"\n🔧 Testing get_code_context('{query}')...")
            try:
                result = await self.tool.get_code_context(
                    query=query,
                    codebase_name="utils",
                    n_results=3,
                    __event_emitter__=self.emitter
                )
                
                if "No relevant code context found" in result:
                    print(f"   ❌ No results found (same as OpenWebUI issue)")
                else:
                    print(f"   ✅ Results found! Length: {len(result)}")
                    print(f"   Preview: {result[:150]}...")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
    
    async def test_direct_server_calls(self):
        """Test direct server calls from within the tool"""
        print("\n" + "="*60)
        print("🌐 TESTING DIRECT SERVER CALLS")
        print("="*60)
        
        # Test the exact same calls our working script made
        import requests
        
        test_query = "tmwmem_alloc"
        
        print(f"\n🔧 Testing direct server call for '{test_query}'...")
        
        try:
            # Use the same payload format as our working script
            payload = {
                "query": test_query,
                "codebase_name": "utils",
                "n_results": 5,
                "context_preferences": None
            }
            
            print(f"   Server URL: {self.tool.valves.code_analyzer_server_url}")
            print(f"   Payload: {payload}")
            
            response = requests.post(
                f"{self.tool.valves.code_analyzer_server_url}/tools/get_optimized_context",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            print(f"   Status: {response.status_code}")
            print(f"   Response length: {len(response.text)}")
            
            if response.status_code == 200:
                data = response.json()
                result = data.get("result", "")
                if "No relevant code context found" in str(result):
                    print(f"   ❌ No results (server issue)")
                else:
                    print(f"   ✅ Results found!")
                    print(f"   Preview: {str(result)[:150]}...")
            else:
                print(f"   ❌ Server error: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Request error: {e}")
    
    async def test_cache_behavior(self):
        """Test cache behavior"""
        print("\n" + "="*60)
        print("💾 TESTING CACHE BEHAVIOR")
        print("="*60)
        
        # Test with cache enabled
        print("\n🔧 Testing with cache enabled...")
        self.tool.valves.enable_caching = True
        result1 = await self.tool.get_code_context(
            query="tmwmem_alloc",
            codebase_name="utils", 
            n_results=3,
            __event_emitter__=self.emitter
        )
        print(f"   First call result: {'Found' if 'No relevant' not in result1 else 'Not found'}")
        
        # Test cache clearing
        print("\n🔧 Testing cache clearing...")
        clear_result = await self.tool.clear_cache(__event_emitter__=self.emitter)
        print(f"   Clear result: {clear_result}")
        
        # Test with cache disabled
        print("\n🔧 Testing with cache disabled...")
        disable_result = await self.tool.disable_cache(__event_emitter__=self.emitter)
        print(f"   Disable result: {disable_result}")
        
        result2 = await self.tool.get_code_context(
            query="tmwmem_alloc",
            codebase_name="utils",
            n_results=3, 
            __event_emitter__=self.emitter
        )
        print(f"   No-cache call result: {'Found' if 'No relevant' not in result2 else 'Not found'}")
    
    async def run_all_tests(self):
        """Run comprehensive tests"""
        print("🚀 STARTING MOCK TOOL TESTING")
        print("="*80)
        
        await self.test_basic_functions()
        await self.test_search_functions() 
        await self.test_direct_server_calls()
        await self.test_cache_behavior()
        
        print("\n" + "="*80)
        print("🎯 MOCK TESTING COMPLETE")
        print("="*80)

async def main():
    """Main test execution"""
    tester = ToolTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
