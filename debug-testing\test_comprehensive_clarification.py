#!/usr/bin/env python3
"""
Test the comprehensive chunk clarification fix
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_comprehensive_clarification():
    """Test that the comprehensive fix catches ALL chunk mentions"""
    print("🧪 Testing Comprehensive Chunk Clarification Fix")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test comprehensive set of statistics queries
    test_queries = [
        "show details about the utils code base",
        "what are the metrics for utils?",
        "get statistics for utils codebase", 
        "show me utils codebase stats",
        "utils statistics",
        "stats for utils",
        "tell me about utils codebase",
        "utils codebase information",
    ]
    
    results = []
    
    for query in test_queries:
        print(f"\n🔍 Testing: '{query}'")
        print("-" * 40)
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": f"select codebase utils\n{query}"}],
                    "tool_ids": ["code_analyzer_tool"],
                    "stream": False,
                    "max_tokens": 1500
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                print(f"✅ Response received ({len(content)} chars)")
                
                # Check if response mentions chunks
                mentions_chunks = "chunks" in content.lower()
                
                if mentions_chunks:
                    # Check for comprehensive clarification
                    clarification_indicators = [
                        "text segments created by splitting source files",
                        "vector search and embedding",
                        "chunk count (479) represents text segments", 
                        "not the number of actual files",
                        "divided into multiple chunks",
                        "search granularity",
                        "text segments",
                        "splitting source files"
                    ]
                    
                    found_clarification = [ind for ind in clarification_indicators if ind.lower() in content.lower()]
                    
                    # Check for problematic interpretations
                    problematic = [
                        "chunks.*indicates.*files",
                        "chunks.*number of.*files",
                        "479.*files"
                    ]
                    
                    import re
                    issues = []
                    for phrase in problematic:
                        if re.search(phrase, content, re.IGNORECASE):
                            issues.append(phrase)
                    
                    if found_clarification:
                        if not issues:
                            print("🎉 PERFECT: Mentions chunks + has clarification + no issues")
                            status = "perfect"
                        else:
                            print("✅ GOOD: Mentions chunks + has clarification but some issues remain")
                            status = "good"
                        print(f"   Clarification: {found_clarification[:2]}")
                        if issues:
                            print(f"   Issues: {issues}")
                    else:
                        print("❌ MISSING: Mentions chunks but NO clarification")
                        status = "missing"
                        
                else:
                    print("ℹ️ No chunks mentioned in response")
                    status = "no_chunks"
                
                results.append({
                    "query": query,
                    "status": status,
                    "mentions_chunks": mentions_chunks,
                    "has_clarification": len(found_clarification) > 0 if mentions_chunks else None,
                    "content_length": len(content)
                })
                
            else:
                print(f"❌ HTTP {response.status_code}")
                results.append({"query": query, "status": "error"})
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append({"query": query, "status": "error"})
    
    return results

def analyze_comprehensive_results(results):
    """Analyze the comprehensive fix results"""
    print(f"\n📊 COMPREHENSIVE FIX ANALYSIS")
    print("=" * 50)
    
    valid_results = [r for r in results if r.get("status") != "error"]
    chunk_results = [r for r in valid_results if r.get("mentions_chunks")]
    
    total_responses = len(valid_results)
    chunk_responses = len(chunk_results)
    
    if chunk_responses == 0:
        print("⚠️ No responses mentioned chunks - cannot evaluate clarification")
        return
    
    perfect = len([r for r in chunk_results if r.get("status") == "perfect"])
    good = len([r for r in chunk_results if r.get("status") == "good"])
    missing = len([r for r in chunk_results if r.get("status") == "missing"])
    
    print(f"📈 Chunk Response Analysis:")
    print(f"  Total responses: {total_responses}")
    print(f"  Responses mentioning chunks: {chunk_responses}")
    print(f"  🎉 Perfect (chunks + clarification + no issues): {perfect}/{chunk_responses}")
    print(f"  ✅ Good (chunks + clarification + some issues): {good}/{chunk_responses}")
    print(f"  ❌ Missing (chunks but no clarification): {missing}/{chunk_responses}")
    
    # Calculate success metrics
    clarification_rate = (perfect + good) / chunk_responses * 100 if chunk_responses > 0 else 0
    perfect_rate = perfect / chunk_responses * 100 if chunk_responses > 0 else 0
    
    print(f"\n🎯 SUCCESS METRICS:")
    print(f"  Clarification coverage: {clarification_rate:.1f}%")
    print(f"  Perfect responses: {perfect_rate:.1f}%")
    
    # Overall assessment
    if clarification_rate >= 95:
        print("\n🎉 EXCELLENT: Comprehensive fix working perfectly!")
        print("✅ Nearly all chunk mentions have clarification")
        print("✅ Users will understand chunks ≠ files")
        print("✅ Problem solved!")
    elif clarification_rate >= 80:
        print("\n👍 VERY GOOD: Major improvement achieved")
        print("✅ Most chunk mentions have clarification")
        print("⚠️ Minor edge cases may remain")
    elif clarification_rate >= 60:
        print("\n⚠️ GOOD: Significant improvement but inconsistent")
        print("✅ Many responses improved")
        print("🔧 Some queries still need work")
    else:
        print("\n❌ ISSUE: Comprehensive fix not working as expected")
        print("🔧 Need to investigate tool update")
    
    # Show specific results
    print(f"\n📋 Query-by-Query Results:")
    for result in chunk_results:
        query = result.get("query", "")[:40] + "..." if len(result.get("query", "")) > 40 else result.get("query", "")
        status = result.get("status", "unknown")
        status_emoji = {"perfect": "🎉", "good": "✅", "missing": "❌"}.get(status, "❓")
        print(f"  {status_emoji} {query}")

def main():
    """Main test function"""
    print("🔧 Comprehensive Chunk Clarification Test")
    print("=" * 70)
    print("Testing that ALL responses mentioning chunks include clarification")
    
    # Test the comprehensive fix
    results = test_comprehensive_clarification()
    
    # Analyze results
    analyze_comprehensive_results(results)
    
    print(f"\n🎯 Goal:")
    print("100% of responses that mention 'chunks' should include clarification")
    print("This eliminates confusion about chunks vs files in RAG systems")

if __name__ == "__main__":
    main()
