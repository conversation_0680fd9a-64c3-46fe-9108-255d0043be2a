/** Declaration file generated by dts-gen */

export const version: string;

export function authorize(params: any, callback: any): any;

export function batch(reqs: any, additionalData: any, callback: any): any;

export function del(url: any, postData: any, callback: any): any;

export function extendAccessToken(params: any, callback: any): any;

export function fql(query: any, params: any, callback: any): any;

export function get(url: any, params?: any, callback?: any): any;

export function getAccessToken(): any;

export function getAppSecret(): any;

export function getGraphUrl(): any;

export function getOauthUrl(params: any, opts: any): any;

export function getOptions(): any;

export function post(url: any, postData: any, callback: any): any;

export function search(options: any, callback: any): any;

export function setAccessToken(token: any): any;

export function setAppSecret(token: any): any;

export function setGraphUrl(url: any): any;

export function setOptions(options: any): any;

export function setVersion(version: any): any;

/**
 * Fairly incomplete. I only added some commonly used fields.
 */
export type FacebookUser = {
    id: string,
    name: string,
    email: string,
    first_name: string,
    last_name: string,
    gender: string,
    link: string,
    locale: string,
    timezone: number
};

