#!/usr/bin/env python3
"""
Test the model selection functionality
"""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from openwebui_auto_tester import OpenWebUITester

def test_model_selection():
    """Test different model configurations"""
    print("🤖 Testing Model Selection")
    print("=" * 40)
    
    tester = OpenWebUITester("http://home-ai-server.local:8080")
    
    # Test different models
    models_to_test = [
        "llama3:latest",
        "llama3.1:latest", 
        "llama3:8b",
        "codellama:latest"
    ]
    
    test_message = "status"
    
    for model in models_to_test:
        print(f"\n🧪 Testing model: {model}")
        print("-" * 30)
        
        result = tester.send_chat_message(test_message, model)
        
        if result and result.get("success"):
            print(f"✅ {model}: Working")
            content = result.get("content", "")
            print(f"   Response length: {len(content)} chars")
            if content:
                preview = content[:100] + "..." if len(content) > 100 else content
                print(f"   Preview: {preview}")
        else:
            error = result.get("error", "Unknown error") if result else "No response"
            print(f"❌ {model}: Failed - {error}")

def show_model_recommendations():
    """Show model recommendations for tool testing"""
    print("\n🎯 Model Recommendations for Tool Testing")
    print("=" * 50)
    
    recommendations = [
        {
            "model": "llama3:latest",
            "status": "✅ RECOMMENDED",
            "reason": "Default model with tool enabled",
            "use_case": "Primary testing, tool functionality"
        },
        {
            "model": "llama3.1:latest", 
            "status": "⚠️ CONDITIONAL",
            "reason": "May not have tool enabled by default",
            "use_case": "Advanced testing if tool is manually enabled"
        },
        {
            "model": "codellama:latest",
            "status": "🔧 SPECIALIZED",
            "reason": "Code-focused model",
            "use_case": "Code analysis testing if available"
        },
        {
            "model": "llama3:8b",
            "status": "💡 ALTERNATIVE",
            "reason": "Smaller, faster variant",
            "use_case": "Quick testing, resource-constrained environments"
        }
    ]
    
    for rec in recommendations:
        print(f"\n{rec['status']} {rec['model']}")
        print(f"   Reason: {rec['reason']}")
        print(f"   Use case: {rec['use_case']}")
    
    print(f"\n💡 Pro Tips:")
    print("• Start with llama3:latest (has tool enabled by default)")
    print("• Check OpenWebUI settings to see which models have tools enabled")
    print("• Tool must be enabled per model in OpenWebUI settings")
    print("• Use 'preview' mode to test model compatibility before full test suite")

def main():
    """Main test function"""
    print("🧪 Model Selection Test Suite")
    print("=" * 40)
    
    print("Choose test mode:")
    print("1. Test model connectivity")
    print("2. Show model recommendations")
    print("3. Both")
    
    try:
        choice = input("\nEnter choice (1-3): ").strip()
        
        if choice == "1":
            test_model_selection()
        elif choice == "2":
            show_model_recommendations()
        elif choice == "3":
            test_model_selection()
            show_model_recommendations()
        else:
            print("Invalid choice. Showing recommendations...")
            show_model_recommendations()
            
    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")

if __name__ == "__main__":
    main()
