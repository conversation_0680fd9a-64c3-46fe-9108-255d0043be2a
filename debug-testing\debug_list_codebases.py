#!/usr/bin/env python3
"""
Debug the list_codebases response parsing
"""

import requests
import json

CODE_ANALYZER_SERVER_URL = "http://home-ai-server:5002"

def test_list_codebases():
    """Test the list_codebases endpoint and show the raw response"""
    print("🔍 Testing /tools/list_codebases endpoint...")
    
    try:
        response = requests.post(f"{CODE_ANALYZER_SERVER_URL}/tools/list_codebases", timeout=30)
        if response.status_code == 200:
            result = response.json()
            result_text = result.get('result', '')
            
            print("✅ Raw response:")
            print("-" * 60)
            print(result_text)
            print("-" * 60)
            
            # Test the parsing logic from test_collections.py
            print("\n🔍 Testing parsing logic...")
            lines = result_text.split('\n')
            codebases = []
            
            for i, line in enumerate(lines):
                print(f"Line {i:2d}: {repr(line)}")
                if line.strip().startswith('**') and ('✅' in line or '⚠️' in line or '📦' in line):
                    print(f"  → Matched pattern!")
                    # Extract name from markdown format
                    parts = line.split('**')
                    print(f"  → Parts: {parts}")
                    if len(parts) >= 2:
                        name_part = parts[1].strip()
                        print(f"  → Name part: {repr(name_part)}")
                        # Remove emoji
                        name = ''.join(c for c in name_part if c.isalnum() or c in ['_', '-']).strip()
                        print(f"  → Cleaned name: {repr(name)}")
                        if name:
                            codebases.append({'name': name, 'status': 'detected'})
                            print(f"  → Added: {name}")
            
            print(f"\n📊 Parsed codebases: {codebases}")
            return codebases
            
        else:
            print(f"❌ HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return []

def test_improved_parsing():
    """Test an improved parsing approach"""
    print("\n🔧 Testing improved parsing...")
    
    try:
        response = requests.post(f"{CODE_ANALYZER_SERVER_URL}/tools/list_codebases", timeout=30)
        if response.status_code == 200:
            result = response.json()
            result_text = result.get('result', '')
            
            # Improved parsing logic
            lines = result_text.split('\n')
            codebases = []
            
            for line in lines:
                # Look for lines that start with ** and contain status emojis
                if line.strip().startswith('**') and any(emoji in line for emoji in ['✅', '⚠️', '📦', '🚀']):
                    # Extract text between the first ** and second **
                    parts = line.split('**')
                    if len(parts) >= 3:  # Should have: '', 'emoji name', 'rest'
                        name_with_emoji = parts[1].strip()
                        # Remove common emojis and clean up
                        name = name_with_emoji
                        for emoji in ['✅', '⚠️', '📦', '🚀', '⚡']:
                            name = name.replace(emoji, '').strip()
                        
                        if name and len(name) > 0:
                            codebases.append({'name': name, 'status': 'detected'})
                            print(f"  Found: {name}")
            
            print(f"\n📊 Improved parsing result: {codebases}")
            return codebases
            
        else:
            print(f"❌ HTTP {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return []

if __name__ == "__main__":
    print("🚀 Debug List Codebases Parsing")
    print("=" * 50)
    
    # Test current parsing
    current_result = test_list_codebases()
    
    # Test improved parsing
    improved_result = test_improved_parsing()
    
    print("\n" + "=" * 50)
    print("📊 COMPARISON:")
    print(f"Current parsing found: {len(current_result)} codebases")
    print(f"Improved parsing found: {len(improved_result)} codebases")
    
    if len(improved_result) > len(current_result):
        print("💡 The improved parsing works better!")
    elif len(current_result) == 0:
        print("❌ Both parsing methods failed - check the response format")
    else:
        print("✅ Current parsing is working fine")
