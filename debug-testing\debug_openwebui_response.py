#!/usr/bin/env python3
"""
Debug OpenWebUI API responses to see what's actually being returned
"""

import requests
import json
import time

def test_single_query():
    """Test a single query and show the full response"""
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    model = "llama3:latest"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test query that should work
    query = "tmwmem_alloc"
    
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user", 
                "content": query
            }
        ],
        "stream": False
    }
    
    print(f"🔧 Testing query: '{query}'")
    print(f"🔧 URL: {base_url}/api/chat/completions")
    print(f"🔧 Model: {model}")
    print(f"🔧 Payload: {json.dumps(payload, indent=2)}")
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=60
        )
        end_time = time.time()
        
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📊 Response Time: {end_time - start_time:.2f}s")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"\n📋 Full Response Structure:")
            print(json.dumps(result, indent=2))
            
            # Extract the actual content
            choices = result.get("choices", [])
            if choices:
                message = choices[0].get("message", {})
                content = message.get("content", "")
                
                print(f"\n💬 Message Content:")
                print("="*80)
                print(content)
                print("="*80)
                
                # Look for tool usage indicators
                tool_indicators = [
                    "code context retrieved",
                    "chunks found", 
                    "codebase analyzer",
                    "=== code metadata ===",
                    "file:", "language:", "type:",
                    "tmwmem_alloc", "tmwdiag_error"
                ]
                
                found_indicators = [indicator for indicator in tool_indicators if indicator in content.lower()]
                
                print(f"\n🔍 Tool Usage Analysis:")
                print(f"   Content Length: {len(content)} characters")
                print(f"   Found Indicators: {found_indicators}")
                print(f"   Tool Likely Used: {len(found_indicators) > 0}")
                
                # Look for chunk count
                import re
                chunk_matches = re.findall(r'(\d+)\s+chunks?\s+found', content.lower())
                if chunk_matches:
                    print(f"   Chunks Found: {chunk_matches}")
                else:
                    print(f"   Chunks Found: None detected")
                
            else:
                print("❌ No choices in response")
        else:
            print(f"❌ Error Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

def test_natural_language_query():
    """Test a natural language query"""
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    model = "llama3:latest"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test natural language query
    query = "How does memory allocation work in this codebase?"
    
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user", 
                "content": query
            }
        ],
        "stream": False
    }
    
    print(f"\n\n🔧 Testing natural language query: '{query}'")
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=60
        )
        end_time = time.time()
        
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📊 Response Time: {end_time - start_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            
            # Extract the actual content
            choices = result.get("choices", [])
            if choices:
                message = choices[0].get("message", {})
                content = message.get("content", "")
                
                print(f"\n💬 Message Content:")
                print("="*80)
                print(content)
                print("="*80)
                
                # Check if tool was used
                tool_used = any(indicator in content.lower() for indicator in [
                    "code context retrieved", "chunks found", "codebase analyzer"
                ])
                
                print(f"\n🔍 Analysis:")
                print(f"   Content Length: {len(content)} characters")
                print(f"   Tool Used: {tool_used}")
                print(f"   Contains 'utils': {'utils' in content.lower()}")
                print(f"   Contains 'memory': {'memory' in content.lower()}")
                print(f"   Contains 'allocation': {'allocation' in content.lower()}")
                
            else:
                print("❌ No choices in response")
        else:
            print(f"❌ Error Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    print("🚀 DEBUGGING OPENWEBUI API RESPONSES")
    print("="*80)
    
    # Test exact function name first
    test_single_query()
    
    # Test natural language query
    test_natural_language_query()
    
    print(f"\n{'='*80}")
    print("🎯 DEBUG COMPLETE")
    print("="*80)
