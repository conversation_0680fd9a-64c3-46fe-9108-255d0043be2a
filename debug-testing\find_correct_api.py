#!/usr/bin/env python3
"""
Find the correct way to use code_analyzer_tools in OpenWebUI
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_different_tool_formats():
    """Test different ways to specify tools in the API"""
    print("🔍 Testing Different Tool Specification Formats")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Different ways to specify tools
    tool_formats = [
        {"tools": ["codebase_analyzer"]},
        {"tool_ids": ["codebase_analyzer"]},
        {"functions": ["codebase_analyzer"]},
        {"tools": ["codebase_analyzer"]},
        {"tools": [{"type": "function", "function": {"name": "codebase_analyzer"}}]},
        {"tool_choice": "code_analyzer_tools"},
        {"use_tools": True, "tools": ["code_analyzer_tool"]},
    ]
    
    base_payload = {
        "model": "llama3:latest",
        "messages": [{"role": "user", "content": "status"}],
        "stream": False,
        "max_tokens": 500
    }
    
    for i, tool_format in enumerate(tool_formats, 1):
        print(f"\n🧪 Test {i}: {tool_format}")
        print("-" * 40)
        
        payload = {**base_payload, **tool_format}
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                    
                    # Check for tool indicators
                    tool_indicators = ["🔧", "📚", "✅", "Code Analysis System", "Available Codebases", "Tool Server"]
                    found = [ind for ind in tool_indicators if ind in content]
                    
                    if found:
                        print(f"🎉 PLUGIN WORKING! Found: {found}")
                        print(f"✅ This format works: {tool_format}")
                        return tool_format
                    else:
                        print("⚠️ No tool indicators (likely just LLM response)")
                        
                except json.JSONDecodeError:
                    print(f"Non-JSON response: {response.text[:100]}...")
                    
            else:
                error_text = response.text[:200] if response.text else "No error text"
                print(f"❌ Error: {error_text}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    print(f"\n❌ None of the tool formats worked")
    return None

def test_manual_function_calling():
    """Test if we can manually trigger the tool functions"""
    print(f"\n🔧 Testing Manual Function Calling")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Try calling tool functions directly in the message
    manual_calls = [
        "call function list_codebases",
        "use tool code_analyzer_tools to list codebases",
        "execute code_analyzer_tools.list_codebases()",
        "@code_analyzer_tools list codebases",
        "!list_codebases",
        "/list_codebases",
    ]
    
    for call in manual_calls:
        print(f"\n🧪 Testing: '{call}'")
        
        try:
            payload = {
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": call}],
                "stream": False,
                "max_tokens": 500
            }
            
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                # Check for tool indicators
                tool_indicators = ["🔧", "📚", "✅", "utils", "z80emu", "modbus"]
                found = [ind for ind in tool_indicators if ind in content]
                
                if found:
                    print(f"🎉 PLUGIN WORKING! Found: {found}")
                    print(f"✅ This call works: '{call}'")
                    return call
                else:
                    print("⚠️ No tool indicators")
                    
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return None

def check_openwebui_version():
    """Check OpenWebUI version and configuration"""
    print(f"\n📋 Checking OpenWebUI Configuration")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Try to get version/config info
    config_endpoints = [
        "/api/config",
        "/api/version", 
        "/api/info",
        "/api/status",
        "/version",
        "/config"
    ]
    
    for endpoint in config_endpoints:
        try:
            response = session.get(f"{OPENWEBUI_URL}{endpoint}", timeout=10)
            print(f"📡 {endpoint}: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   Data: {str(data)[:200]}...")
                except:
                    print(f"   Text: {response.text[:200]}...")
                    
        except Exception as e:
            print(f"❌ {endpoint}: {e}")

def main():
    """Main test function"""
    print("🔍 Finding Correct API Usage for code_analyzer_tools")
    print("=" * 70)
    
    # Test different approaches
    working_format = test_different_tool_formats()
    
    if not working_format:
        working_call = test_manual_function_calling()
        
        if not working_call:
            print(f"\n❌ Could not find working method to enable code_analyzer_tools")
            print(f"\n💡 Possible issues:")
            print("1. Tool not installed in OpenWebUI")
            print("2. Tool not enabled for llama3:latest model")
            print("3. Different API format required")
            print("4. Tool has errors")
            
            check_openwebui_version()
            
            print(f"\n🔧 Next steps:")
            print("1. Check OpenWebUI Functions/Tools settings in web interface")
            print("2. Verify code_analyzer_tools is installed and enabled")
            print("3. Check OpenWebUI logs for errors")
            print("4. Try testing in the web interface first")
        else:
            print(f"\n✅ Found working manual call: '{working_call}'")
    else:
        print(f"\n✅ Found working tool format: {working_format}")

if __name__ == "__main__":
    main()
