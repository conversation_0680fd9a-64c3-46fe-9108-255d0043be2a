#!/usr/bin/env python3
"""
Diagnostic script to investigate the collection ID mismatch issue
"""

import requests
import json
import chromadb
from pathlib import Path

CODE_ANALYZER_SERVER_URL = "http://home-ai-server:5002"
CHROMA_DB_PATH = "./chroma_db"

def check_server_collections():
    """Check what collections the server reports"""
    print("🔍 Checking server-reported collections...")
    
    try:
        response = requests.post(f"{CODE_ANALYZER_SERVER_URL}/tools/list_codebases", timeout=30)
        if response.status_code == 200:
            result = response.json()
            print("✅ Server collections response:")
            print(result.get('result', 'No result'))
            return True
        else:
            print(f"❌ Server returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Failed to check server collections: {e}")
        return False

def check_direct_chromadb():
    """Check ChromaDB directly"""
    print("\n🔍 Checking ChromaDB directly...")
    
    try:
        # Connect to ChromaDB directly
        client = chromadb.PersistentClient(path=CHROMA_DB_PATH)
        collections = client.list_collections()
        
        print(f"✅ Found {len(collections)} collections in ChromaDB:")
        
        for collection in collections:
            print(f"\n📁 Collection: {collection.name}")
            print(f"   ID: {collection.id}")
            print(f"   Metadata: {collection.metadata}")
            
            try:
                count = collection.count()
                print(f"   Document count: {count}")
                
                if count > 0:
                    # Try to get a sample document
                    sample = collection.get(limit=1, include=['metadatas'])
                    if sample['metadatas']:
                        print(f"   Sample metadata keys: {list(sample['metadatas'][0].keys())}")
                else:
                    print("   ⚠️ Collection is empty!")
                    
            except Exception as e:
                print(f"   ❌ Error accessing collection: {e}")
        
        return collections
        
    except Exception as e:
        print(f"❌ Failed to access ChromaDB directly: {e}")
        return None

def test_collection_access():
    """Test accessing collections with different embedding functions"""
    print("\n🧪 Testing collection access methods...")
    
    try:
        client = chromadb.PersistentClient(path=CHROMA_DB_PATH)
        collections = client.list_collections()
        
        if not collections:
            print("❌ No collections found to test")
            return
        
        test_collection = collections[0]
        print(f"🎯 Testing collection: {test_collection.name}")
        
        # Method 1: Access without embedding function
        try:
            coll1 = client.get_collection(test_collection.name)
            print(f"✅ Method 1 (no embedding): {coll1.count()} documents")
        except Exception as e:
            print(f"❌ Method 1 failed: {e}")
        
        # Method 2: Access with Ollama embedding function
        try:
            from vector_db_creator import OllamaEmbeddingFunction
            embedding_function = OllamaEmbeddingFunction(
                ollama_host="http://ollama:11434",
                model_name="nomic-embed-text"
            )
            coll2 = client.get_collection(
                name=test_collection.name,
                embedding_function=embedding_function
            )
            print(f"✅ Method 2 (Ollama embedding): {coll2.count()} documents")
        except Exception as e:
            print(f"❌ Method 2 failed: {e}")
            
        # Method 3: Try to query the collection
        try:
            results = test_collection.query(
                query_texts=["test query"],
                n_results=1
            )
            print(f"✅ Method 3 (direct query): {len(results.get('documents', [[]])[0])} results")
        except Exception as e:
            print(f"❌ Method 3 failed: {e}")
            
    except Exception as e:
        print(f"❌ Collection access test failed: {e}")

def test_ai_endpoint():
    """Test the AI endpoint that's failing"""
    print("\n🤖 Testing AI endpoint...")
    
    try:
        payload = {
            "question": "What functions are available?",
            "codebase_name": "utils",
            "n_results": 3
        }
        
        response = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/ask_about_code",
            json=payload,
            timeout=120
        )
        
        if response.status_code == 200:
            result = response.json()
            result_text = result.get('result', '')
            print("✅ AI endpoint responded:")
            print(f"   Response length: {len(result_text)} characters")
            if "Collection" in result_text and "does not exist" in result_text:
                print("❌ Found collection existence error in response")
                # Extract the collection ID from the error
                import re
                match = re.search(r'Collection ([a-f0-9-]+) does not exist', result_text)
                if match:
                    collection_id = match.group(1)
                    print(f"   Problematic collection ID: {collection_id}")
            else:
                print("✅ No collection errors found")
        else:
            print(f"❌ AI endpoint returned status {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ AI endpoint test failed: {e}")

def main():
    print("🚀 Collection Issue Diagnostic Tool")
    print("=" * 50)
    
    # Check server collections
    server_ok = check_server_collections()
    
    # Check ChromaDB directly
    collections = check_direct_chromadb()
    
    # Test collection access methods
    if collections:
        test_collection_access()
    
    # Test the failing AI endpoint
    test_ai_endpoint()
    
    print("\n" + "=" * 50)
    print("📊 DIAGNOSTIC SUMMARY:")
    print("=" * 50)
    
    if server_ok and collections:
        print("✅ Server and ChromaDB are accessible")
        print("🔍 The issue is likely in collection access or embedding function mismatch")
        print("\n💡 RECOMMENDED FIXES:")
        print("1. Restart the Code Analysis server to clear active_collections cache")
        print("2. Check embedding function consistency between creation and access")
        print("3. Consider rebuilding collections if embedding mismatch persists")
    else:
        print("❌ Basic connectivity issues found")
        print("🔧 Fix server/database connectivity first")

if __name__ == "__main__":
    main()
