import { PrismaClient } from '@prisma/client';
import { UserRole } from '../types/enums';

const prisma = new PrismaClient();

export class UserModel {
  async add(user: { name: string; email: string; role?: UserRole }) {
    return prisma.user.create({
      data: {
        name: user.name,
        email: user.email,
        role: user.role || UserRole.CUSTOMER,
      },
    });
  }

  async list() {
    return prisma.user.findMany();
  }

  async get(id: string) {
    return prisma.user.findUnique({ where: { id } });
  }
}
