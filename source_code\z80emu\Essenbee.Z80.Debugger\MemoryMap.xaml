﻿<UserControl x:Class="Essenbee.Z80.Debugger.MemoryMap"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:controls="clr-namespace:Essenbee.Z80.Debugger"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <DataGrid x:Name="MemoryMapControl"
                  VerticalScrollBarVisibility="Visible"
                  HorizontalScrollBarVisibility="Hidden"
                  AutoGenerateColumns="False"
                  ItemsSource="{Binding Memory}" 
                  IsReadOnly="True"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  RowBackground="LightGray"
                  SelectionMode="Single"
                  SelectedIndex="{Binding MemoryMapRow}">
            <DataGrid.Resources>
                <Style TargetType="{x:Type DataGridCell}">
                    <Style.Triggers>
                        <Trigger Property="DataGridCell.IsSelected" Value="True">
                            <Setter Property="Background" Value="#CCDAFF" />
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </DataGrid.Resources>
            <DataGrid.Columns>
                <DataGridTextColumn 
                        Header="Address" 
                        IsReadOnly="True" 
                        Width="100" 
                        Binding="{Binding Path=Key}" />
                <DataGridTextColumn 
                        Header="Contents" 
                        IsReadOnly="True" 
                        Width="500"
                        Binding="{Binding Path=Value}"/>
            </DataGrid.Columns>
        </DataGrid>
    </Grid>
</UserControl>
