/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2023 */
/*****************************************************************************/
/*                                                                           */
/* This file is the property of:                                             */
/*                                                                           */
/*                       Triangle MicroWorks, Inc.                           */
/*                      Raleigh, North Carolina USA                          */
/*                       www.TriangleMicroWorks.com                          */
/*                          (919) 870-6615                                   */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*                                                                           */
/*****************************************************************************/

/* file: mmbsesp.h
 * description: This file is intended for internal SCL use only.
 *   Private master Modbus session support
 */
#ifndef MMBSESP_DEFINED
#define MMBSESP_DEFINED
#include "tmwscl/utils/tmwsesn.h"
#include "tmwscl/modbus/mbsesn.h"

/* Define MMB Session Context */
typedef struct MMBSessionStruct {

  /* Generic modbus Session info, must be first entry */
  MBSESN mb;

  /* default response timeout value to use for all request on this session */
  TMWTYPES_MILLISECONDS defaultResponseTimeout;

} MMBSESN;

#endif /* MMBSESP_DEFINED */
