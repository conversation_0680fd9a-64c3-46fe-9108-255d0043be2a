#!/usr/bin/env python3
"""
Test script to verify the enhanced search fix works after server restart
"""

import requests
import json
import time
import subprocess

CODE_ANALYZER_SERVER_URL = "http://home-ai-server.local:5002"

def restart_code_analyzer_server():
    """Restart the Code Analyzer server container to pick up code changes"""
    print("🔄 Restarting Code Analyzer server to apply code changes...")
    
    try:
        # Try to restart via SSH
        result = subprocess.run([
            'ssh', 'home-ai-server', 
            'docker', 'restart', 'code-analyzer-server'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Code Analyzer server restarted successfully via SSH")
            return True
        else:
            print(f"❌ SSH restart failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ SSH restart timed out")
        return False
    except FileNotFoundError:
        print("❌ SSH not available")
        return False
    except Exception as e:
        print(f"❌ Restart failed: {e}")
        return False

def wait_for_server():
    """Wait for server to come back online"""
    print("⏳ Waiting for server to come back online...")
    
    for i in range(30):  # Wait up to 30 seconds
        try:
            response = requests.get(f"{CODE_ANALYZER_SERVER_URL}/health", timeout=5)
            if response.status_code == 200:
                print(f"✅ Server is back online after {i+1} seconds")
                return True
        except:
            pass
        
        time.sleep(1)
        if i % 5 == 4:  # Print progress every 5 seconds
            print(f"   Still waiting... ({i+1}s)")
    
    print("❌ Server did not come back online within 30 seconds")
    return False

def test_enhanced_search():
    """Test the enhanced search with multiple filters"""
    print("\n🔍 Testing enhanced search with multiple filters...")
    
    try:
        payload = {
            "query": "memory allocation",
            "codebase_name": "utils",
            "n_results": 3,
            "filter_language": "c",
            "filter_type": "function"
        }
        
        response = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/enhanced_search",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            result_text = result.get('result', '')
            
            # Check for the old error
            if "Expected where to have exactly one operator" in result_text:
                print("❌ STILL GETTING THE OLD ERROR")
                print("   The ChromaDB query syntax issue is not fixed")
                return False
            elif "Error in enhanced search" in result_text:
                print("❌ DIFFERENT ERROR OCCURRED")
                print(f"   New error: {result_text[:200]}...")
                return False
            else:
                print("✅ ENHANCED SEARCH IS WORKING!")
                print(f"   Response preview: {result_text[:200]}...")
                
                # Check if we got actual search results
                if "memory" in result_text.lower() or "alloc" in result_text.lower():
                    print("✅ Got relevant search results with filtering")
                    return True
                else:
                    print("⚠️ Search worked but results may not be filtered correctly")
                    return True
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    print("🧪 Enhanced Search Fix Tester")
    print("=" * 50)
    
    # Step 1: Restart server
    if not restart_code_analyzer_server():
        print("❌ Cannot restart server - testing with current version")
    
    # Step 2: Wait for server
    if not wait_for_server():
        print("❌ Server not responding - cannot test")
        return
    
    # Step 3: Test enhanced search
    success = test_enhanced_search()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 ENHANCED SEARCH FIX IS WORKING!")
        print("💡 Multiple filters can now be used together")
    else:
        print("❌ Enhanced search still has issues")
        print("💡 Check server logs for more details")

if __name__ == "__main__":
    main()
