#!/usr/bin/env python3
"""
Check the actual server state to debug codebase selection
"""

import requests
import json

def check_server_health():
    """Check what the server thinks is selected"""
    
    try:
        response = requests.get("http://home-ai-server.local:5002/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("🔧 Server Health Response:")
            print(json.dumps(data, indent=2))
            
            current_codebase = data.get("current_codebase")
            print(f"\n🎯 Server Current Codebase: '{current_codebase}'")
            return current_codebase
        else:
            print(f"❌ Server health check failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error checking server health: {e}")
        return None

def call_unselect_endpoint():
    """Call the server unselect endpoint directly"""
    
    try:
        response = requests.post("http://home-ai-server.local:5002/tools/unselect_codebase", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("🔧 Unselect Response:")
            print(json.dumps(data, indent=2))
            return True
        else:
            print(f"❌ Unselect failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error calling unselect: {e}")
        return False

def call_select_endpoint():
    """Call the server select endpoint directly"""
    
    try:
        payload = {"codebase_name": "utils"}
        response = requests.post(
            "http://home-ai-server.local:5002/tools/select_codebase",
            json=payload,
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            print("🔧 Select Response:")
            print(json.dumps(data, indent=2))
            return True
        else:
            print(f"❌ Select failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error calling select: {e}")
        return False

def main():
    """Test server state directly"""
    
    print("🚀 CHECKING SERVER STATE DIRECTLY")
    print("="*60)
    
    # Check initial state
    print("\n📋 STEP 1: Check initial server state")
    initial_state = check_server_health()
    
    # Unselect directly
    print("\n📋 STEP 2: Call unselect endpoint directly")
    unselect_success = call_unselect_endpoint()
    
    # Check state after unselect
    print("\n📋 STEP 3: Check server state after unselect")
    after_unselect = check_server_health()
    
    # Select directly
    print("\n📋 STEP 4: Call select endpoint directly")
    select_success = call_select_endpoint()
    
    # Check final state
    print("\n📋 STEP 5: Check server state after select")
    final_state = check_server_health()
    
    # Analysis
    print(f"\n{'='*60}")
    print("📊 ANALYSIS")
    print(f"{'='*60}")
    
    print(f"Initial state: '{initial_state}'")
    print(f"After unselect: '{after_unselect}'")
    print(f"After select: '{final_state}'")
    
    if initial_state != after_unselect:
        print("✅ Unselect endpoint works - server state changed")
    else:
        print("❌ Unselect endpoint failed - server state unchanged")
    
    if final_state == "utils":
        print("✅ Select endpoint works - utils selected")
    else:
        print("❌ Select endpoint failed - utils not selected")

if __name__ == "__main__":
    main()
