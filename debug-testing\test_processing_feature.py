#!/usr/bin/env python3
"""
Quick test to verify the new processing functionality in test_collections.py
"""

import sys
import os
import requests

# Add the debug-testing directory to the path
sys.path.insert(0, os.path.dirname(__file__))

from test_collections import CodeAnalysisServerTester

def test_processing_feature():
    """Test the new processing feature"""
    print("🧪 Testing new processing feature...")
    
    # Initialize tester
    tester = CodeAnalysisServerTester()
    
    # Test listing collections
    print("\n📚 Testing collection listing...")
    collections = tester.list_collections()
    print(f"Found {len(collections)} collections:")
    for collection in collections:
        print(f"  • {collection['name']} ({collection['status']})")
    
    # Test getting collection info
    if collections:
        test_collection = collections[0]['name']
        print(f"\n📊 Testing collection info for: {test_collection}")
        info = tester.get_collection_info(test_collection)
        if 'error' not in info:
            print(f"  Documents: {info.get('total_documents', 'unknown')}")
        else:
            print(f"  Error: {info['error']}")
    
    # Test processing (dry run - just check the method exists)
    print(f"\n🔄 Testing processing method (dry run)...")
    if collections:
        test_collection = collections[0]['name']
        print(f"  Would process: {test_collection}")
        print("  ✅ Processing method is available")
    
    print("\n✅ All tests completed!")

if __name__ == "__main__":
    test_processing_feature()
