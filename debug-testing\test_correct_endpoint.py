#!/usr/bin/env python3
"""
Test the correct RAG server endpoint for statistics
"""

import requests
import json

CODE_ANALYZER_SERVER_URL = "http://home-ai-server.local:5002"

def test_correct_endpoint():
    """Test the correct endpoint that the tool actually uses"""
    print("🔍 Testing Correct RAG Server Endpoint")
    print("=" * 50)
    
    # Test the endpoint the tool actually calls
    endpoint = "/tools/get_code_stats"
    
    try:
        response = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}{endpoint}",
            json={"codebase_name": "utils"},
            timeout=30
        )
        
        print(f"📡 Endpoint: {endpoint}")
        print(f"📡 Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            result = data.get('result', '')
            
            print(f"📊 Response ({len(result)} chars):")
            print("-" * 40)
            print(result)
            print("-" * 40)
            
            # Check if it contains real codebase data
            real_indicators = ["chunks", "files", "languages", "C:", "utils", "479", "43"]
            found_real = [ind for ind in real_indicators if ind in result]
            
            if found_real:
                print(f"✅ Contains real codebase data: {found_real}")
                return True, result
            else:
                print("❌ No real codebase data found")
                return False, result
        else:
            print(f"❌ Error: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False, None

def test_alternative_endpoints():
    """Test other possible endpoints"""
    print(f"\n🔍 Testing Alternative Endpoints")
    print("=" * 50)
    
    endpoints = [
        "/tools/get_codebase_stats",
        "/tools/codebase_stats", 
        "/tools/stats",
        "/status",
        "/tools/list_codebases"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.post(
                f"{CODE_ANALYZER_SERVER_URL}{endpoint}",
                json={"codebase_name": "utils"},
                timeout=10
            )
            
            print(f"📡 {endpoint}: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                result = data.get('result', str(data))
                preview = result[:100] + "..." if len(str(result)) > 100 else str(result)
                print(f"   Preview: {preview}")
                
        except Exception as e:
            print(f"📡 {endpoint}: Error - {e}")

def main():
    """Main test function"""
    print("🔧 Correct Endpoint Test")
    print("=" * 60)
    print("Testing the actual endpoint the tool uses")
    
    # Test correct endpoint
    success, result = test_correct_endpoint()
    
    # Test alternatives if main fails
    if not success:
        test_alternative_endpoints()
    
    # Analysis
    print(f"\n📊 ANALYSIS")
    print("=" * 50)
    
    if success:
        print("✅ RAG server endpoint is working")
        print("✅ Real codebase data is available")
        print("❌ Issue: Tool not processing the data correctly")
    else:
        print("❌ RAG server endpoint issue")
        print("🔧 Need to find the correct endpoint or fix server")

if __name__ == "__main__":
    main()
