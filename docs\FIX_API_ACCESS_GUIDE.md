# Fix API Access - Step by Step Guide

## 🎯 **Problem Confirmed:**
- ✅ Tool works perfectly in OpenWebUI web interface
- ❌ Tool does NOT work via API calls (all tool IDs return generic responses)
- 🔧 Tool is not properly registered for API access

## 📋 **Solution Steps:**

### **Step 1: Check Current Tool Registration**
1. **Open OpenWebUI**: http://home-ai-server.local:8080
2. **Go to**: Workspace → Tools
3. **Find the Code Analysis Tool** and note:
   - Exact tool name/title
   - Tool ID (if visible)
   - Status (Active/Inactive)
   - Any error messages

### **Step 2: Check Model Assignment**
1. **Go to**: Workspace → Models
2. **Find**: llama3:latest
3. **Click**: Edit (✏️) button
4. **Check**: Tools section
5. **Verify**: Code Analysis Tool is checked/enabled
6. **Note**: Exact tool name shown in the list

### **Step 3: Get Tool Information**
Look for these details in OpenWebUI:
- **Tool ID**: The internal identifier used by OpenWebUI
- **Tool Name**: The display name
- **Status**: Whether it's active and working
- **API Access**: Whether it's enabled for API calls

### **Step 4: Fix Tool Registration**

**Option A: Re-enable for API Access**
1. In Workspace → Tools, check if there's an "API Access" setting
2. Ensure the tool is enabled for API calls
3. Save changes

**Option B: Re-install Tool**
1. **Delete** existing tool in Workspace → Tools
2. **Re-install** using the current `open_webui_code_analyzer_tool.py`
3. **Enable** for llama3:latest model
4. **Test** both web interface and API

**Option C: Check Tool Configuration**
1. Edit the tool in OpenWebUI
2. Verify the server URL is correct: `http://code-analyzer-server:5002`
3. Check all valve settings
4. Save and test

### **Step 5: Test API Access**
After making changes, test with:
```bash
python debug-testing\find_correct_tool_id.py
```

Expected result: One of the tool IDs should return actual tool responses instead of generic ones.

### **Step 6: Update Auto-Tester**
Once you find the working tool ID, update the auto-tester:
```python
# In debug-testing/openwebui_auto_tester.py, line 96:
"tool_ids": ["CORRECT_TOOL_ID_HERE"],
```

## 🔍 **What to Look For:**

### **In OpenWebUI Interface:**
- Tool should show as "Active" or "Enabled"
- No error messages in tool configuration
- Tool appears in model's tool list
- Server URL is correct for Docker environment

### **In API Testing:**
- Tool ID returns actual codebase data instead of generic responses
- Responses include: "Selected Enhanced Codebase", "Documents: 479", etc.
- No more generic responses about "utility functions"

## 🎯 **Expected Results After Fix:**

### **API Calls Should Return:**
```
✅ "select utils codebase" → "Selected Enhanced Codebase: utils..."
✅ "list codebases" → Shows actual indexed codebases
✅ "status" → Shows actual system status
```

### **Auto-Tester Should Show:**
```
✅ Tool-specific responses instead of generic ones
✅ Success indicators in test results
✅ Actual codebase data and statistics
```

## 🚨 **Common Issues:**

1. **Tool ID Changed**: During renaming, the tool ID might have changed
2. **API Access Disabled**: Tool might be enabled for web but not API
3. **Multiple Tools**: Conflicting tools with similar names
4. **Configuration Mismatch**: Wrong server URL or settings

## 📊 **Success Criteria:**

The fix is complete when:
- ✅ API calls return tool-specific responses
- ✅ Auto-tester shows actual codebase data
- ✅ Both web interface AND API work correctly
- ✅ Tool ID is identified and documented

**Remember: The auto-tester USED TO WORK, so this is definitely fixable!**
