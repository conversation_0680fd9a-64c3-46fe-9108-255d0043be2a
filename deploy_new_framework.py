#!/usr/bin/env python3
"""
Deployment script for the new language framework
Handles Docker rebuild and service restart
"""

import subprocess
import sys
import time
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_command(command, description, check=True):
    """Run a shell command with logging"""
    logger.info(f"🔄 {description}")
    logger.info(f"   Command: {command}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True,
            check=check
        )
        
        if result.stdout:
            logger.info(f"   Output: {result.stdout.strip()}")
        
        if result.stderr and result.returncode != 0:
            logger.error(f"   Error: {result.stderr.strip()}")
            
        return result.returncode == 0
        
    except subprocess.CalledProcessError as e:
        logger.error(f"   Command failed: {e}")
        if e.stdout:
            logger.error(f"   Stdout: {e.stdout}")
        if e.stderr:
            logger.error(f"   Stderr: {e.stderr}")
        return False

def check_docker_compose():
    """Check if docker-compose is available"""
    logger.info("🔍 Checking Docker Compose availability...")
    
    # Try docker compose (newer syntax)
    if run_command("docker compose version", "Checking docker compose", check=False):
        return "docker compose"
    
    # Try docker-compose (older syntax)
    if run_command("docker-compose version", "Checking docker-compose", check=False):
        return "docker-compose"
    
    logger.error("❌ Neither 'docker compose' nor 'docker-compose' is available")
    return None

def stop_services(compose_cmd):
    """Stop the code analyzer services"""
    logger.info("🛑 Stopping code analyzer services...")
    
    # Stop just the code analyzer server
    success = run_command(
        f"{compose_cmd} stop code-analyzer-server",
        "Stopping code-analyzer-server",
        check=False
    )
    
    if success:
        logger.info("✅ Services stopped successfully")
    else:
        logger.warning("⚠️ Some services may not have stopped cleanly")
    
    return success

def build_services(compose_cmd):
    """Build the code analyzer services"""
    logger.info("🔨 Building code analyzer services...")
    
    success = run_command(
        f"{compose_cmd} build code-analyzer-server",
        "Building code-analyzer-server"
    )
    
    if success:
        logger.info("✅ Services built successfully")
    else:
        logger.error("❌ Build failed")
    
    return success

def start_services(compose_cmd):
    """Start the code analyzer services"""
    logger.info("🚀 Starting code analyzer services...")
    
    success = run_command(
        f"{compose_cmd} up -d code-analyzer-server",
        "Starting code-analyzer-server"
    )
    
    if success:
        logger.info("✅ Services started successfully")
    else:
        logger.error("❌ Start failed")
    
    return success

def check_service_health():
    """Check if the service is healthy"""
    logger.info("🏥 Checking service health...")
    
    # Wait a bit for the service to start
    time.sleep(10)
    
    # Check if the container is running
    success = run_command(
        "docker ps | grep code-analyzer-server",
        "Checking if container is running",
        check=False
    )
    
    if success:
        logger.info("✅ Container is running")
        
        # Try to hit the health endpoint
        time.sleep(5)
        health_check = run_command(
            "curl -f http://localhost:5002/health",
            "Checking health endpoint",
            check=False
        )
        
        if health_check:
            logger.info("✅ Health endpoint responding")
            return True
        else:
            logger.warning("⚠️ Health endpoint not responding yet")
            return False
    else:
        logger.error("❌ Container is not running")
        return False

def show_logs():
    """Show recent logs from the service"""
    logger.info("📋 Showing recent logs...")
    
    run_command(
        "docker logs --tail=20 code-analyzer-server",
        "Fetching recent logs",
        check=False
    )

def main():
    """Main deployment function"""
    logger.info("🚀 Starting deployment of new language framework...")
    
    # Check if we're in the right directory
    if not Path("docker-compose.yml").exists():
        logger.error("❌ docker-compose.yml not found. Please run from the project root.")
        return 1
    
    # Check Docker Compose availability
    compose_cmd = check_docker_compose()
    if not compose_cmd:
        return 1
    
    logger.info(f"✅ Using: {compose_cmd}")
    
    # Deployment steps
    steps = [
        ("Stop Services", lambda: stop_services(compose_cmd)),
        ("Build Services", lambda: build_services(compose_cmd)),
        ("Start Services", lambda: start_services(compose_cmd)),
        ("Check Health", check_service_health),
    ]
    
    for step_name, step_func in steps:
        logger.info(f"\n📋 Step: {step_name}")
        success = step_func()
        
        if not success:
            logger.error(f"❌ Step '{step_name}' failed")
            logger.info("📋 Showing logs for debugging...")
            show_logs()
            return 1
        
        logger.info(f"✅ Step '{step_name}' completed")
    
    logger.info("\n🎉 Deployment completed successfully!")
    logger.info("📋 Final status check...")
    show_logs()
    
    logger.info("\n🔗 Service should be available at:")
    logger.info("   - Health: http://localhost:5002/health")
    logger.info("   - API: http://localhost:5002/docs")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
