#!/usr/bin/env python3
"""
Comprehensive Test Runner for OpenWebUI Code Analyzer System

This script orchestrates all testing activities including:
- Unit tests for individual components
- Integration tests between components  
- Performance benchmarks and load testing
- Regression testing
- Test report generation

Usage:
    python run_all_tests.py                     # Run all tests
    python run_all_tests.py --quick             # Run quick smoke tests only
    python run_all_tests.py --full              # Run comprehensive test suite
    python run_all_tests.py --performance       # Run performance tests only
    python run_all_tests.py --regression        # Run regression tests
    python run_all_tests.py --report            # Generate detailed report
"""

import subprocess
import sys
import os
import json
import time
import argparse
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

# Test Configuration
TEST_CONFIG = {
    'server_url': 'http://home-ai-server.local:5002',
    'openwebui_url': 'http://home-ai-server.local:8080',
    'test_timeout': 300,  # 5 minutes per test suite
    'report_dir': 'test_reports',
    'baseline_file': 'performance_baseline.json'
}


class TestResult:
    """Container for test results"""
    
    def __init__(self, name: str, success: bool, duration: float, 
                 details: Optional[Dict] = None, output: str = ""):
        self.name = name
        self.success = success
        self.duration = duration
        self.details = details or {}
        self.output = output
        self.timestamp = datetime.now()


class TestRunner:
    """Orchestrates all testing activities"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.results: List[TestResult] = []
        self.start_time = datetime.now()
        
        # Ensure report directory exists
        os.makedirs(config['report_dir'], exist_ok=True)
    
    def run_command(self, command: List[str], name: str, timeout: Optional[int] = None) -> TestResult:
        """Run a command and capture results"""
        print(f"\n{'='*60}")
        print(f"[TEST] RUNNING: {name}")
        print(f"Command: {' '.join(command)}")
        print(f"{'='*60}")
        
        start_time = time.time()
        timeout = timeout or self.config['test_timeout']
        
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=os.path.dirname(os.path.abspath(__file__))
            )
            
            duration = time.time() - start_time
            success = result.returncode == 0
            output = result.stdout + result.stderr
            
            status = "[PASS]" if success else "[FAIL]"
            print(f"{status} {name} ({duration:.1f}s)")

            if not success:
                print(f"Error output:\n{result.stderr}")

            return TestResult(name, success, duration, output=output)

        except subprocess.TimeoutExpired:
            duration = time.time() - start_time
            print(f"[TIMEOUT] {name} ({duration:.1f}s)")
            return TestResult(name, False, duration,
                            details={'error': 'timeout'},
                            output="Test timed out")

        except Exception as e:
            duration = time.time() - start_time
            print(f"[ERROR] {name} ({duration:.1f}s): {e}")
            return TestResult(name, False, duration, 
                            details={'error': str(e)}, 
                            output=f"Exception: {e}")
    
    def run_smoke_tests(self) -> List[TestResult]:
        """Run quick smoke tests to verify basic functionality"""
        print("\n[ROCKET] RUNNING SMOKE TESTS")
        
        smoke_tests = [
            (['python', 'debug-testing/check_server_status.py'], 'Server Health Check'),
            (['python', 'debug-testing/quick_status_check.py'], 'Quick Status Check'),
            (['python', 'test_suite.py', 'TestServer.test_health_endpoint'], 'Server Health Unit Test'),
        ]
        
        results = []
        for command, name in smoke_tests:
            result = self.run_command(command, name, timeout=60)
            results.append(result)
            self.results.append(result)
            
            # Stop on first failure for smoke tests
            if not result.success:
                print(f"[FAIL] Smoke test failed: {name}")
                break
        
        return results
    
    def run_unit_tests(self) -> List[TestResult]:
        """Run comprehensive unit tests"""
        print("\n[TEST] RUNNING UNIT TESTS")
        
        unit_tests = [
            (['python', 'test_suite.py', 'TestCodeAnalyzerServer'], 'Server API Tests'),
            (['python', 'test_suite.py', 'TestOpenWebUIPlugin'], 'Plugin Unit Tests'),
            (['python', 'test_suite.py', 'TestIntelligentCaching'], 'Caching System Tests'),
        ]
        
        results = []
        for command, name in unit_tests:
            result = self.run_command(command, name)
            results.append(result)
            self.results.append(result)
        
        return results
    
    def run_integration_tests(self) -> List[TestResult]:
        """Run integration tests"""
        print("\n[LINK] RUNNING INTEGRATION TESTS")
        
        integration_tests = [
            (['python', 'test_suite.py', 'TestIntegration'], 'End-to-End Integration'),
            (['python', 'debug-testing/test_code_analyzer_server_apis.py'], 'API Integration'),
            (['python', 'debug-testing/verify_complete_functionality.py'], 'Complete Functionality'),
        ]
        
        results = []
        for command, name in integration_tests:
            result = self.run_command(command, name)
            results.append(result)
            self.results.append(result)
        
        return results
    
    def run_performance_tests(self) -> List[TestResult]:
        """Run performance and benchmark tests"""
        print("\n[RUNNER] RUNNING PERFORMANCE TESTS")
        
        performance_tests = [
            (['python', 'performance_test_suite.py', '--benchmark'], 'Response Time Benchmarks'),
            (['python', 'performance_test_suite.py', '--cache-test'], 'Cache Performance Tests'),
            (['python', 'debug-testing/test_collections.py', '--benchmark'], 'Collection Performance'),
        ]
        
        results = []
        for command, name in performance_tests:
            result = self.run_command(command, name, timeout=600)  # Longer timeout for performance tests
            results.append(result)
            self.results.append(result)
        
        return results
    
    def run_regression_tests(self) -> List[TestResult]:
        """Run regression tests against baseline"""
        print("\n[CYCLE] RUNNING REGRESSION TESTS")

        # Load baseline if it exists
        baseline_path = Path(self.config['baseline_file'])
        baseline = {}
        if baseline_path.exists():
            try:
                with open(baseline_path, 'r') as f:
                    baseline = json.load(f)
                print(f"📊 Loaded baseline from {baseline_path}")
            except Exception as e:
                print(f"⚠️ Could not load baseline: {e}")
        else:
            print(f"ℹ️ No baseline found at {baseline_path}")

        regression_tests = [
            (['python', 'debug-testing/test_all_languages.py'], 'Language Support Regression'),
            (['python', 'debug-testing/test_enhanced_search_fix.py'], 'Enhanced Search Regression'),
            (['python', 'debug-testing/test_codebase_selection.py'], 'Codebase Selection Regression'),
        ]

        results = []
        for command, name in regression_tests:
            result = self.run_command(command, name)
            results.append(result)
            self.results.append(result)

            # Compare against baseline if available
            if baseline and 'test_results' in baseline:
                self._compare_with_baseline(result, baseline['test_results'])

        return results

    def _compare_with_baseline(self, current_result: TestResult, baseline_results: List[dict]):
        """Compare current test result with baseline performance"""
        # Find matching baseline result
        baseline_result = None
        for baseline in baseline_results:
            if baseline.get('name') == current_result.name:
                baseline_result = baseline
                break

        if not baseline_result:
            return  # No baseline data for this test

        baseline_duration = baseline_result.get('duration', 0)
        current_duration = current_result.duration

        # Check for significant performance regression (>50% slower)
        if baseline_duration > 0 and current_duration > baseline_duration * 1.5:
            regression_pct = ((current_duration - baseline_duration) / baseline_duration) * 100
            print(f"⚠️ PERFORMANCE REGRESSION: {current_result.name}")
            print(f"   Baseline: {baseline_duration:.1f}s, Current: {current_duration:.1f}s (+{regression_pct:.1f}%)")
        elif baseline_duration > 0 and current_duration < baseline_duration * 0.8:
            improvement_pct = ((baseline_duration - current_duration) / baseline_duration) * 100
            print(f"✅ PERFORMANCE IMPROVEMENT: {current_result.name}")
            print(f"   Baseline: {baseline_duration:.1f}s, Current: {current_duration:.1f}s (-{improvement_pct:.1f}%)")

    def generate_report(self) -> str:
        """Generate comprehensive test report"""
        total_duration = (datetime.now() - self.start_time).total_seconds()
        
        # Calculate summary statistics
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.success)
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        # Group results by category
        categories = {}
        for result in self.results:
            category = result.name.split()[0] if ' ' in result.name else 'Other'
            if category not in categories:
                categories[category] = {'passed': 0, 'failed': 0, 'total_time': 0.0}
            
            if result.success:
                categories[category]['passed'] += 1
            else:
                categories[category]['failed'] += 1
            categories[category]['total_time'] += result.duration
        
        # Generate report
        report = f"""
# Test Execution Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Duration: {total_duration:.1f} seconds

## Summary
- **Total Tests**: {total_tests}
- **Passed**: {passed_tests} ({success_rate:.1f}%)
- **Failed**: {failed_tests}
- **Success Rate**: {success_rate:.1f}%

## Results by Category
"""
        
        for category, stats in categories.items():
            total = stats['passed'] + stats['failed']
            cat_success_rate = (stats['passed'] / total * 100) if total > 0 else 0
            report += f"- **{category}**: {stats['passed']}/{total} passed ({cat_success_rate:.1f}%) - {stats['total_time']:.1f}s\n"
        
        report += "\n## Detailed Results\n"
        
        for result in self.results:
            status = "[PASS]" if result.success else "[FAIL]"
            report += f"- {status} **{result.name}** ({result.duration:.1f}s)\n"
            
            if not result.success and result.details.get('error'):
                report += f"  - Error: {result.details['error']}\n"
        
        # Save report
        report_file = os.path.join(
            self.config['report_dir'],
            f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        )
        
        with open(report_file, 'w') as f:
            f.write(report)
        
        print(f"\n📄 Report saved to: {report_file}")
        return report
    
    def save_performance_baseline(self):
        """Save current performance metrics as baseline"""
        baseline = {
            'timestamp': datetime.now().isoformat(),
            'performance_metrics': {},
            'test_results': []
        }
        
        for result in self.results:
            if 'Performance' in result.name or 'Benchmark' in result.name:
                baseline['test_results'].append({
                    'name': result.name,
                    'duration': result.duration,
                    'success': result.success
                })
        
        baseline_path = Path(self.config['baseline_file'])
        with open(baseline_path, 'w') as f:
            json.dump(baseline, f, indent=2)
        
        print(f"💾 Performance baseline saved to: {baseline_path}")


def main():
    parser = argparse.ArgumentParser(description='Comprehensive Test Runner')
    parser.add_argument('--quick', action='store_true', help='Run quick smoke tests only')
    parser.add_argument('--full', action='store_true', help='Run comprehensive test suite')
    parser.add_argument('--performance', action='store_true', help='Run performance tests only')
    parser.add_argument('--regression', action='store_true', help='Run regression tests only')
    parser.add_argument('--report', action='store_true', help='Generate detailed report')
    parser.add_argument('--save-baseline', action='store_true', help='Save performance baseline')
    
    args = parser.parse_args()
    
    runner = TestRunner(TEST_CONFIG)
    
    print("[ROCKET] STARTING COMPREHENSIVE TEST EXECUTION")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        if args.quick:
            runner.run_smoke_tests()
        elif args.performance:
            runner.run_performance_tests()
        elif args.regression:
            runner.run_regression_tests()
        elif args.full:
            runner.run_smoke_tests()
            runner.run_unit_tests()
            runner.run_integration_tests()
            runner.run_performance_tests()
            runner.run_regression_tests()
        else:
            # Default: run core tests
            runner.run_smoke_tests()
            runner.run_unit_tests()
            runner.run_integration_tests()
        
        # Generate report
        if args.report or args.full:
            report = runner.generate_report()
            print("\n" + "="*60)
            print("📊 FINAL REPORT")
            print("="*60)
            print(report)
        
        # Save baseline if requested
        if args.save_baseline:
            runner.save_performance_baseline()
        
        # Exit with appropriate code
        all_passed = all(result.success for result in runner.results)
        if all_passed:
            print("\n[SUCCESS] ALL TESTS PASSED!")
            sys.exit(0)
        else:
            print("\n[WARNING] SOME TESTS FAILED!")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n[STOP] Test execution interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n[ERROR] Test execution failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
