# Enhanced Multi-Language RAG Server for Code Analysis

A comprehensive tool server that provides intelligent code analysis capabilities for **C/C++/C#/Python** codebases using RAG (Retrieval-Augmented Generation) technology.

## Features

### 🚀 **Multi-Language & Multi-Codebase Support**
- Support for **C, C++, C#, and Python** codebases
- Manage multiple projects independently across different languages
- Switch between codebases seamlessly
- Isolated vector databases for each project

### 🔍 **Intelligent Code Analysis**
- Semantic code search using vector embeddings
- AI-powered code explanations and insights
- Language-aware analysis and cross-language pattern recognition
- Comprehensive preprocessing with tree-sitter parsing

### 🛠️ **Advanced Processing Pipeline**
- **Code Preprocessing**: Extract functions, classes, methods, structs, enums, templates, imports
- **Vector Database Creation**: ChromaDB with automatic embeddings
- **Ollama Integration**: AI-powered analysis and explanations
- **Language Detection**: Automatic detection of programming languages in codebases

## Supported Languages & File Types

### **C/C++**
- **C Files**: `.c`, `.h`
- **C++ Files**: `.cpp`, `.cxx`, `.cc`, `.c++`, `.hpp`, `.hxx`, `.hh`
- **Features**: Functions, Classes, Methods, Namespaces, Structs, Enums, Templates, Typedefs

### **Python**
- **Python Files**: `.py`, `.pyw`
- **Features**: Functions, Classes, Methods, Imports, Decorators, Modules

### **C#**
- **C# Files**: `.cs`
- **Features**: Methods, Classes, Interfaces, Structs, Enums, Namespaces, Properties, Using directives

## Directory Structure

```
openwebui_rag_server/
├── main.py                    # Enhanced RAG server with multi-language support
├── code_preprocessor.py       # Multi-language code parsing and chunk extraction
├── vector_db_creator.py       # ChromaDB collection creation and management
├── open-webui-rag-plugin.py   # OpenWebUI plugin with enhanced tools
├── Dockerfile                 # Container configuration
├── requirements.txt           # Python dependencies
└── README.md                  # This file
```

## Installation

### Prerequisites
- Python 3.10+
- Docker and Docker Compose
- Access to Ollama service

### Setup Steps

1. **Create the directory structure:**
```bash
mkdir -p openwebui_rag_server
cd openwebui_rag_server
```

2. **Copy all files to the directory**

3. **Prepare source code:**
```bash
mkdir -p ../source_code
# Place your codebases in subdirectories:
# ../source_code/my_cpp_project/
# ../source_code/my_python_app/
# ../source_code/my_csharp_service/
# etc.
```

4. **Build and run:**
```bash
docker-compose up --build
```

## Usage

### 1. **List Available Codebases**
```python
list_codebases()
```
Shows all codebases in the source_code directory with their indexing status and detected languages.

### 2. **Process a New Codebase**
```python
process_codebase("my_project", exclude_dirs=["build", "test", "bin", "obj"])
```
Analyzes source code across all supported languages and creates searchable vector database.

### 3. **Select Working Codebase**
```python
select_codebase("my_project")
```
Sets the active codebase for subsequent operations.

### 4. **Search Code**
```python
search_code("database connection", "my_project", filter_language="python")
```
Finds relevant code snippets using semantic search with language filtering.

### 5. **Ask Questions**
```python
ask_about_code("How does error handling work across languages?", "my_project")
```
Gets AI-powered explanations about code functionality with multi-language awareness.

### 6. **Get Statistics**
```python
get_codebase_stats("my_project")
```
View metrics about indexed code chunks, language breakdown, and structure.

## Code Processing Features

### **Supported Code Structures by Language**

#### **C/C++**
- **Functions**: Complete function definitions with signatures
- **Classes**: C++ class definitions with methods
- **Methods**: Individual class member functions
- **Namespaces**: C++ namespace definitions
- **Structs**: Structure definitions
- **Enums**: Enumeration definitions
- **Typedefs**: Type definitions
- **Templates**: Template definitions (C++)
- **Headers**: Include files and macro definitions

#### **Python**
- **Functions**: Function definitions with decorators
- **Classes**: Class definitions with methods
- **Methods**: Class member functions
- **Imports**: Import statements and from imports
- **Decorators**: Decorated definitions
- **Modules**: Module-level code organization

#### **C#**
- **Methods**: Method declarations in classes/interfaces
- **Classes**: Class declarations with members
- **Interfaces**: Interface definitions
- **Structs**: Structure declarations
- **Enums**: Enumeration declarations
- **Namespaces**: Namespace organization
- **Properties**: Property definitions
- **Using Directives**: Namespace imports

### **Processing Pipeline**
1. **Language Detection**: Automatic identification of programming languages
2. **Tree-sitter Parsing**: Accurate syntax analysis for each language
3. **Chunk Extraction**: Language-specific semantic code segments
4. **Metadata Enhancement**: Rich context information with language tags
5. **Vector Embedding**: ChromaDB with semantic search capabilities
6. **Cross-Language Indexing**: Unified search across multiple languages

## API Endpoints

### **Tool Endpoints (for OpenWebUI)**
- `POST /tools/list_codebases` - List available codebases with language info
- `POST /tools/select_codebase` - Select active codebase
- `POST /tools/process_codebase` - Process and index multi-language codebase
- `POST /tools/search_code` - Search for code snippets with language filters
- `POST /tools/ask_about_code` - AI-powered multi-language code analysis
- `POST /tools/get_code_stats` - Get codebase statistics with language breakdown

### **Direct API Endpoints**
- `GET /health` - System health check with language support status
- `GET /stats` - Current codebase statistics
- `POST /search` - Direct search interface with language filtering
- `POST /ask` - Direct AI query interface with multi-language context

## Configuration

### **Environment Variables**
```bash
OLLAMA_HOST=http://ollama:11434              # Ollama service URL
CHROMA_DB_BASE_PATH=./chroma_db              # ChromaDB storage path
SOURCE_CODE_BASE_PATH=./source_code          # Source code directory
```

### **Docker Compose Integration**
```yaml
openwebui-rag-server:
  container_name: openwebui-rag-server
  build:
    context: ./openwebui_rag_server
  ports:
    - "5002:5002"
  environment:
    - OLLAMA_HOST=http://ollama:11434
  volumes:
    - chroma_db:/app/chroma_db
    - ./source_code:/app/source_code:ro
  depends_on:
    - ollama
```

## Examples

### **Processing a Multi-Language Codebase**
```python
# 1. Check what's available
list_codebases()

# 2. Process new multi-language codebase
process_codebase("web_service", exclude_dirs=["build", "test", "bin", "obj", "__pycache__"])

# 3. Select for analysis
select_codebase("web_service")

# 4. Get overview with language breakdown
get_codebase_stats("web_service")
```

### **Language-Specific Analysis**
```python
# Search for Python-specific patterns
search_code("exception handling", "web_service", filter_language="python")

# Find C# database code
search_code("Entity Framework", "web_service", filter_language="csharp")

# Look for C++ performance optimizations
search_code("memory pool", "web_service", filter_language="cpp")

# Cross-language API analysis
ask_about_code("How do the Python and C# services communicate?", "web_service")
```

### **Multi-Language Comparative Analysis**
```python
# Compare error handling across languages
ask_about_code("Compare error handling patterns across Python, C#, and C++", "web_service")

# Find similar functionality in different languages
search_code("logging", "web_service", n_results=10)  # Will show results from all languages

# Analyze architecture patterns
ask_about_code("What design patterns are used across the different language components?", "web_service")
```

### **Language Migration Analysis**
```python
# Understanding code relationships for migration
ask_about_code("What C++ components could be ported to C#?", "legacy_system")

# API compatibility analysis
search_code("public interface", "microservices", filter_language="csharp")
search_code("def api_", "microservices", filter_language="python")
```

## Language-Specific Features

### **Advanced Search Filters**
- **filter_language**: `"c"`, `"cpp"`, `"python"`, `"csharp"`
- **filter_type**: `"function"`, `"class"`, `"method"`, `"namespace"`, `"import"`, etc.
- **filter_file**: Pattern matching for specific files or directories

### **Language-Aware AI Analysis**
The AI model provides language-specific insights:
- **Python**: PEP compliance, pythonic patterns, async/await usage
- **C#**: .NET conventions, LINQ patterns, async patterns
- **C++**: Memory management, RAII patterns, template usage
- **C**: Function organization, memory safety, portability

## Troubleshooting

### **Common Issues**

**Language not detected properly:**
- Verify file extensions are correct
- Check if files contain actual code (not just comments)
- Review exclude_dirs to ensure source files aren't excluded

**No results from search:**
- Verify codebase is processed: `get_codebase_stats("codebase_name")`
- Check language filter: try without filter first
- Use broader search terms

**Processing fails for specific languages:**
- Check tree-sitter-language-pack installation
- Verify file encoding (UTF-8 recommended)
- Review syntax errors in source files

**Mixed language results:**
- Use `filter_language` parameter to narrow results
- Check file extensions and language detection
- Verify metadata in search results

### **Performance Tips**

1. **Use language filters** to improve search precision
2. **Exclude build artifacts** (bin/, obj/, __pycache__, etc.)
3. **Process languages incrementally** for large codebases
4. **Use specific technical terms** in searches
5. **Leverage cross-language analysis** for architectural insights

## Integration with OpenWebUI

The enhanced RAG server integrates seamlessly with OpenWebUI through the provided plugin. Users can access all multi-language functionality through natural language commands in the chat interface.

**Enhanced Plugin Features:**
- Multi-language codebase management
- Language-aware code search
- Cross-language AI explanations
- Language-specific filtering and analysis
- Comprehensive help system with language examples
- Debug and troubleshooting tools

## Language Support Roadmap

**Currently Supported:**
- ✅ C
- ✅ C++
- ✅ Python
- ✅ C#

**Planned Future Support:**
- 🔄 Java
- 🔄 JavaScript/TypeScript
- 🔄 Go
- 🔄 Rust

## Performance Benchmarks

**Processing Speed (approximate):**
- **C/C++**: ~500-1000 files/minute
- **Python**: ~800-1200 files/minute  
- **C#**: ~600-1000 files/minute

**Index Size (approximate):**
- **Small Project** (< 100 files): 10-50 MB
- **Medium Project** (100-1000 files): 50-500 MB
- **Large Project** (1000+ files): 500+ MB

## License

[Add your license information here]