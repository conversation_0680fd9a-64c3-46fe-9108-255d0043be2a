#!/usr/bin/env python3
"""
Test the new stateless client-side selection architecture
"""

import requests
import json
import time

def call_api(query, description):
    """Make an API call and analyze response"""
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    model = "llama3:latest"
    tool_id = "code_analyzer_tool"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model,
        "messages": [{"role": "user", "content": query}],
        "tool_ids": [tool_id],
        "stream": False
    }
    
    print(f"\n🔧 {description}")
    print(f"   Query: '{query}'")
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=120
        )
        end_time = time.time()
        
        print(f"   Status: {response.status_code}, Time: {end_time - start_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            choices = result.get("choices", [])
            
            if choices:
                content = choices[0].get("message", {}).get("content", "")
                
                print(f"   Response Length: {len(content)} chars")
                print(f"   First 200 chars: {content[:200]}...")
                
                # Analyze response quality
                has_real_code = "tmwmem" in content.lower()
                has_no_codebase = "no codebase selected" in content.lower()
                has_context = "context" in content.lower()
                has_client_side = "client-side" in content.lower()
                has_stateless = "stateless" in content.lower()
                is_generic = any(word in content.lower() for word in [
                    "tensorflow", "general programming", "typical implementation"
                ])
                
                print(f"   Real Code (tmwmem): {has_real_code}")
                print(f"   No Codebase Error: {has_no_codebase}")
                print(f"   Has Context: {has_context}")
                print(f"   Client-Side Mention: {has_client_side}")
                print(f"   Stateless Mention: {has_stateless}")
                print(f"   Generic Response: {is_generic}")
                
                # Overall assessment
                if has_no_codebase:
                    quality = "NO_CODEBASE_ERROR"
                elif has_real_code and not is_generic:
                    quality = "EXCELLENT_WITH_CODE"
                elif has_client_side or has_stateless:
                    quality = "GOOD_ARCHITECTURE_INFO"
                elif has_context:
                    quality = "GOOD"
                elif is_generic:
                    quality = "GENERIC"
                else:
                    quality = "UNCLEAR"
                
                print(f"   Quality: {quality}")
                
                return {
                    "success": True,
                    "content": content,
                    "quality": quality,
                    "has_real_code": has_real_code,
                    "has_no_codebase": has_no_codebase,
                    "response_time": end_time - start_time
                }
            else:
                print("   ❌ No choices in response")
                return {"success": False, "quality": "FAILED"}
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            return {"success": False, "quality": "FAILED"}
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return {"success": False, "quality": "FAILED"}

def test_server_endpoints():
    """Test server endpoints directly to verify stateless behavior"""
    
    print("\n🔧 Testing server endpoints directly")
    
    # Test search without codebase_name (should fail)
    try:
        response = requests.post(
            "http://home-ai-server.local:5002/search",
            json={"query": "tmwmem_alloc"},
            timeout=10
        )
        print(f"   Search without codebase: {response.status_code}")
        if response.status_code == 400:
            print("   ✅ Server correctly rejects requests without codebase_name")
        else:
            print("   ❌ Server should reject requests without codebase_name")
    except Exception as e:
        print(f"   ❌ Error testing server: {e}")
    
    # Test search with codebase_name (should work)
    try:
        response = requests.post(
            "http://home-ai-server.local:5002/search",
            json={"query": "tmwmem_alloc", "codebase_name": "utils"},
            timeout=10
        )
        print(f"   Search with codebase: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Server correctly accepts requests with codebase_name")
        else:
            print("   ❌ Server should accept requests with codebase_name")
    except Exception as e:
        print(f"   ❌ Error testing server: {e}")

def main():
    """Test the stateless architecture"""
    
    print("🚀 TESTING STATELESS CLIENT-SIDE ARCHITECTURE")
    print("="*80)
    
    # Test server endpoints first
    test_server_endpoints()
    
    # Step 1: Unselect codebase (should be client-side only now)
    print("\n📋 STEP 1: Unselecting codebase (client-side)")
    result1 = call_api("unselect codebase", "Client-side unselect")
    time.sleep(3)
    
    # Step 2: Query without codebase (should give proper error)
    print("\n📋 STEP 2: Query without codebase selection")
    result2 = call_api("tmwmem_alloc", "Query without codebase")
    time.sleep(3)
    
    # Step 3: Select utils codebase (should be client-side only)
    print("\n📋 STEP 3: Selecting utils codebase (client-side)")
    result3 = call_api("select utils codebase", "Client-side select")
    time.sleep(3)
    
    # Step 4: Query with codebase (should work with real code)
    print("\n📋 STEP 4: Query with codebase selection")
    result4 = call_api("tmwmem_alloc", "Query with codebase")
    
    # Analysis
    print(f"\n{'='*80}")
    print("📊 STATELESS ARCHITECTURE ANALYSIS")
    print(f"{'='*80}")
    
    if result2.get("success") and result4.get("success"):
        without_quality = result2.get("quality", "UNKNOWN")
        with_quality = result4.get("quality", "UNKNOWN")
        
        print(f"\n🔍 Results:")
        print(f"   WITHOUT codebase: {without_quality}")
        print(f"   WITH codebase: {with_quality}")
        
        # Determine if the stateless architecture works
        if without_quality == "NO_CODEBASE_ERROR" and with_quality == "EXCELLENT_WITH_CODE":
            print(f"\n✅ SUCCESS: Stateless architecture works perfectly!")
            print(f"   - Without selection: Proper error message")
            print(f"   - With selection: Real code analysis")
            print(f"   - Client-side selection is working correctly")
        elif without_quality != with_quality:
            print(f"\n⚠️ PARTIAL SUCCESS: Different responses but may need refinement")
        else:
            print(f"\n❌ FAILED: Both scenarios give same quality - selection not working")
    else:
        print(f"\n❌ TEST FAILED: Could not complete both scenarios")
    
    # Check for architecture mentions
    if result1.get("success") and result3.get("success"):
        unselect_content = result1.get("content", "")
        select_content = result3.get("content", "")
        
        mentions_stateless = "stateless" in (unselect_content + select_content).lower()
        mentions_client_side = "client-side" in (unselect_content + select_content).lower()
        
        print(f"\n🏗️ Architecture Information:")
        print(f"   Mentions Stateless: {mentions_stateless}")
        print(f"   Mentions Client-Side: {mentions_client_side}")
        
        if mentions_stateless and mentions_client_side:
            print(f"   ✅ Tool correctly explains new architecture")
        else:
            print(f"   ⚠️ Tool should explain the new stateless architecture")

if __name__ == "__main__":
    main()
