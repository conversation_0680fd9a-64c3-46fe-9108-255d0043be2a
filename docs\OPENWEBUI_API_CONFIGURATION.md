# OpenWebUI API Key Configuration Guide

## 🔐 API Key Authentication Setup

Based on your OpenWebUI configuration, here's how to properly set up API key authentication for testing the Code Analysis tool.

## 📋 OpenWebUI Environment Variables

### **Current Configuration Check**
First, check your current OpenWebUI configuration:

```bash
# Check if API keys are enabled
echo $ENABLE_API_KEY  # Should be True (default)

# Check if endpoint restrictions are enabled  
echo $ENABLE_API_KEY_ENDPOINT_RESTRICTIONS  # Default: False

# Check allowed endpoints (if restrictions enabled)
echo $API_KEY_ALLOWED_ENDPOINTS

# Check JWT expiration
echo $JWT_EXPIRES_IN  # Default: -1 (no expiration)
```

## 🎯 Configuration Scenarios

### **Scenario 1: Default Configuration (Recommended)**
```bash
ENABLE_API_KEY=True
ENABLE_API_KEY_ENDPOINT_RESTRICTIONS=False
# No API_KEY_ALLOWED_ENDPOINTS needed
JWT_EXPIRES_IN=-1
```

**Result:** API keys work for all endpoints ✅

### **Scenario 2: Endpoint Restrictions Enabled**
```bash
ENABLE_API_KEY=True
ENABLE_API_KEY_ENDPOINT_RESTRICTIONS=True
API_KEY_ALLOWED_ENDPOINTS="/api/chat/completions,/api/models,/health"
JWT_EXPIRES_IN=-1
```

**Result:** API keys only work for specified endpoints ⚠️

### **Scenario 3: API Keys Disabled**
```bash
ENABLE_API_KEY=False
# Other settings don't matter
```

**Result:** No API authentication required, but may have other auth ❌

## 🔧 Setup Instructions

### **Step 1: Create API Key in OpenWebUI**

1. **Access OpenWebUI:**
   ```
   http://home-ai-server.local:8080
   ```

2. **Navigate to Settings:**
   - Click your profile/avatar
   - Go to "Settings"
   - Select "Account" tab
   - Find "API Keys" section

3. **Create New API Key:**
   - Click "Create API Key"
   - Give it a name (e.g., "Code Analysis Tool Testing")
   - Copy the generated key immediately (you won't see it again)

### **Step 2: Configure for Testing**

#### **Option A: Environment Variable (Recommended)**
```bash
export OPENWEBUI_API_KEY="sk-your-api-key-here"
python debug-testing/openwebui_auto_tester.py
```

#### **Option B: Interactive Input**
```bash
python debug-testing/openwebui_auto_tester.py
# Enter API key when prompted
```

### **Step 3: Handle Endpoint Restrictions**

If you have `ENABLE_API_KEY_ENDPOINT_RESTRICTIONS=True`, you need to allow the testing endpoints:

#### **Required Endpoints for Testing:**
```bash
API_KEY_ALLOWED_ENDPOINTS="/api/chat/completions,/api/models,/health,/api/chat,/api/v1/chat/completions"
```

#### **Update OpenWebUI Configuration:**
```bash
# Add to your OpenWebUI environment file or docker-compose
ENABLE_API_KEY_ENDPOINT_RESTRICTIONS=True
API_KEY_ALLOWED_ENDPOINTS="/api/chat/completions,/api/models,/health,/api/chat,/api/v1/chat/completions"
```

#### **Restart OpenWebUI:**
```bash
# If using Docker
docker restart openwebui

# If using systemd
sudo systemctl restart openwebui
```

## 🧪 Testing API Configuration

### **Test API Permissions**
The auto-tester now includes permission testing:

```bash
python debug-testing/openwebui_auto_tester.py
```

**Expected Output:**
```
🔐 Testing API Key Permissions:
----------------------------------------
✅ /api/chat/completions: 200 - Chat completions (main testing endpoint)
✅ /api/models: 200 - Models list  
✅ /health: 200 - Health check
✅ /api/chat: 200 - Chat endpoint
✅ /api/v1/chat/completions: 200 - Alternative chat endpoint
```

### **Common Permission Issues**

#### **❌ 403 Forbidden Errors**
```
❌ /api/chat/completions: 403 - Chat completions (main testing endpoint)
```

**Causes:**
- Invalid API key
- Endpoint restrictions enabled without allowing the endpoint
- API key expired (if JWT_EXPIRES_IN is set)

**Solutions:**
1. **Check API Key:**
   ```bash
   # Test with curl
   curl -H "Authorization: Bearer $OPENWEBUI_API_KEY" \
        http://home-ai-server.local:8080/api/models
   ```

2. **Add Missing Endpoints:**
   ```bash
   # Update configuration
   API_KEY_ALLOWED_ENDPOINTS="/api/chat/completions,/api/models,/health"
   ```

3. **Disable Restrictions Temporarily:**
   ```bash
   ENABLE_API_KEY_ENDPOINT_RESTRICTIONS=False
   ```

#### **❌ 401 Unauthorized Errors**
```
❌ HTTP 401: {"detail":"Not authenticated"}
```

**Causes:**
- No API key provided
- Malformed Authorization header

**Solutions:**
- Ensure API key is set correctly
- Check environment variable name: `OPENWEBUI_API_KEY`

## 🎯 Recommended Configuration for Testing

### **For Development/Testing:**
```bash
# Disable endpoint restrictions for easier testing
ENABLE_API_KEY=True
ENABLE_API_KEY_ENDPOINT_RESTRICTIONS=False
JWT_EXPIRES_IN=-1
```

### **For Production:**
```bash
# Enable restrictions with specific endpoints
ENABLE_API_KEY=True
ENABLE_API_KEY_ENDPOINT_RESTRICTIONS=True
API_KEY_ALLOWED_ENDPOINTS="/api/chat/completions,/api/models"
JWT_EXPIRES_IN=86400  # 24 hours
```

## 🔄 Alternative Testing Methods

If API key setup is problematic, use these alternatives:

### **1. Browser Testing (No Auth Required)**
```bash
python debug-testing/openwebui_browser_tester.py
```

### **2. Direct Tool Testing**
```bash
python debug-testing/interactive_tool_test.py
```

### **3. Manual curl Commands**
```bash
./debug-testing/curl_test_commands.sh
```

## 📊 Troubleshooting Checklist

- [ ] OpenWebUI server is running
- [ ] API keys are enabled (`ENABLE_API_KEY=True`)
- [ ] API key is created in OpenWebUI interface
- [ ] API key is set in environment or provided to script
- [ ] If endpoint restrictions enabled, required endpoints are allowed
- [ ] OpenWebUI restarted after configuration changes
- [ ] No firewall blocking API requests
- [ ] Correct OpenWebUI URL (`http://home-ai-server.local:8080`)

## 🎉 Success Indicators

When properly configured, you should see:
- ✅ All API permission tests pass
- ✅ Chat completions endpoint accessible
- ✅ Tool responses in test results
- ✅ No 401/403 authentication errors
- ✅ Formatted tool output (not just plain LLM responses)

The auto-tester will now guide you through any API configuration issues! 🚀
