#!/usr/bin/env python3
"""
Integration test for new language support in the RAG server.
Tests that the server correctly processes and indexes the new languages.
"""

import requests
import json
import tempfile
import shutil
from pathlib import Path

# Server configuration
RAG_SERVER_URL = "http://home-ai-server.local:5002"

def create_test_codebase():
    """Create a test codebase with files in all supported languages"""
    temp_dir = tempfile.mkdtemp()
    codebase_path = Path(temp_dir) / "test_multilang_codebase"
    codebase_path.mkdir()
    
    # Create source directory
    src_dir = codebase_path / "src"
    src_dir.mkdir()
    
    # JavaScript file
    js_file = src_dir / "api.js"
    js_file.write_text("""
// JavaScript API client
class ApiClient {
    constructor(baseUrl) {
        this.baseUrl = baseUrl;
    }
    
    async fetchUser(id) {
        try {
            const response = await fetch(`${this.baseUrl}/users/${id}`);
            return await response.json();
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }
}

module.exports = ApiClient;
""")
    
    # TypeScript file
    ts_file = src_dir / "types.ts"
    ts_file.write_text("""
// TypeScript type definitions
interface User {
    id: number;
    name: string;
    email: string;
    roles: Role[];
}

interface Role {
    id: number;
    name: string;
    permissions: Permission[];
}

type Permission = 'read' | 'write' | 'admin';

export { User, Role, Permission };
""")
    
    # Rust file
    rust_file = src_dir / "lib.rs"
    rust_file.write_text("""
// Rust library for user management
use std::collections::HashMap;

#[derive(Debug, Clone)]
pub struct User {
    pub id: u32,
    pub name: String,
    pub email: String,
}

impl User {
    pub fn new(id: u32, name: String, email: String) -> Result<Self, String> {
        if name.is_empty() {
            return Err("Name cannot be empty".to_string());
        }
        Ok(User { id, name, email })
    }
}

pub fn create_user_map(users: Vec<User>) -> HashMap<u32, User> {
    users.into_iter().map(|user| (user.id, user)).collect()
}
""")
    
    # Java file
    java_file = src_dir / "UserService.java"
    java_file.write_text("""
// Java service class
package com.example.service;

import java.util.List;
import java.util.Optional;

public class UserService {
    private final UserRepository repository;

    public UserService(UserRepository repository) {
        this.repository = repository;
    }

    public Optional<User> findById(Long id) {
        try {
            return repository.findById(id);
        } catch (Exception e) {
            throw new ServiceException("Failed to find user", e);
        }
    }

    public List<User> findAll() {
        return repository.findAll();
    }
}
""")

    # Go file
    go_file = src_dir / "server.go"
    go_file.write_text("""
// Go web server with goroutines
package main

import (
    "context"
    "fmt"
    "net/http"
    "sync"
    "time"
)

type Server struct {
    mu    sync.RWMutex
    users map[int]User
}

func (s *Server) handleUsers(w http.ResponseWriter, r *http.Request) {
    ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
    defer cancel()

    select {
    case <-ctx.Done():
        http.Error(w, "Request timeout", http.StatusRequestTimeout)
        return
    default:
        s.mu.RLock()
        defer s.mu.RUnlock()
        fmt.Fprintf(w, "Users: %v", s.users)
    }
}

func main() {
    server := &Server{users: make(map[int]User)}
    http.HandleFunc("/users", server.handleUsers)
    http.ListenAndServe(":8080", nil)
}
""")

    # SQL file
    sql_file = src_dir / "schema.sql"
    sql_file.write_text("""
-- Database schema with complex queries
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_users_email ON users(email);

-- Complex query with CTE
WITH active_users AS (
    SELECT u.*,
           COUNT(o.id) as order_count
    FROM users u
    LEFT JOIN orders o ON u.id = o.user_id
    WHERE u.created_at >= CURRENT_DATE - INTERVAL '30 days'
    GROUP BY u.id
)
SELECT * FROM active_users
WHERE order_count > 0
ORDER BY order_count DESC;

-- Stored function
CREATE OR REPLACE FUNCTION get_user_stats(user_id INTEGER)
RETURNS TABLE(total_orders INTEGER, total_spent DECIMAL) AS $$
BEGIN
    RETURN QUERY
    SELECT COUNT(*)::INTEGER, COALESCE(SUM(total_amount), 0)
    FROM orders
    WHERE orders.user_id = get_user_stats.user_id;
END;
$$ LANGUAGE plpgsql;
""")
    
    return str(codebase_path), temp_dir

def test_server_health():
    """Test that the RAG server is running"""
    try:
        response = requests.get(f"{RAG_SERVER_URL}/health")
        if response.status_code == 200:
            print("✅ RAG server is running")
            return True
        else:
            print(f"❌ RAG server health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to RAG server. Make sure it's running on port 5002")
        return False

def test_codebase_creation_and_indexing():
    """Test creating and indexing a codebase with new languages"""
    print("\n🔧 Testing codebase creation and indexing...")
    
    codebase_path, temp_dir = create_test_codebase()
    codebase_name = "test_multilang"
    
    try:
        # Create codebase
        create_data = {
            "codebase_name": codebase_name,
            "source_path": codebase_path
        }
        
        response = requests.post(f"{RAG_SERVER_URL}/create_codebase", json=create_data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Codebase created: {result.get('message', 'Success')}")
            
            # Check if new languages were detected
            if 'detected_languages' in result:
                languages = result['detected_languages']
                print(f"📋 Detected languages: {languages}")
                
                expected_languages = {'JavaScript', 'TypeScript', 'Rust', 'Java', 'Go', 'SQL'}
                detected_set = set(languages)
                
                if expected_languages.issubset(detected_set):
                    print("✅ All new languages detected correctly")
                else:
                    missing = expected_languages - detected_set
                    print(f"⚠️  Missing languages: {missing}")
            
            return True
        else:
            print(f"❌ Failed to create codebase: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error during codebase creation: {e}")
        return False
    finally:
        # Cleanup
        shutil.rmtree(temp_dir)

def test_language_specific_search():
    """Test searching for language-specific code"""
    print("\n🔍 Testing language-specific search...")
    
    codebase_name = "test_multilang"
    
    # Test searches for each new language
    test_queries = [
        ("async function", "javascript"),
        ("interface User", "typescript"),
        ("Result<Self, String>", "rust"),
        ("public class", "java"),
        ("goroutine channel", "go"),
        ("SELECT FROM", "sql")
    ]
    
    for query, expected_lang in test_queries:
        try:
            search_data = {
                "query": query,
                "codebase_name": codebase_name,
                "n_results": 3
            }
            
            response = requests.post(f"{RAG_SERVER_URL}/search", json=search_data)
            
            if response.status_code == 200:
                results = response.json()
                if results and len(results) > 0:
                    # Check if results contain the expected language
                    found_lang = False
                    for result in results:
                        if 'metadata' in result and 'language' in result['metadata']:
                            if result['metadata']['language'] == expected_lang:
                                found_lang = True
                                break
                    
                    if found_lang:
                        print(f"✅ {expected_lang.capitalize()} search successful: '{query}'")
                    else:
                        print(f"⚠️  {expected_lang.capitalize()} search returned results but wrong language")
                else:
                    print(f"⚠️  {expected_lang.capitalize()} search returned no results: '{query}'")
            else:
                print(f"❌ {expected_lang.capitalize()} search failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error searching for {expected_lang}: {e}")

def test_statistics():
    """Test that statistics include new languages"""
    print("\n📊 Testing statistics with new languages...")
    
    codebase_name = "test_multilang"
    
    try:
        response = requests.get(f"{RAG_SERVER_URL}/stats/{codebase_name}")
        
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ Statistics retrieved successfully")
            
            if 'languages' in stats:
                languages = stats['languages']
                print(f"📋 Languages in stats: {list(languages.keys())}")
                
                # Check for new languages
                new_langs = ['JavaScript', 'TypeScript', 'Rust', 'Java', 'Go', 'SQL']
                found_new_langs = [lang for lang in new_langs if lang in languages]
                
                if found_new_langs:
                    print(f"✅ New languages found in stats: {found_new_langs}")
                else:
                    print("⚠️  No new languages found in statistics")
            else:
                print("⚠️  No language information in statistics")
                
        else:
            print(f"❌ Failed to get statistics: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error getting statistics: {e}")

def cleanup_test_codebase():
    """Clean up the test codebase"""
    print("\n🧹 Cleaning up test codebase...")
    
    try:
        response = requests.delete(f"{RAG_SERVER_URL}/delete_codebase/test_multilang")
        if response.status_code == 200:
            print("✅ Test codebase cleaned up")
        else:
            print(f"⚠️  Cleanup warning: {response.status_code}")
    except Exception as e:
        print(f"⚠️  Cleanup error: {e}")

def main():
    """Run all integration tests"""
    print("🚀 Testing New Language Integration with RAG Server")
    print("=" * 60)
    
    # Check server health first
    if not test_server_health():
        print("\n❌ Cannot proceed without RAG server running")
        return
    
    try:
        # Run tests
        if test_codebase_creation_and_indexing():
            test_language_specific_search()
            test_statistics()
        
        print("\n" + "=" * 60)
        print("✅ Integration tests completed!")
        
    except Exception as e:
        print(f"\n❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        cleanup_test_codebase()

if __name__ == "__main__":
    main()
