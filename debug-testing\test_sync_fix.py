#!/usr/bin/env python3
"""
Quick test to verify the codebase sync fix is working.
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path to import the tool
sys.path.append(str(Path(__file__).parent))

from open_webui_code_analyzer_tool import Tools

async def test_sync_fix():
    """Test the codebase sync fix"""
    print("🧪 Testing Codebase Sync Fix")
    print("=" * 50)
    
    # Initialize the tool with correct server URL
    tool = Tools()
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    
    print(f"1. Initial tool state: {tool.valves.current_codebase or 'None'}")
    
    # Test sync with server state
    print("2. Syncing with server state...")
    await tool._sync_with_server_state()
    print(f"   After sync: {tool.valves.current_codebase or 'None'}")
    
    # Test smart_code_context with memory management query
    print("3. Testing smart_code_context...")
    try:
        result = await tool.smart_code_context("memory management practices")
        print(f"   Result length: {len(result)} characters")
        if "❌" in result:
            print(f"   Error: {result}")
        else:
            print("   ✅ Success! Context retrieved")
            # Show first 200 characters
            print(f"   Preview: {result[:200]}...")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Sync fix test completed!")

if __name__ == "__main__":
    try:
        asyncio.run(test_sync_fix())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
