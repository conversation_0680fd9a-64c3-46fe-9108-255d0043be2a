#!/usr/bin/env python3
"""
Performance Test Suite for OpenWebUI Code Analyzer

This suite focuses on performance testing, benchmarking, and load testing
to ensure the system performs well under various conditions.

Features:
- Response time benchmarking
- Cache performance validation
- Concurrent request testing
- Memory usage monitoring
- Throughput testing
- Performance regression detection

Usage:
    python performance_test_suite.py                    # Run all performance tests
    python performance_test_suite.py --benchmark        # Run benchmarks only
    python performance_test_suite.py --load-test        # Run load tests only
    python performance_test_suite.py --cache-test       # Test cache performance
"""

import unittest
import asyncio
import requests
import time
import threading
import psutil
import statistics
import json
import sys
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Tuple
from datetime import datetime, timedelta

# Add parent directory for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from open_webui_code_analyzer_tool import Tools

# Performance Test Configuration
PERF_CONFIG = {
    'server_url': 'http://home-ai-server.local:5002',
    'test_codebase': 'utils',
    'benchmark_queries': [
        'memory management functions',
        'error handling patterns', 
        'network operations',
        'file I/O operations',
        'string manipulation',
        'data structures',
        'algorithm implementations',
        'security functions'
    ],
    'load_test_duration': 30,  # seconds
    'concurrent_users': [1, 2, 5, 10],
    'cache_test_iterations': 50,
    'performance_thresholds': {
        'avg_response_time': 3.0,  # seconds
        'p95_response_time': 5.0,  # seconds
        'cache_hit_improvement': 0.5,  # 50% faster
        'memory_usage_mb': 500,  # MB
        'error_rate': 0.05  # 5%
    }
}


class PerformanceMonitor:
    """Monitor system performance during tests"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.start_memory = None
        self.peak_memory = 0
        self.start_time = None
        
    def start_monitoring(self):
        """Start performance monitoring"""
        self.start_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        self.peak_memory = self.start_memory
        self.start_time = time.time()
        
    def update_peak_memory(self):
        """Update peak memory usage"""
        current_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        self.peak_memory = max(self.peak_memory, current_memory)
        
    def get_stats(self) -> Dict[str, float]:
        """Get performance statistics"""
        current_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        duration = time.time() - self.start_time if self.start_time else 0
        
        return {
            'start_memory_mb': self.start_memory or 0,
            'current_memory_mb': current_memory,
            'peak_memory_mb': self.peak_memory,
            'memory_increase_mb': current_memory - (self.start_memory or 0),
            'duration_seconds': duration,
            'cpu_percent': self.process.cpu_percent()
        }


class ResponseTimeCollector:
    """Collect and analyze response times"""
    
    def __init__(self):
        self.response_times: List[float] = []
        self.errors: List[str] = []
        
    def add_response_time(self, response_time: float):
        """Add a response time measurement"""
        self.response_times.append(response_time)
        
    def add_error(self, error: str):
        """Add an error"""
        self.errors.append(error)
        
    def get_statistics(self) -> Dict[str, Any]:
        """Get response time statistics"""
        if not self.response_times:
            return {'error': 'No response times recorded'}
            
        return {
            'count': len(self.response_times),
            'min': min(self.response_times),
            'max': max(self.response_times),
            'mean': statistics.mean(self.response_times),
            'median': statistics.median(self.response_times),
            'p95': self._percentile(self.response_times, 95),
            'p99': self._percentile(self.response_times, 99),
            'std_dev': statistics.stdev(self.response_times) if len(self.response_times) > 1 else 0,
            'error_count': len(self.errors),
            'error_rate': len(self.errors) / (len(self.response_times) + len(self.errors)) if (len(self.response_times) + len(self.errors)) > 0 else 0
        }
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile"""
        sorted_data = sorted(data)
        index = int((percentile / 100) * len(sorted_data))
        return sorted_data[min(index, len(sorted_data) - 1)]


class PerformanceTestCase(unittest.TestCase):
    """Base class for performance tests"""
    
    def setUp(self):
        """Set up performance test"""
        self.config = PERF_CONFIG.copy()
        self.monitor = PerformanceMonitor()
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
        # Check server availability
        try:
            response = self.session.get(f"{self.config['server_url']}/health", timeout=10)
            if response.status_code != 200:
                self.skipTest(f"Server not available: {response.status_code}")
        except requests.exceptions.RequestException as e:
            self.skipTest(f"Server not accessible: {e}")
    
    def tearDown(self):
        """Clean up after performance test"""
        if hasattr(self, 'session'):
            self.session.close()


class TestResponseTimeBenchmarks(PerformanceTestCase):
    """Benchmark response times for different operations"""
    
    def test_context_retrieval_benchmark(self):
        """Benchmark context retrieval performance"""
        collector = ResponseTimeCollector()
        self.monitor.start_monitoring()
        
        print(f"\n🏃 Benchmarking context retrieval with {len(self.config['benchmark_queries'])} queries...")
        
        for query in self.config['benchmark_queries']:
            start_time = time.time()
            
            try:
                payload = {
                    'query': query,
                    'codebase_name': self.config['test_codebase'],
                    'n_results': 5
                }
                response = self.session.post(
                    f"{self.config['server_url']}/tools/get_optimized_context",
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 200:
                    response_time = time.time() - start_time
                    collector.add_response_time(response_time)
                    self.monitor.update_peak_memory()
                else:
                    collector.add_error(f"HTTP {response.status_code}")
                    
            except Exception as e:
                collector.add_error(str(e))
        
        # Analyze results
        stats = collector.get_statistics()
        perf_stats = self.monitor.get_stats()
        
        print(f"📊 Response Time Statistics:")
        print(f"   Mean: {stats['mean']:.3f}s")
        print(f"   Median: {stats['median']:.3f}s") 
        print(f"   P95: {stats['p95']:.3f}s")
        print(f"   P99: {stats['p99']:.3f}s")
        print(f"   Error Rate: {stats['error_rate']:.1%}")
        print(f"   Memory Usage: {perf_stats['peak_memory_mb']:.1f} MB")
        
        # Performance assertions
        self.assertLess(stats['mean'], self.config['performance_thresholds']['avg_response_time'])
        self.assertLess(stats['p95'], self.config['performance_thresholds']['p95_response_time'])
        self.assertLess(stats['error_rate'], self.config['performance_thresholds']['error_rate'])
        self.assertLess(perf_stats['peak_memory_mb'], self.config['performance_thresholds']['memory_usage_mb'])
    
    def test_search_vs_context_performance(self):
        """Compare performance of different endpoints"""
        endpoints = [
            ('/tools/get_optimized_context', 'Optimized Context'),
            ('/tools/enhanced_search', 'Enhanced Search'),
            ('/tools/search_code', 'Legacy Search')
        ]
        
        results = {}
        
        for endpoint, name in endpoints:
            collector = ResponseTimeCollector()
            
            for query in self.config['benchmark_queries'][:3]:  # Use subset for comparison
                start_time = time.time()
                
                try:
                    payload = {
                        'query': query,
                        'codebase_name': self.config['test_codebase'],
                        'n_results': 5
                    }
                    response = self.session.post(
                        f"{self.config['server_url']}{endpoint}",
                        json=payload,
                        timeout=30
                    )
                    
                    if response.status_code == 200:
                        response_time = time.time() - start_time
                        collector.add_response_time(response_time)
                    else:
                        collector.add_error(f"HTTP {response.status_code}")
                        
                except Exception as e:
                    collector.add_error(str(e))
            
            results[name] = collector.get_statistics()
        
        # Print comparison
        print(f"\n📊 Endpoint Performance Comparison:")
        for name, stats in results.items():
            if 'mean' in stats:
                print(f"   {name:20} | Mean: {stats['mean']:.3f}s | P95: {stats['p95']:.3f}s | Errors: {stats['error_count']}")
        
        # Optimized context should be fastest or comparable
        if 'Optimized Context' in results and 'mean' in results['Optimized Context']:
            optimized_mean = results['Optimized Context']['mean']
            for name, stats in results.items():
                if name != 'Optimized Context' and 'mean' in stats:
                    # Optimized should be within 50% of other endpoints (or faster)
                    self.assertLessEqual(optimized_mean, stats['mean'] * 1.5)


class TestCachePerformance(PerformanceTestCase):
    """Test caching system performance"""
    
    def setUp(self):
        super().setUp()
        self.plugin = Tools()
        self.plugin.valves.code_analyzer_server_url = self.config['server_url']
        self.plugin.valves.current_codebase = self.config['test_codebase']
        self.plugin.valves.enable_caching = True
        self.plugin.__init__()  # Reinitialize to apply cache settings
    
    def test_cache_hit_performance(self):
        """Test cache hit vs miss performance"""
        if not self.plugin.cache:
            self.skipTest("Caching not enabled")
        
        query = "cache performance test query"
        
        # Measure cache miss (first request)
        miss_times = []
        for _ in range(3):  # Average over multiple requests
            start_time = time.time()
            result = asyncio.run(self.plugin.get_code_context(
                query=query,
                codebase_name=self.config['test_codebase']
            ))
            miss_time = time.time() - start_time
            miss_times.append(miss_time)
            
            # Clear cache for next miss test
            if hasattr(self.plugin.cache.memory_cache, 'cache'):
                self.plugin.cache.memory_cache.cache.clear()
        
        avg_miss_time = statistics.mean(miss_times)
        
        # Ensure cache is populated
        asyncio.run(self.plugin.get_code_context(
            query=query,
            codebase_name=self.config['test_codebase']
        ))
        
        # Measure cache hit
        hit_times = []
        for _ in range(10):  # More samples for cache hits
            start_time = time.time()
            result = asyncio.run(self.plugin.get_code_context(
                query=query,
                codebase_name=self.config['test_codebase']
            ))
            hit_time = time.time() - start_time
            hit_times.append(hit_time)
        
        avg_hit_time = statistics.mean(hit_times)
        improvement_ratio = avg_miss_time / avg_hit_time if avg_hit_time > 0 else 0
        
        print(f"\n⚡ Cache Performance Results:")
        print(f"   Cache Miss Time: {avg_miss_time:.3f}s")
        print(f"   Cache Hit Time: {avg_hit_time:.3f}s")
        print(f"   Improvement: {improvement_ratio:.1f}x faster")
        
        # Cache hits should be significantly faster
        self.assertLess(avg_hit_time, avg_miss_time)
        self.assertGreater(improvement_ratio, self.config['performance_thresholds']['cache_hit_improvement'])
    
    def test_cache_memory_efficiency(self):
        """Test cache memory usage efficiency"""
        if not self.plugin.cache:
            self.skipTest("Caching not enabled")
        
        self.monitor.start_monitoring()
        
        # Generate many different queries to fill cache
        queries = [f"test query {i}" for i in range(20)]
        
        for query in queries:
            asyncio.run(self.plugin.get_code_context(
                query=query,
                codebase_name=self.config['test_codebase']
            ))
            self.monitor.update_peak_memory()
        
        perf_stats = self.monitor.get_stats()
        cache_stats = self.plugin.cache.get_cache_stats()
        
        print(f"\n💾 Cache Memory Efficiency:")
        print(f"   Memory Increase: {perf_stats['memory_increase_mb']:.1f} MB")
        print(f"   Cache Entries: {cache_stats['memory_cache']['entries']}")
        print(f"   Cache Size: {cache_stats['memory_cache']['total_size_bytes'] / 1024:.1f} KB")
        print(f"   Hit Rate: {cache_stats['hit_rate']:.1%}")
        
        # Memory usage should be reasonable
        self.assertLess(perf_stats['memory_increase_mb'], 100)  # Less than 100MB increase


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='Performance Test Suite')
    parser.add_argument('--benchmark', action='store_true', help='Run benchmark tests only')
    parser.add_argument('--cache-test', action='store_true', help='Run cache performance tests only')
    parser.add_argument('--load-test', action='store_true', help='Run load tests only')
    
    args = parser.parse_args()
    
    # Create test suite based on arguments
    suite = unittest.TestSuite()
    
    if args.benchmark:
        suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestResponseTimeBenchmarks))
    elif args.cache_test:
        suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestCachePerformance))
    else:
        # Run all performance tests
        suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestResponseTimeBenchmarks))
        suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestCachePerformance))
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2, buffer=True)
    result = runner.run(suite)
    
    # Print summary
    print(f"\n{'='*60}")
    print("🏁 PERFORMANCE TEST SUMMARY")
    print(f"{'='*60}")
    print(f"Tests Run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.wasSuccessful():
        print("🎉 ALL PERFORMANCE TESTS PASSED!")
    else:
        print("⚠️ SOME PERFORMANCE TESTS FAILED")
        sys.exit(1)
