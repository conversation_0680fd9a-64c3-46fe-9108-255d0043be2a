
# Note we depend on NODE_ENV being set to dictate which of the env variables below get loaded at runtime. 
# See README for more details.

# Get this from https://mlab.com/home after you've logged in and created a database
MONGODB_URI=mongodb://<mlab_user>:<mlab_password>@<mlab_connection_url>

# This is standard running mongodb locally
MONGODB_URI_LOCAL=mongodb://localhost:27017/<database>

# Put lots of randomness in these
SESSION_SECRET=sessionsecret

# Facebook keys - register your app and get yours here: https://developers.facebook.com/
FACEBOOK_ID=facebookID
FACEBOOK_SECRET=facebooksecret

# SendGrid Login - create an account with SendGrid here: https://signup.sendgrid.com/
SENDGRID_USER=myusername
SENDGRID_PASSWORD=mysecurepassword

# Application Port - express server listens on this port (default 3000).
PORT=3000
