#!/usr/bin/env python3
"""
Compare Code Analyzer server direct vs OpenWebUI tool results
"""

import requests
import json
from datetime import datetime

# Configuration
CODE_ANALYZER_SERVER_URL = "http://home-ai-server.local:5002"
OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_code_analyzer_server_direct(query, codebase="utils", n_results=5):
    """Test Code Analyzer server directly"""
    print(f"🔧 Code Analyzer Server Direct: '{query}'")
    print("-" * 50)
    
    try:
        # Test search endpoint directly
        response = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/search_code",
            json={
                "query": query,
                "codebase_name": codebase,
                "n_results": n_results
            },
            timeout=30
        )
        
        print(f"📡 Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                
                # Extract results - Code Analyzer server returns result as a string
                if 'result' in data:
                    result_text = data['result']
                    print(f"📊 Code Analyzer server response: {len(result_text)} chars")

                    # Check if it contains actual code results
                    if "Found" in result_text and "results:" in result_text:
                        # Count results by looking for "Result X" patterns
                        import re
                        result_matches = re.findall(r'\*\*Result \d+\*\*', result_text)
                        result_count = len(result_matches)

                        print(f"✅ Code Analyzer server found {result_count} code results")

                        # Extract file information
                        file_matches = re.findall(r'File\*\*: `([^`]+)`', result_text)
                        if file_matches:
                            print(f"📁 Files found: {file_matches[:3]}")

                        # Extract code snippets
                        code_indicators = ["tmwdiag", "tmwcrypto", "tmwvrsn", "void TMWDEFS_GLOBAL", "Function:", "Lines:"]
                        found_indicators = [ind for ind in code_indicators if ind in result_text]

                        if found_indicators:
                            print(f"🔍 Code indicators: {found_indicators[:5]}")

                        return {
                            "success": True,
                            "count": result_count,
                            "result_text": result_text,
                            "files": file_matches,
                            "indicators": found_indicators
                        }
                    elif "No results found" in result_text:
                        print("📋 No results found for this query")
                        return {"success": True, "count": 0, "result_text": result_text}
                    else:
                        print("✅ Code Analyzer server response received")
                        return {"success": True, "count": 1, "result_text": result_text}
                else:
                    print(f"⚠️ No 'result' key in response: {list(data.keys())}")
                    return {"success": False, "error": "No result key"}
                    
            except json.JSONDecodeError:
                print(f"❌ Invalid JSON: {response.text[:200]}")
                return {"success": False, "error": "Invalid JSON"}
                
        else:
            print(f"❌ Error {response.status_code}: {response.text[:200]}")
            return {"success": False, "error": f"HTTP {response.status_code}"}
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return {"success": False, "error": str(e)}

def test_openwebui_tool(query):
    """Test through OpenWebUI tool"""
    print(f"\n🌐 OpenWebUI Tool: '{query}'")
    print("-" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    try:
        # First ensure codebase is selected
        select_response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "select codebase utils"}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 500
            },
            timeout=60
        )
        
        if select_response.status_code != 200:
            print(f"❌ Codebase selection failed: {select_response.status_code}")
            return {"success": False, "error": "Selection failed"}
        
        print("✅ Codebase selected")
        
        # Now run the search query
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": f"search for {query}"}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 1500
            },
            timeout=90
        )
        
        print(f"📡 Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            print(f"📊 Response length: {len(content)} chars")
            
            # Analyze content for code indicators
            code_indicators = [
                ".c:", ".h:", "line", "function", "Context", "tmwappl", 
                "#include", "int ", "char ", "void ", "return", "printf",
                "malloc", "free", "struct"
            ]
            
            found_indicators = [ind for ind in code_indicators if ind in content]
            
            # Check for file references
            file_references = []
            lines = content.split('\n')
            for line in lines:
                if any(term in line for term in ['.c:', '.h:', 'tmwappl', 'Context']):
                    file_references.append(line.strip())
            
            print(f"🔍 Code indicators found: {found_indicators[:5]}")
            if file_references:
                print(f"📁 File references: {file_references[:3]}")
            
            return {
                "success": True,
                "content": content,
                "length": len(content),
                "indicators": found_indicators,
                "file_references": file_references
            }
            
        else:
            print(f"❌ Error {response.status_code}: {response.text[:200]}")
            return {"success": False, "error": f"HTTP {response.status_code}"}
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return {"success": False, "error": str(e)}

def compare_results(query, code_analyzer_result, openwebui_result):
    """Compare and analyze the results"""
    print(f"\n📊 COMPARISON: '{query}'")
    print("=" * 60)
    
    # Code Analyzer Server Analysis
    if code_analyzer_result.get("success"):
        code_analyzer_count = code_analyzer_result.get("count", 0)
        files = code_analyzer_result.get("files", [])
        indicators = code_analyzer_result.get("indicators", [])

        print(f"🔧 Code Analyzer Server: {code_analyzer_count} results")

        if code_analyzer_count > 0:
            print("   ✅ Direct access to indexed code")
            if files:
                print(f"   ✅ Files found: {files[:3]}")
            if indicators:
                print(f"   ✅ Code indicators: {indicators[:3]}")
        else:
            print("   ❌ No results found")
    else:
        print(f"🔧 Code Analyzer Server: ❌ Failed - {code_analyzer_result.get('error')}")
    
    # OpenWebUI Analysis
    if openwebui_result.get("success"):
        indicators = openwebui_result.get("indicators", [])
        file_refs = openwebui_result.get("file_references", [])
        length = openwebui_result.get("length", 0)
        
        print(f"🌐 OpenWebUI: {length} chars, {len(indicators)} indicators")
        
        if len(indicators) > 3 and file_refs:
            print("   ✅ Tool processing code results")
            print("   ✅ File references found")
            print("   ✅ Code analysis working")
        elif len(indicators) > 0:
            print("   ⚠️ Some code indicators but limited context")
        else:
            print("   ❌ Generic response, no code context")
    else:
        print(f"🌐 OpenWebUI: ❌ Failed - {openwebui_result.get('error')}")
    
    # Comparison
    print(f"\n🎯 Analysis:")
    
    if code_analyzer_result.get("success") and code_analyzer_result.get("count", 0) > 0:
        if openwebui_result.get("success") and openwebui_result.get("indicators"):
            print("✅ BOTH WORKING: Code Analyzer has data, OpenWebUI processes it")
        else:
            print("⚠️ Code Analyzer HAS DATA but OpenWebUI not processing it properly")
    elif code_analyzer_result.get("success") and code_analyzer_result.get("count", 0) == 0:
        print("📋 Code Analyzer correctly reports no results for this query")
    else:
        print("❌ Code Analyzer server issue - can't retrieve data")

def main():
    """Main comparison test"""
    print("🔍 Code Analyzer Server vs OpenWebUI Tool Comparison")
    print("=" * 70)
    print(f"Time: {datetime.now()}")
    print(f"Code Analyzer Server: {CODE_ANALYZER_SERVER_URL}")
    print(f"OpenWebUI: {OPENWEBUI_URL}")
    
    # Test queries
    test_queries = [
        "memory allocation",
        "printf",
        "main function", 
        "include",
        "tmwappl",
        "struct",
        "file operations"
    ]
    
    results = []
    
    for query in test_queries:
        print(f"\n" + "="*80)
        print(f"🧪 TESTING QUERY: '{query}'")
        print("="*80)
        
        # Test Code Analyzer server directly
        code_analyzer_result = test_code_analyzer_server_direct(query)
        
        # Test through OpenWebUI
        openwebui_result = test_openwebui_tool(query)
        
        # Compare results
        compare_results(query, code_analyzer_result, openwebui_result)
        
        results.append({
            "query": query,
            "ca_result": code_analyzer_result,
            "openwebui": openwebui_result
        })
    
    # Final summary
    print(f"\n" + "="*80)
    print("📋 FINAL SUMMARY")
    print("="*80)
    
    ca_successes = sum(1 for r in results if r["ca_result"].get("success") and r["ca_result"].get("count", 0) > 0)
    openwebui_successes = sum(1 for r in results if r["openwebui"].get("success") and r["openwebui"].get("indicators"))
    
    print(f"🔧 Code Analyzer Server: {ca_successes}/{len(test_queries)} queries returned results")
    print(f"🌐 OpenWebUI: {openwebui_successes}/{len(test_queries)} queries showed code indicators")
    
    if ca_successes > 0 and openwebui_successes > 0:
        print(f"\n🎉 BOTH SYSTEMS WORKING!")
        print("✅ Code Analyzer server has indexed code data")
        print("✅ OpenWebUI tool processes and presents it")
    elif ca_successes > 0:
        print(f"\n⚠️ Code Analyzer SERVER HAS DATA but OpenWebUI tool issues")
        print("🔧 Check tool routing and processing logic")
    else:
        print(f"\n❌ Code Analyzer SERVER DATA ISSUES")
        print("🔧 Check codebase indexing and search functionality")

if __name__ == "__main__":
    main()
