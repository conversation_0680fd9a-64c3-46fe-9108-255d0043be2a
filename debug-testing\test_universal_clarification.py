#!/usr/bin/env python3
"""
Test the universal chunk clarification fix
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_universal_clarification():
    """Test that ALL statistics queries now get clarification"""
    print("🧪 Testing Universal Chunk Clarification Fix")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test all the problematic queries from before
    test_queries = [
        "show details about the utils code base",
        "what are the metrics for utils?", 
        "get statistics for utils codebase",
        "show me utils codebase stats",
        "utils statistics",
        "stats for utils",
    ]
    
    results = []
    
    for query in test_queries:
        print(f"\n🔍 Testing: '{query}'")
        print("-" * 40)
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": f"select codebase utils\n{query}"}],
                    "tool_ids": ["code_analyzer_tool"],
                    "stream": False,
                    "max_tokens": 1200
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                print(f"✅ Response received ({len(content)} chars)")
                
                # Check for clarification
                clarification_indicators = [
                    "text segments created by splitting source files",
                    "vector search and embedding", 
                    "chunk count (479) represents text segments",
                    "not the number of actual files",
                    "divided into multiple chunks",
                    "search granularity",
                    "chunks are text segments",
                    "splitting source files"
                ]
                
                found_clarification = [ind for ind in clarification_indicators if ind.lower() in content.lower()]
                
                # Check for problematic interpretations
                problematic = [
                    "chunks.*indicates.*files",
                    "chunks.*number of.*files", 
                    "479.*files"
                ]
                
                import re
                issues = []
                for phrase in problematic:
                    if re.search(phrase, content, re.IGNORECASE):
                        issues.append(phrase)
                
                # Evaluate
                has_clarification = len(found_clarification) > 0
                has_issues = len(issues) > 0
                
                if has_clarification and not has_issues:
                    print("🎉 PERFECT: Has clarification, no issues")
                    status = "perfect"
                elif has_clarification and has_issues:
                    print("✅ GOOD: Has clarification but still some issues")
                    status = "good"
                elif not has_clarification and not has_issues:
                    print("⚠️ NEUTRAL: No clarification but no issues either")
                    status = "neutral"
                else:
                    print("❌ POOR: No clarification and has issues")
                    status = "poor"
                
                if found_clarification:
                    print(f"   Clarification found: {found_clarification[:2]}")
                if issues:
                    print(f"   Issues found: {issues}")
                
                results.append({
                    "query": query,
                    "status": status,
                    "has_clarification": has_clarification,
                    "has_issues": has_issues,
                    "content_length": len(content)
                })
                
            else:
                print(f"❌ HTTP {response.status_code}")
                results.append({"query": query, "status": "error"})
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append({"query": query, "status": "error"})
    
    return results

def analyze_universal_results(results):
    """Analyze the universal fix results"""
    print(f"\n📊 UNIVERSAL FIX ANALYSIS")
    print("=" * 50)
    
    valid_results = [r for r in results if r.get("status") != "error"]
    total = len(valid_results)
    
    perfect = len([r for r in valid_results if r.get("status") == "perfect"])
    good = len([r for r in valid_results if r.get("status") == "good"])
    neutral = len([r for r in valid_results if r.get("status") == "neutral"])
    poor = len([r for r in valid_results if r.get("status") == "poor"])
    
    has_clarification = len([r for r in valid_results if r.get("has_clarification")])
    has_issues = len([r for r in valid_results if r.get("has_issues")])
    
    print(f"📈 Status Distribution:")
    print(f"  🎉 Perfect: {perfect}/{total} ({perfect/total*100:.1f}%)")
    print(f"  ✅ Good: {good}/{total} ({good/total*100:.1f}%)")
    print(f"  ⚠️ Neutral: {neutral}/{total} ({neutral/total*100:.1f}%)")
    print(f"  ❌ Poor: {poor}/{total} ({poor/total*100:.1f}%)")
    
    print(f"\n📋 Key Metrics:")
    print(f"  ✅ Has clarification: {has_clarification}/{total} ({has_clarification/total*100:.1f}%)")
    print(f"  ⚠️ Still has issues: {has_issues}/{total} ({has_issues/total*100:.1f}%)")
    
    # Overall assessment
    success_rate = (perfect + good) / total * 100 if total > 0 else 0
    clarification_rate = has_clarification / total * 100 if total > 0 else 0
    
    print(f"\n🎯 OVERALL ASSESSMENT:")
    print(f"Success rate: {success_rate:.1f}%")
    print(f"Clarification rate: {clarification_rate:.1f}%")
    
    if clarification_rate >= 90:
        print("🎉 EXCELLENT: Universal fix working!")
        print("✅ Consistent chunk clarification across all queries")
        print("✅ Users will understand chunks ≠ files")
    elif clarification_rate >= 70:
        print("👍 GOOD: Major improvement achieved")
        print("✅ Most queries now have clarification")
        print("⚠️ Some edge cases may remain")
    elif clarification_rate >= 50:
        print("⚠️ PARTIAL: Some improvement but inconsistent")
        print("🔧 May need additional fixes")
    else:
        print("❌ ISSUE: Universal fix not working")
        print("🔧 Need to investigate tool update")

def main():
    """Main test function"""
    print("🔧 Universal Chunk Clarification Test")
    print("=" * 70)
    print("Testing that ALL statistics queries now get chunk clarification")
    
    # Test the universal fix
    results = test_universal_clarification()
    
    # Analyze results
    analyze_universal_results(results)
    
    print(f"\n🎯 Expected Outcome:")
    print("ALL statistics queries should now include clarification about chunks vs files")
    print("This should eliminate user confusion about what 'chunks' means")

if __name__ == "__main__":
    main()
