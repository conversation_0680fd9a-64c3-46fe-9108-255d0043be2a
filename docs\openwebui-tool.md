🛠️ Tools & Functions
Imagine you've just stumbled upon Open WebUI, or maybe you're already using it, but you're a bit lost with all the talk about "Tools", "Functions", and "Pipelines". Everything sounds like some mysterious tech jargon, right? No worries! Let's break it down piece by piece, super clearly, step by step. By the end of this, you'll have a solid understanding of what these terms mean, how they work, and why know it's not as complicated as it seems.

TL;DR
Tools extend the abilities of LLMs, allowing them to collect real-world, real-time data like weather, stock prices, etc.
Functions extend the capabilities of the Open WebUI itself, enabling you to add new AI model support (like Anthropic or Vertex AI) or improve usability (like creating custom buttons or filters).
Pipelines are more for advanced users who want to transform Open WebUI features into API-compatible workflows—mainly for offloading heavy processing.
Getting started with Tools and Functions is easy because everything’s already built into the core system! You just click a button and import these features directly from the community, so there’s no coding or deep technical work required.

What are "Tools" and "Functions"?
Let's start by thinking of Open WebUI as a "base" software that can do many tasks related to using Large Language Models (LLMs). But sometimes, you need extra features or abilities that don't come out of the box—this is where tools and functions come into play.

Tools
Tools are an exciting feature because they allow LLMs to do more than just process text. They provide external abilities that LLMs wouldn't otherwise have on their own.

Example of a Tool:
Imagine you're chatting with an LLM and you want it to give you the latest weather update or stock prices in real time. Normally, the LLM can't do that because it's just working on pre-trained knowledge. This is where tools come in!

Tools are like c that the LLM can use to gather real-world, real-time data. So, with a "weather tool" enabled, the model can go out on the internet, gather live weather data, and display it in your conversation.
Tools are essentially abilities you’re giving your AI to help it interact with the outside world. By adding these, the LLM can "grab" useful information or perform specialized tasks based on the context of the conversation.

Examples of Tools (extending LLM’s abilities):
Real-time weather predictions 🛰️.
Stock price retrievers 📈.
Flight tracking information ✈️.
Functions
While tools are used by the AI during a conversation, functions help extend or customize the capabilities of Open WebUI itself. Imagine tools are like adding new ingredients to a dish, and functions are the process you use to control the kitchen! 🚪

Let's break that down:
Functions give you the ability to tweak or add features inside Open WebUI itself.
You’re not giving new abilities to the LLM, but instead, you’re extending the interface, behavior, or logic of the platform itself!
For instance, maybe you want to:

Add a new AI model like Anthropic to the WebUI.
Create a custom button in your toolbar that performs a frequently used command.
Implement a better filter function that catches inappropriate or spammy messages from the incoming text.
Without functions, these would all be out of reach. But with this framework in Open WebUI, you can easily extend these features!

Where to Find and Manage Functions
Functions are not located in the same place as Tools.

Tools are about model access and live in your Workspace tabs (where you add models, prompts, and knowledge collections). They can be added by users if granted permissions.
Functions are about platform customization and are found in the Admin Panel.
They are configured and managed only by admins who want to extend the platform interface or behavior for all users.
Summary of Differences:
Tools are things that allow LLMs to do more things outside their default abilities (such as retrieving live info or performing custom tasks based on external data).
Functions help the WebUI itself do more things, like adding new AI models or creating smarter ways to filter data.
Both are designed to be pluggable, meaning you can easily import them into your system with just one click from the community! 🎉 You won’t have to spend hours coding or tinkering with them.

What are Pipelines?
And then, we have Pipelines… Here’s where things start to sound pretty technical—but don’t despair.

Pipelines are part of an Open WebUI initiative focused on making every piece of the WebUI inter-operable with OpenAI’s API system. Essentially, they extend what both Tools and Functions can already do, but now with even more flexibility. They allow you to turn features into OpenAI API-compatible formats. 🧠

But here’s the thing…
You likely won't need pipelines unless you're dealing with super-advanced setups.

Who are pipelines for? Typically, experts or people running more complicated use cases.
When do you need them? If you're trying to offload processing from your primary Open WebUI instance to a different machine (so you don’t overload your primary system).
In most cases, as a beginner or even an intermediate user, you won’t have to worry about pipelines. Just focus on enjoying the benefits that tools and functions bring to your Open WebUI experience!

Want to Try? 🚀
Jump into Open WebUI, head over to the community section, and try importing a tool like weather updates or maybe adding a new feature to the toolbar with a function. Exploring these tools will show you how powerful and flexible Open WebUI can be!

🌟 There's always more to learn, so stay curious and keep experimenting!


What are Tools?
Tools are small Python scripts that add superpowers to your LLM. When enabled, they allow your chatbot to do amazing things — like search the web, scrape data, generate images, talk back using AI voices, and more.

Think of Tools as useful functions that your AI can use when chatting with you.

🚀 What Can Tools Help Me Do?
Here are just a few examples of what Tools let your AI assistant do:

🌍 Web Search: Get real-time answers by searching the internet.
🖼️ Image Generation: Create images from your prompts.
🔊 Voice Output: Generate AI voices using ElevenLabs.
Explore ready-to-use tools here:
🧰 Tools Showcase

📦 How to Install Tools
There are two easy ways to install Tools in Open WebUI:

Go to Community Tool Library
Choose a Tool, then click the Get button.
Enter your Open WebUI instance’s IP address or URL.
Click “Import to WebUI” — done!
🛑 Safety Tip: Never import a Tool you don’t recognize or trust. These are Python scripts and might run unsafe code.

🔧 How to Use Tools in Open WebUI
Once you've installed Tools (we’ll show you how below), here’s how to enable and use them:

You have two ways to enable a Tool for your model:

➕ Option 1: Enable from the Chat Window
While chatting, click the ➕ icon in the input area. You’ll see a list of available Tools — you can enable any of them on the fly for that session.

💡 Tip: Enabling a Tool gives the model permission to use it — but it may not use it unless it's useful for the task.

✏️ Option 2: Enable by Default (Recommended for Frequent Use)
Go to: Workspace ➡️ Models
Choose the model you’re using (like GPT-4 or LLaMa2) and click the ✏️ edit icon.
Scroll down to the “Tools” section.
✅ Check the Tools you want your model to have access to by default.
Click Save.
This ensures the model always has these Tools ready to use whenever you chat with it.

You can also let your LLM auto-select the right Tools using the AutoTool Filter:

🔗 AutoTool Filter

🎯 Note: Even when using AutoTool, you still need to enable your Tools using Option 2.

✅ And that’s it — your LLM is now Tool-powered! You're ready to supercharge your chats with web search, image generation, voice output, and more.

🧠 Choosing How Tools Are Used: Default vs Native
Once Tools are enabled for your model, Open WebUI gives you two different ways to let your LLM use them in conversations.

You can decide how the model should call Tools by choosing between:

🟡 Default Mode (Prompt-based)
🟢 Native Mode (Built-in function calling)
Let’s break it down:

🟡 Default Mode (Prompt-based Tool Triggering)
This is the default setting in Open WebUI.

Here, your LLM doesn’t need to natively support function calling. Instead, we guide the model using smart tool selection prompt template to select and use a Tool.

✅ Works with almost any model
✅ Great way to unlock Tools with basic or local models
❗ Not as reliable or flexible as Native Mode when chaining tools

🟢 Native Mode (Function Calling Built-In)
If your model does support “native” function calling (like GPT-4o or GPT-3.5-turbo-1106), you can use this powerful mode to let the LLM decide — in real time — when and how to call multiple Tools during a single chat message.

✅ Fast, accurate, and can chain multiple Tools in one response
✅ The most natural and advanced experience
❗ Requires a model that actually supports native function calling

✳️ How to Switch Between Modes
Want to enable native function calling in your chats? Here's how:

Chat Controls

Open the chat window with your model.
Click ⚙️ Chat Controls > Advanced Params.
Look for the Function Calling setting and switch it from Default → Native
That’s it! Your chat is now using true native Tool support (as long as the model supports it).

➡️ We recommend using GPT-4o or another OpenAI model for the best native function-calling experience.
🔎 Some local models may claim support, but often struggle with accurate or complex Tool usage.

💡 Summary:

Mode	Who it’s for	Pros	Cons
Default	Any model	Broad compatibility, safer, flexible	May be less accurate or slower
Native	GPT-4o, etc.	Fast, smart, excellent tool chaining	Needs proper function call support
Choose the one that works best for your setup — and remember, you can always switch on the fly via Chat Controls.

👏 And that's it — your LLM now knows how and when to use Tools, intelligently.

🧠 Summary
Tools are add-ons that help your AI model do much more than just chat. From answering real-time questions to generating images or speaking out loud — Tools bring your AI to life.

Visit: https://openwebui.com/tools to discover new Tools.
Install them manually or with one-click.
Enable them per model from Workspace ➡️ Models.
Use them in chat by clicking ➕
Now go make your AI waaaaay smarter 🤖✨



Tool Development
Writing A Custom Toolkit
Toolkits are defined in a single Python file, with a top level docstring with metadata and a Tools class.

Example Top-Level Docstring
"""
title: String Inverse
author: Your Name
author_url: https://website.com
git_url: https://github.com/username/string-reverse.git
description: This tool calculates the inverse of a string
required_open_webui_version: 0.4.0
requirements: langchain-openai, langgraph, ollama, langchain_ollama
version: 0.4.0
licence: MIT
"""

Tools Class
Tools have to be defined as methods within a class called Tools, with optional subclasses called Valves and UserValves, for example:

class Tools:
    def __init__(self):
        """Initialize the Tool."""
        self.valves = self.Valves()

    class Valves(BaseModel):
        api_key: str = Field("", description="Your API key here")

    def reverse_string(self, string: str) -> str:
        """
        Reverses the input string.
        :param string: The string to reverse
        """
        # example usage of valves
        if self.valves.api_key != "42":
            return "Wrong API key"
        return string[::-1] 

Type Hints
Each tool must have type hints for arguments. The types may also be nested, such as queries_and_docs: list[tuple[str, int]]. Those type hints are used to generate the JSON schema that is sent to the model. Tools without type hints will work with a lot less consistency.

Valves and UserValves - (optional, but HIGHLY encouraged)
Valves and UserValves are used for specifying customizable settings of the Tool, you can read more on the dedicated Valves & UserValves page.

Optional Arguments
Below is a list of optional arguments your tools can depend on:

__event_emitter__: Emit events (see following section)
__event_call__: Same as event emitter but can be used for user interactions
__user__: A dictionary with user information. It also contains the UserValves object in __user__["valves"].
__metadata__: Dictionary with chat metadata
__messages__: List of previous messages
__files__: Attached files
__model__: A dictionary with model information
Just add them as argument to any method of your Tool class just like __user__ in the example above.

Event Emitters
Event Emitters are used to add additional information to the chat interface. Similarly to Filter Outlets, Event Emitters are capable of appending content to the chat. Unlike Filter Outlets, they are not capable of stripping information. Additionally, emitters can be activated at any stage during the Tool.

There are two different types of Event Emitters:

If the model seems to be unable to call the tool, make sure it is enabled (either via the Model page or via the + sign next to the chat input field). You can also turn the Function Calling argument of the Advanced Params section of the Model page from Default to Native.

Status
This is used to add statuses to a message while it is performing steps. These can be done at any stage during the Tool. These statuses appear right above the message content. These are very useful for Tools that delay the LLM response or process large amounts of information. This allows you to inform users what is being processed in real-time.

await __event_emitter__(
            {
                "type": "status", # We set the type here
                "data": {"description": "Message that shows up in the chat", "done": False, "hidden": False}, 
                # Note done is False here indicating we are still emitting statuses
            }
        )


Example
Message
This type is used to append a message to the LLM at any stage in the Tool. This means that you can append messages, embed images, and even render web pages before, or after, or during the LLM response.

await __event_emitter__(
                    {
                        "type": "message", # We set the type here
                        "data": {"content": "This message will be appended to the chat."},
                        # Note that with message types we do NOT have to set a done condition
                    }
                )


Example
async def test_function(
        self, prompt: str, __user__: dict, __event_emitter__=None
    ) -> str:
        """
        This is a demo

        :param test: this is a test parameter
        """

        await __event_emitter__(
                    {
                        "type": "message", # We set the type here
                        "data": {"content": "This message will be appended to the chat."},
                        # Note that with message types we do NOT have to set a done condition
                    }
                )

        except Exception as e:
            await __event_emitter__(
                {
                    "type": "status",
                    "data": {"description": f"An error occured: {e}", "done": True},
                }
            )

            return f"Tell the user: {e}"


Citations
This type is used to provide citations or references in the chat. You can utilize it to specify the content, the source, and any relevant metadata. Below is an example of how to emit a citation event:

await __event_emitter__(
    {
        "type": "citation",
        "data": {
            "document": [content],
            "metadata": [
                {
                    "date_accessed": datetime.now().isoformat(),
                    "source": title,
                }
            ],
            "source": {"name": title, "url": url},
        },
    }
)

If you are sending multiple citations, you can iterate over citations and call the emitter multiple times. When implementing custom citations, ensure that you set self.citation = False in your Tools class __init__ method. Otherwise, the built-in citations will override the ones you have pushed in. For example:

def __init__(self):
    self.citation = False

Warning: if you set self.citation = True, this will replace any custom citations you send with the automatically generated return citation. By disabling it, you can fully manage your own citation references.

Example
class Tools:
    class UserValves(BaseModel):
        test: bool = Field(
            default=True, description="test"
        )

    def __init__(self):
        self.citation = False

async def test_function(
        self, prompt: str, __user__: dict, __event_emitter__=None
    ) -> str:
        """
        This is a demo that just creates a citation

        :param test: this is a test parameter
        """

        await __event_emitter__(
            {
                "type": "citation",
                "data": {
                    "document": ["This message will be appended to the chat as a citation when clicked into"],
                    "metadata": [
                        {
                            "date_accessed": datetime.now().isoformat(),
                            "source": title,
                        }
                    ],
                    "source": {"name": "Title of the content", "url": "http://link-to-citation"},
                },
            }
        )


External packages
In the Tools definition metadata you can specify custom packages. When you click Save the line will be parsed and pip install will be run on all requirements at once.

Keep in mind that as pip is used in the same process as Open WebUI, the UI will be completely unresponsive during the installation.

No measures are taken to handle package conflicts with Open WebUI's requirements. That means that specifying requirements can break Open WebUI if you're not careful. You might be able to work around this by specifying open-webui itself as a requirement.

Example
"""
title: myToolName
author: myName
funding_url: [any link here will be shown behind a `Heart` button for users to show their support to you]
version: 1.0.0
# the version is displayed in the UI to help users keep track of updates.
license: GPLv3
description: [recommended]
requirements: package1>=2.7.0,package2,package3
"""