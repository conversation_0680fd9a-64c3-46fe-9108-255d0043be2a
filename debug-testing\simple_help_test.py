#!/usr/bin/env python3
"""
Simple test for help intent detection
"""

# Test the intent detection logic directly
def test_help_intent_detection():
    """Test help intent detection logic"""
    
    test_commands = [
        # Should trigger help intent
        ("code analysis help", True),
        ("code tools help", True), 
        ("codebase help", True),
        ("help with code analysis", True),
        ("how to analyze code", True),
        ("help", True),
        ("what can you do", True),
        
        # Should NOT trigger help intent
        ("help me debug this function", False),
        ("I need help with Python", False),
        ("find memory allocation", False),
    ]
    
    print("🧪 Testing Help Intent Detection Logic")
    print("=" * 50)
    
    for command, should_be_help in test_commands:
        print(f"\n📋 Testing: '{command}'")
        print(f"   Expected: {'help' if should_be_help else 'not help'}")
        
        # Simulate the intent detection logic
        query_lower = command.lower()
        
        # Check specific help patterns
        specific_help = any(phrase in query_lower for phrase in [
            'code_analyzer_tools help', 'code analyzer tools help', 'code analysis help', 'codebase help',
            'help with code analysis', 'help with codebase', 'help with code tools',
            'code tools help', 'available code tools', 'code analysis guide',
            'how to analyze code', 'code analysis documentation', 'codebase documentation',
            'code search help', 'code context help'
        ])
        
        # Check generic help patterns
        generic_help = (
            query_lower.strip() in ['help', 'get help', 'show help', 'guide', 'documentation', 'instructions', 'tutorial', 'usage'] or
            query_lower.startswith('what can') or query_lower.startswith('how do i')
        )
        
        detected_as_help = specific_help or generic_help
        
        if detected_as_help:
            print("   ✅ DETECTED AS HELP")
        else:
            print("   ❌ NOT DETECTED AS HELP")
            
        # Check if result matches expectation
        if detected_as_help == should_be_help:
            print("   🎯 CORRECT")
        else:
            print("   ⚠️ INCORRECT - Logic needs adjustment")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    test_help_intent_detection()
