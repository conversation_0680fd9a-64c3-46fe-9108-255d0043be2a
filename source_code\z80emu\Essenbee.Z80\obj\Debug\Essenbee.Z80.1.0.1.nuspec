﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Essenbee.Z80</id>
    <version>1.0.1</version>
    <authors><PERSON><PERSON><PERSON></authors>
    <license type="file">LICENSE</license>
    <licenseUrl>https://aka.ms/deprecateLicenseUrl</licenseUrl>
    <projectUrl>https://github.com/essenbee/z80emu</projectUrl>
    <description>A Z80 emulator</description>
    <releaseNotes>A sample implementation using this code can be found in the repo. It is a ZX Spectrum 48 emulator (without sound).</releaseNotes>
    <tags>z80</tags>
    <repository type="Git" url="https://github.com/essenbee/z80emu" />
    <dependencies>
      <group targetFramework=".NETStandard2.0" />
    </dependencies>
    <contentFiles>
      <files include="any/netstandard2.0/.cr/images/CF3C03E9AEA956395291BE98C580C27F.png" buildAction="Content" />
      <files include="any/netstandard2.0/.cr/images/E5D9CCCCCD8B82BB1159F822CFE086F8.png" buildAction="Content" />
    </contentFiles>
  </metadata>
  <files>
    <file src="C:\home-repos\openwebui_rag_code_server\source_code\z80emu\Essenbee.Z80\bin\Debug\netstandard2.0\Essenbee.Z80.dll" target="lib\netstandard2.0\Essenbee.Z80.dll" />
    <file src="C:\home-repos\openwebui_rag_code_server\source_code\z80emu\Essenbee.Z80\.cr\images\CF3C03E9AEA956395291BE98C580C27F.png" target="content\.cr\images\CF3C03E9AEA956395291BE98C580C27F.png" />
    <file src="C:\home-repos\openwebui_rag_code_server\source_code\z80emu\Essenbee.Z80\.cr\images\CF3C03E9AEA956395291BE98C580C27F.png" target="contentFiles\any\netstandard2.0\.cr\images\CF3C03E9AEA956395291BE98C580C27F.png" />
    <file src="C:\home-repos\openwebui_rag_code_server\source_code\z80emu\Essenbee.Z80\.cr\images\E5D9CCCCCD8B82BB1159F822CFE086F8.png" target="content\.cr\images\E5D9CCCCCD8B82BB1159F822CFE086F8.png" />
    <file src="C:\home-repos\openwebui_rag_code_server\source_code\z80emu\Essenbee.Z80\.cr\images\E5D9CCCCCD8B82BB1159F822CFE086F8.png" target="contentFiles\any\netstandard2.0\.cr\images\E5D9CCCCCD8B82BB1159F822CFE086F8.png" />
    <file src="C:\home-repos\openwebui_rag_code_server\source_code\z80emu\LICENSE" target="LICENSE" />
  </files>
</package>