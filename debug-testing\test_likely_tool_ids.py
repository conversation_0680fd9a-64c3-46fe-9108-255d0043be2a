#!/usr/bin/env python3
"""
Test the most likely tool IDs based on inconsistencies found
"""

import requests

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_likely_tool_ids():
    """Test the most likely tool IDs based on the title and variations found"""
    print("🔍 Testing Most Likely Tool IDs")
    print("=" * 40)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Based on inconsistencies found in codebase
    likely_tool_ids = [
        "Code Analysis Tool",      # Exact title from file header
        "code_analysis_tool",      # Snake case version
        "code-analysis-tool",      # Kebab case version
        "codebase_analyzer",       # From quick_help_test.py (you mentioned this works in OpenWebUI)
        "code_analyzer_tools",     # From auto_tester
        "code_analyzer_tool",      # Singular version
    ]
    
    test_query = "select utils codebase"
    
    for tool_id in likely_tool_ids:
        print(f"\n🧪 Testing: \"{tool_id}\"")
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": test_query}],
                    "tool_ids": [tool_id],
                    "stream": False,
                    "max_tokens": 200
                },
                timeout=20
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                
                # Check for actual tool execution
                success_indicators = [
                    "Selected Enhanced Codebase: utils",
                    "Documents: 479",
                    "ready_enhanced",
                    "Enhanced Statistics",
                    "Status: ready_enhanced"
                ]
                
                if any(indicator in content for indicator in success_indicators):
                    print(f"   ✅ SUCCESS! Working tool ID: {tool_id}")
                    print(f"   Response: {content[:100]}...")
                    return tool_id
                else:
                    print(f"   ❌ Generic response")
                    print(f"   Preview: {content[:60]}...")
            else:
                print(f"   ❌ Failed: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Error: {str(e)[:50]}...")
    
    print("\n❌ None of the likely tool IDs worked")
    print("Need to check OpenWebUI interface for exact tool ID")
    return None

def show_inconsistencies():
    """Show the inconsistencies found in the codebase"""
    print("\n🔍 Tool ID Inconsistencies Found in Codebase:")
    print("=" * 50)
    
    inconsistencies = [
        ("quick_help_test.py", "codebase_analyzer"),
        ("openwebui_auto_tester.py", "code_analyzer_tools"),
        ("investigate_api_discrepancy.py", "code_analyzer_tools"),
        ("find_correct_tool_id.py", "multiple variations tested"),
        ("Tool file header", "Code Analysis Tool (title)"),
    ]
    
    for file, tool_id in inconsistencies:
        print(f"   📄 {file}: \"{tool_id}\"")
    
    print("\n💡 This explains why API calls don't work!")
    print("   Different files are using different tool IDs.")

def main():
    """Main function"""
    print("🔧 Fixing Tool ID Inconsistencies")
    print("=" * 70)
    print("You found the root cause: inconsistent tool IDs throughout codebase!\n")
    
    # Show inconsistencies
    show_inconsistencies()
    
    # Test likely IDs
    working_tool_id = test_likely_tool_ids()
    
    print("\n📊 RESULTS")
    print("=" * 20)
    if working_tool_id:
        print(f"✅ Found working tool ID: \"{working_tool_id}\"")
        print("\n🔧 Next steps:")
        print(f"1. Update all files to use: \"{working_tool_id}\"")
        print("2. Test auto-tester with correct tool ID")
        print("3. Verify API functionality is restored")
    else:
        print("❌ No working tool ID found via API")
        print("\n🔧 Next steps:")
        print("1. Check OpenWebUI interface for exact tool ID")
        print("2. Look in Workspace → Tools for the tool name/ID")
        print("3. Update all files to use the correct ID")

if __name__ == "__main__":
    main()
