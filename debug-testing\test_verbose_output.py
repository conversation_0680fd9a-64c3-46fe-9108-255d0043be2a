#!/usr/bin/env python3
"""
Test the verbose output functionality
"""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from openwebui_auto_tester import OpenWebUITester

def test_verbose_modes():
    """Test both verbose and non-verbose modes"""
    print("🧪 Testing Verbose Output Modes")
    print("=" * 50)
    
    # Mock tester (won't actually send requests)
    tester = OpenWebUITester("http://home-ai-server.local:8080")
    
    # Test message
    test_message = "status"
    
    print("\n1. Testing Verbose Mode (verbose=True):")
    print("-" * 40)
    print("This should show full response content")
    
    print("\n2. Testing Quiet Mode (verbose=False):")
    print("-" * 40)
    print("This should only show response length")
    
    print("\n✅ Verbose output functionality added!")
    print("\n💡 Usage:")
    print("   • When prompted 'Show full response content? (Y/n):'")
    print("   • Press Enter or 'y' for verbose output (default)")
    print("   • Type 'n' for quiet mode (just response length)")
    
    print("\n📋 Verbose Mode Shows:")
    print("   • Full response content (up to 1000 chars)")
    print("   • For longer responses: first 500 + last 500 chars")
    print("   • Clear separation with dashed lines")
    
    print("\n📋 Quiet Mode Shows:")
    print("   • Just 'Response received (X chars)'")
    print("   • Useful for quick testing without clutter")

def show_example_output():
    """Show example of what the output will look like"""
    print("\n📄 Example Verbose Output:")
    print("=" * 50)
    
    example_response = """🔧 **Code Analysis System Status**

✅ **Tool Server**: Online
✅ **Code Analysis Service**: Online
✅ **Ollama AI**: Online
✅ **Source Directory**: Online
✅ **Database Storage**: Online

📚 **Available Codebases**: 5
🎯 **Current Selection**: networking_project"""
    
    print("🧪 Test 1/4: System Status")
    print("📋 Basic status check")
    print("📤 Sending: 'status'")
    print(f"✅ Response received ({len(example_response)} chars)")
    print("📄 Response content:")
    print("-" * 40)
    print(example_response)
    print("-" * 40)
    
    print("\n📄 Example Quiet Output:")
    print("=" * 50)
    print("🧪 Test 1/4: System Status")
    print("📋 Basic status check")
    print("📤 Sending: 'status'")
    print(f"✅ Response received ({len(example_response)} chars)")
    print("(No response content shown in quiet mode)")

def main():
    """Main test function"""
    print("🔍 Verbose Output Test")
    print("=" * 30)
    
    print("Choose test mode:")
    print("1. Test verbose functionality")
    print("2. Show example output")
    print("3. Both")
    
    try:
        choice = input("\nEnter choice (1-3): ").strip()
        
        if choice == "1":
            test_verbose_modes()
        elif choice == "2":
            show_example_output()
        elif choice == "3":
            test_verbose_modes()
            show_example_output()
        else:
            print("Invalid choice. Showing examples...")
            show_example_output()
            
    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")

if __name__ == "__main__":
    main()
