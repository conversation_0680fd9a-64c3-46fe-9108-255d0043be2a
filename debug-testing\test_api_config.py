#!/usr/bin/env python3
"""
Test OpenWebUI API Configuration
Quick test to verify API key setup and endpoint permissions
"""

import requests
import os
import json
from typing import Dict, Optional, Any, Union, cast

OPENWEBUI_URL = "http://home-ai-server.local:8080"
DEFAULT_API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_api_configuration(api_key: Optional[str] = None) -> Dict[str, Union[bool, Dict[str, Any], list]]:
    """Test OpenWebUI API configuration"""
    
    print("🔐 OpenWebUI API Configuration Test")
    print("=" * 50)
    
    # Setup sessionn
    session = requests.Session()
    if api_key:
        session.headers.update({"Authorization": f"Bearer {api_key}"})
        print(f"🔑 Using API key: {api_key[:10]}...{api_key[-4:]}")
    else:
        print("🔑 No API key provided")
    
    results: Dict[str, Union[bool, Dict[str, Any], list]] = {
        "server_accessible": False,
        "api_key_valid": False,
        "endpoints": {},
        "recommendations": []
    }
    
    # Test 1: Server accessibility
    print(f"\n🌐 Testing server accessibility: {OPENWEBUI_URL}")
    try:
        response = session.get(f"{OPENWEBUI_URL}/health", timeout=10)
        results["server_accessible"] = True
        print(f"✅ Server accessible: {response.status_code}")
    except Exception as e:
        print(f"❌ Server not accessible: {e}")
        return results
    
    # Test 2: API endpoints
    endpoints_to_test = [
        ("/api/chat/completions", "Chat completions (required for testing)"),
        ("/api/models", "Models list"),
        ("/api/chat", "Chat endpoint"),
        ("/health", "Health check"),
        ("/api/v1/chat/completions", "Alternative chat endpoint"),
        ("/api/auth/signin", "Authentication endpoint"),
    ]
    
    print(f"\n🧪 Testing API endpoints:")
    print("-" * 40)
    
    accessible_count = 0
    for endpoint, description in endpoints_to_test:
        try:
            response = session.get(f"{OPENWEBUI_URL}{endpoint}", timeout=10)
            accessible = response.status_code not in [401, 403]
            
            if accessible:
                status_icon = "✅"
                accessible_count += 1
            elif response.status_code == 401:
                status_icon = "🔐"  # Authentication required
            elif response.status_code == 403:
                status_icon = "🚫"  # Forbidden
            else:
                status_icon = "⚠️"   # Other issue
            
            print(f"{status_icon} {endpoint}: {response.status_code} - {description}")
            endpoints_dict = cast(Dict[str, Any], results["endpoints"])
            endpoints_dict[endpoint] = {
                "status_code": response.status_code,
                "accessible": accessible,
                "description": description
            }
            
        except Exception as e:
            print(f"❌ {endpoint}: Error - {str(e)}")
            endpoints_dict = cast(Dict[str, Any], results["endpoints"])
            endpoints_dict[endpoint] = {
                "status_code": None,
                "accessible": False,
                "error": str(e)
            }
    
    # Test 3: Chat completions specifically (most important for testing)
    chat_endpoint = "/api/chat/completions"
    endpoints_dict = cast(Dict[str, Any], results["endpoints"])
    chat_accessible = endpoints_dict.get(chat_endpoint, {}).get("accessible", False)
    
    if chat_accessible:
        print("\n✅ Chat completions endpoint is accessible!")
        results["api_key_valid"] = True
        
        # Try a simple chat request
        print("🧪 Testing actual chat request...")
        try:
            test_payload = {
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "test"}],
                "stream": False,
                "max_tokens": 10
            }
            
            response = session.post(
                f"{OPENWEBUI_URL}{chat_endpoint}",
                json=test_payload,
                timeout=30
            )
            
            if response.status_code == 200:
                print("✅ Chat request successful!")
                results["chat_test"] = True
            else:
                print(f"⚠️ Chat request failed: {response.status_code}")
                print(f"   Response: {response.text[:200]}")
                results["chat_test"] = False
                
        except Exception as e:
            print(f"❌ Chat request error: {e}")
            results["chat_test"] = False
    
    # Generate recommendations
    print("\n📊 Analysis & Recommendations:")
    print("-" * 40)
    
    if not api_key:
        print("🔑 No API key provided")
        recommendations_list = cast(list, results["recommendations"])
        recommendations_list.append("Provide an API key for full testing")

    if accessible_count == 0:
        print("❌ No endpoints accessible - API key may be invalid or restrictions too strict")
        recommendations_list = cast(list, results["recommendations"])
        recommendations_list.extend([
            "Check if API key is valid",
            "Verify ENABLE_API_KEY=True in OpenWebUI config",
            "Consider disabling ENABLE_API_KEY_ENDPOINT_RESTRICTIONS temporarily"
        ])
    elif not chat_accessible:
        print("⚠️ Chat completions endpoint not accessible")
        recommendations_list = cast(list, results["recommendations"])
        recommendations_list.extend([
            "Add '/api/chat/completions' to API_KEY_ALLOWED_ENDPOINTS",
            "Or disable ENABLE_API_KEY_ENDPOINT_RESTRICTIONS",
            "Use browser testing as alternative"
        ])
    else:
        print("✅ Configuration looks good for automated testing!")
        recommendations_list = cast(list, results["recommendations"])
        recommendations_list.append("Ready for automated testing")
    
    # Configuration suggestions
    print("\n💡 Configuration Suggestions:")
    print("-" * 40)
    
    if accessible_count < len(endpoints_to_test) // 2:
        print("For testing, consider this OpenWebUI configuration:")
        print("```")
        print("ENABLE_API_KEY=True")
        print("ENABLE_API_KEY_ENDPOINT_RESTRICTIONS=False")
        print("JWT_EXPIRES_IN=-1")
        print("```")
    
    print("\n📋 Summary:")
    print(f"   • Server accessible: {'✅' if results['server_accessible'] else '❌'}")
    print(f"   • API key valid: {'✅' if results['api_key_valid'] else '❌'}")
    print(f"   • Endpoints accessible: {accessible_count}/{len(endpoints_to_test)}")
    print(f"   • Ready for testing: {'✅' if chat_accessible else '❌'}")
    
    return results

def main():
    """Main function"""
    print("🧪 OpenWebUI API Configuration Tester")
    print("=" * 50)
    
    # Get API key
    api_key = os.environ.get('OPENWEBUI_API_KEY')
    if not api_key:
        api_key = DEFAULT_API_KEY
        print(f"🔑 Using default API key: {api_key[:10]}...{api_key[-4:]}")

        use_different = input("Use a different API key? (y/N): ").strip().lower()
        if use_different in ['y', 'yes']:
            api_key_input = input("Enter OpenWebUI API key: ").strip()
            api_key = api_key_input if api_key_input else DEFAULT_API_KEY
    
    # Run tests
    results = test_api_configuration(api_key)
    
    # Save results
    results_file = "api_config_test_results.json"
    try:
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n💾 Results saved to: {results_file}")
    except Exception as e:
        print(f"⚠️ Could not save results: {e}")
    
    # Next steps
    print("\n🎯 Next Steps:")
    if results.get("api_key_valid", False):
        print("✅ Run full tool tests:")
        print("   python debug-testing/openwebui_auto_tester.py")
    else:
        print("🔧 Fix API configuration first, then:")
        print("   python debug-testing/openwebui_auto_tester.py")
        print("📖 See: OPENWEBUI_API_CONFIGURATION.md for detailed setup")

if __name__ == "__main__":
    main()
