#!/usr/bin/env python3
"""
Test how to access OpenWebUI's internal tool execution via API
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_openwebui_specific_parameters():
    """Test OpenWebUI-specific parameters that might trigger internal tool execution"""
    print("🔍 Testing OpenWebUI-Specific Tool Execution")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test different OpenWebUI-specific parameters
    test_configs = [
        {
            "name": "Force internal tools (use_tools)",
            "params": {
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "select utils codebase"}],
                "tool_ids": ["code_analyzer_tool"],
                "use_tools": True,
                "stream": False,
                "max_tokens": 300
            }
        },
        {
            "name": "OpenWebUI tools mode",
            "params": {
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "select utils codebase"}],
                "tool_ids": ["code_analyzer_tool"],
                "tools_mode": "internal",
                "stream": False,
                "max_tokens": 300
            }
        },
        {
            "name": "Disable function calling",
            "params": {
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "select utils codebase"}],
                "tool_ids": ["code_analyzer_tool"],
                "function_calling": False,
                "stream": False,
                "max_tokens": 300
            }
        },
        {
            "name": "OpenWebUI mode",
            "params": {
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "select utils codebase"}],
                "tool_ids": ["code_analyzer_tool"],
                "openwebui_mode": True,
                "stream": False,
                "max_tokens": 300
            }
        },
        {
            "name": "Legacy tools",
            "params": {
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "select utils codebase"}],
                "tool_ids": ["code_analyzer_tool"],
                "legacy_tools": True,
                "stream": False,
                "max_tokens": 300
            }
        }
    ]
    
    for config in test_configs:
        print(f"\n🧪 Testing: {config['name']}")
        print("-" * 40)
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json=config["params"],
                timeout=30
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                
                # Check for actual tool execution
                if any(indicator in content for indicator in ["Selected Enhanced Codebase", "Documents: 479", "ready_enhanced"]):
                    print(f"   ✅ SUCCESS: Tool executed!")
                    print(f"   Response: {content[:150]}...")
                    return config  # Found working configuration
                elif "utils codebase" in content.lower():
                    print(f"   ⚠️ Tool-aware but generic response")
                    print(f"   Response: {content[:100]}...")
                else:
                    print(f"   ❌ Completely generic response")
                    print(f"   Response: {content[:100]}...")
                    
            else:
                print(f"   ❌ Request failed")
                try:
                    error = response.json()
                    print(f"   Error: {error.get('detail', 'Unknown error')}")
                except:
                    print(f"   Error: {response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return None

def test_different_api_endpoints():
    """Test different API endpoints that might handle tools differently"""
    print("\n🔍 Testing Different API Endpoints")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test different endpoints
    endpoints_to_test = [
        "/api/chat/completions",
        "/api/v1/chat/completions", 
        "/api/chat",
        "/api/generate",
        "/api/tools/execute"
    ]
    
    test_payload = {
        "model": "llama3:latest",
        "messages": [{"role": "user", "content": "select utils codebase"}],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False,
        "max_tokens": 300
    }
    
    for endpoint in endpoints_to_test:
        print(f"\n🧪 Testing endpoint: {endpoint}")
        print("-" * 30)
        
        try:
            response = session.post(f"{OPENWEBUI_URL}{endpoint}", json=test_payload, timeout=30)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if "choices" in result:
                        content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                        if any(indicator in content for indicator in ["Selected Enhanced Codebase", "Documents: 479"]):
                            print(f"   ✅ SUCCESS: Tool executed!")
                            print(f"   Response: {content[:100]}...")
                        else:
                            print(f"   ⚠️ Generic response")
                            print(f"   Response: {content[:100]}...")
                    else:
                        print(f"   📋 Different response format: {list(result.keys())}")
                except json.JSONDecodeError:
                    print(f"   📋 Non-JSON response: {response.text[:100]}...")
            else:
                print(f"   ❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def check_web_interface_network_calls():
    """Analyze what the web interface might be doing differently"""
    print("\n🔍 Analysis: Web Interface vs API Differences")
    print("=" * 60)
    
    print("Based on our findings:")
    print("\n✅ Web Interface (Working):")
    print("   - Uses OpenWebUI's internal tool execution")
    print("   - Tools run in Python environment")
    print("   - Works with any LLM (including llama3)")
    print("   - Likely uses different execution path")
    
    print("\n❌ API Calls (Not Working):")
    print("   - Tries to use LLM's native function calling")
    print("   - llama3 doesn't support function calling")
    print("   - Falls back to text generation")
    print("   - Bypasses OpenWebUI's tool system")
    
    print("\n🎯 Potential Solutions:")
    print("   1. Find API parameter to force internal tool execution")
    print("   2. Use different API endpoint for tool execution")
    print("   3. Mimic web interface's exact request format")
    print("   4. Use a different model that supports function calling")

def main():
    """Main investigation function"""
    print("🔧 Internal Tool Execution Investigation")
    print("=" * 70)
    
    # Test OpenWebUI-specific parameters
    working_config = test_openwebui_specific_parameters()
    
    # Test different endpoints
    test_different_api_endpoints()
    
    # Analysis
    check_web_interface_network_calls()
    
    print("\n📋 FINAL SUMMARY")
    print("=" * 40)
    if working_config:
        print(f"✅ Found working configuration: {working_config['name']}")
        print("🎉 API access to tools is now possible!")
    else:
        print("❌ No working API configuration found")
        print("🔍 The discrepancy remains - web interface uses different execution path")
        print("\n💡 Recommendations:")
        print("   1. Use web interface for tool operations (primary use case)")
        print("   2. Consider using a function-calling capable model for API")
        print("   3. Investigate OpenWebUI source code for internal tool execution")

if __name__ == "__main__":
    main()
