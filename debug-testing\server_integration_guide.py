#!/usr/bin/env python3
"""
Complete Server Integration Guide for Dynamic Codebase Analysis
This shows how to integrate all 3 phases into your existing RAG server.
"""

import logging
from flask import Flask
from typing import Dict, Any

# Phase 1: Import the interfaces
from vector_db_interface import create_vector_db_interface
from rag_server_analysis_endpoints import integrate_with_existing_server

def integrate_dynamic_analysis_complete(
    app: Flask, 
    vector_db_client: Any, 
    db_type: str = 'chromadb',
    **db_kwargs
) -> 'CodebaseAnalysisService':
    """
    Complete integration of dynamic codebase analysis into existing RAG server.
    
    Args:
        app: Your existing Flask app
        vector_db_client: Your vector database client (ChromaDB, Pinecone, etc.)
        db_type: Type of vector database ('chromadb', 'pinecone', 'weaviate')
        **db_kwargs: Additional database-specific parameters
    
    Returns:
        CodebaseAnalysisService instance for further customization
    """
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Phase 1: Setup vector database interface
    vector_db_config = {
        'db_type': db_type,
        'client': vector_db_client,
        'logger': logger,
        **db_kwargs
    }
    
    logger.info(f"🔧 Initializing {db_type} interface for dynamic analysis")
    
    # Phase 2 & 3: Integrate analysis service
    analysis_service = integrate_with_existing_server(app, vector_db_config)
    
    logger.info("✅ Dynamic codebase analysis integration complete!")
    logger.info("📋 Available endpoints:")
    logger.info("   GET  /api/v1/codebases/<name>/all_chunks")
    logger.info("   POST /api/v1/codebases/<name>/analyze") 
    logger.info("   GET  /api/v1/codebases/<name>/patterns")
    logger.info("   POST /api/v1/enhance_query")
    logger.info("   GET  /analysis/health")
    logger.info("   GET  /analysis/status")
    
    return analysis_service

# Example integrations for different vector databases

def integrate_with_chromadb(app: Flask, chroma_client) -> 'CodebaseAnalysisService':
    """Integration example for ChromaDB"""
    return integrate_dynamic_analysis_complete(
        app=app,
        vector_db_client=chroma_client,
        db_type='chromadb'
    )

def integrate_with_pinecone(app: Flask, pinecone_client, index_name: str) -> 'CodebaseAnalysisService':
    """Integration example for Pinecone"""
    return integrate_dynamic_analysis_complete(
        app=app,
        vector_db_client=pinecone_client,
        db_type='pinecone',
        index_name=index_name
    )

def integrate_with_weaviate(app: Flask, weaviate_client, class_name: str) -> 'CodebaseAnalysisService':
    """Integration example for Weaviate"""
    return integrate_dynamic_analysis_complete(
        app=app,
        vector_db_client=weaviate_client,
        db_type='weaviate',
        class_name=class_name
    )

# Complete example server with dynamic analysis
def create_enhanced_rag_server():
    """
    Example of creating a complete RAG server with dynamic analysis.
    This shows how to modify your existing server.
    """
    
    app = Flask(__name__)
    
    # Your existing RAG endpoints would be here...
    @app.route('/health', methods=['GET'])
    def health():
        return {'status': 'healthy', 'service': 'Enhanced RAG Server'}
    
    # Example: Initialize your vector database
    # Replace this with your actual vector DB initialization
    try:
        # ChromaDB example
        import chromadb
        chroma_client = chromadb.Client()
        
        # Integrate dynamic analysis
        analysis_service = integrate_with_chromadb(app, chroma_client)
        
        print("✅ ChromaDB integration successful")
        
    except ImportError:
        print("⚠️ ChromaDB not available, using mock interface")
        
        # Mock interface for testing
        class MockVectorDB:
            def get_all_chunks_for_codebase(self, codebase_name, offset=0, limit=1000):
                return []
            def count_chunks_for_codebase(self, codebase_name):
                return 0
            def get_codebase_metadata(self, codebase_name):
                return {'name': codebase_name, 'status': 'mock'}
        
        analysis_service = integrate_dynamic_analysis_complete(
            app=app,
            vector_db_client=MockVectorDB(),
            db_type='chromadb'  # Will use mock
        )
    
    return app, analysis_service

# Migration guide for existing servers
MIGRATION_STEPS = """
🚀 MIGRATION GUIDE: Adding Dynamic Analysis to Existing RAG Server

Step 1: Install Dependencies
    pip install flask requests

Step 2: Add Files to Your Server
    - Copy vector_db_interface.py to your server directory
    - Copy rag_server_analysis_endpoints.py to your server directory

Step 3: Modify Your Main Server File
    
    # Add these imports at the top
    from server_integration_guide import integrate_dynamic_analysis_complete
    
    # In your server initialization code, add:
    analysis_service = integrate_dynamic_analysis_complete(
        app=your_flask_app,
        vector_db_client=your_vector_db_client,
        db_type='chromadb'  # or 'pinecone', 'weaviate'
    )

Step 4: Test the Integration
    
    # Test health endpoint
    curl http://localhost:5002/analysis/health
    
    # Test codebase analysis
    curl -X POST http://localhost:5002/api/v1/codebases/utils/analyze
    
    # Test query enhancement
    curl -X POST http://localhost:5002/api/v1/enhance_query \\
         -H "Content-Type: application/json" \\
         -d '{"query": "memory management", "codebase_name": "utils"}'

Step 5: Update OpenWebUI Tool
    
    The OpenWebUI tool will automatically detect and use the new endpoints.
    No changes needed to open_webui_code_analyzer_tool.py!

🎉 Your server now supports dynamic codebase analysis!
"""

def print_migration_guide():
    """Print the migration guide"""
    print(MIGRATION_STEPS)

# Test script for the integration
async def test_server_integration():
    """Test the complete server integration"""
    import requests
    import json
    
    base_url = "http://localhost:5002"
    
    print("🧪 TESTING SERVER INTEGRATION")
    print("=" * 50)
    
    # Test 1: Health check
    try:
        response = requests.get(f"{base_url}/analysis/health")
        if response.status_code == 200:
            print("✅ Analysis health check passed")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health check error: {e}")
    
    # Test 2: Analyze codebase
    try:
        response = requests.post(f"{base_url}/api/v1/codebases/utils/analyze")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Codebase analysis: {data.get('functions_discovered', 0)} functions")
        else:
            print(f"❌ Analysis failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Analysis error: {e}")
    
    # Test 3: Query enhancement
    try:
        response = requests.post(
            f"{base_url}/api/v1/enhance_query",
            json={"query": "memory management", "codebase_name": "utils"}
        )
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Query enhancement: {len(data.get('enhancements', []))} terms")
        else:
            print(f"❌ Enhancement failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Enhancement error: {e}")
    
    print("\n🎉 Integration testing complete!")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "guide":
        print_migration_guide()
    elif len(sys.argv) > 1 and sys.argv[1] == "test":
        import asyncio
        asyncio.run(test_server_integration())
    else:
        # Run example server
        app, analysis_service = create_enhanced_rag_server()
        print("\n🚀 Starting Enhanced RAG Server with Dynamic Analysis")
        print("📋 Test with: python server_integration_guide.py test")
        print("📖 Migration guide: python server_integration_guide.py guide")
        app.run(host='0.0.0.0', port=5002, debug=True)
