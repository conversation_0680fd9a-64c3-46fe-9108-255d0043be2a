/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2023 */
/*****************************************************************************/
/*                                                                           */
/* This file is the property of:                                             */
/*                                                                           */
/*                       Triangle MicroWorks, Inc.                           */
/*                      Raleigh, North Carolina USA                          */
/*                       www.TriangleMicroWorks.com                          */
/*                          (919) 870-6615                                   */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*                                                                           */
/*****************************************************************************/

/* file: smbdata.h
 * description: This file defines the interface between the Triangle
 *  MicroWorks, Inc. Server MB source code library and the target database.
 *  The default implementation calls functions in the MB Server Database
 *  simulator. These need to be repaced with code to interface with the
 *  device's database.
 *
 * notes: boolean data for Modbus (i.e. Discrete Inputs and Coils) is passed in a 
 *        packed byte format, with the value for the first requested item located
 *        in the lsb of the first byte and continuing into subsequent bytes; for 
 *        example, if coils 5 - 16 (0 based addressing) are to  be retrieved/stored
 *        then the data is passed as follows:
 *          _______________________________
 *         |c12|c11|c10|c09|c08|c07|c06|c05|    Byte 0
 *          -------------------------------
 *         | x | x | x | x |c16|c15|c14|c13|    Byte 1
 *          -------------------------------
 *        note that the data is not aligned on 8 bit boundries, even though the 
 *        target database may actually require this; the routine implementation
 *        should take care to retrieve and store the data appropriately; this is 
 *        done for efficiency, since the MB protocol always passes boolean data
 *        this way.
 */

#include "tmwscl/modbus/mbdiag.h"
#include "tmwscl/utils/tmwcnfg.h"
#include "tmwscl/utils/tmwtarg.h"

#include "tmwscl/modbus/smbdata.h"
#if TMWCNFG_USE_SIMULATED_DB
#include "tmwscl/modbus/smbsim.h"
#endif

#if TMWCNFG_USE_MANAGED_SCL
#undef TMWCNFG_USE_SIMULATED_DB
#define TMWCNFG_USE_SIMULATED_DB TMWDEFS_FALSE
#endif

#if TMWCNFG_USE_MANAGED_SCL
#include "tmwscl/.NET/TMW.SCL/SMBDataBaseWrapper.h"
#endif

/* function: smbdata_init */
void * TMWDEFS_GLOBAL smbdata_init(
  TMWSESN *pSession,
  void *pUserHandle)
{
#if TMWCNFG_USE_SIMULATED_DB
  TMWTARG_UNUSED_PARAM(pUserHandle);
  return(smbsim_init(pSession));
#elif TMWCNFG_USE_MANAGED_SCL
  return (SMBDatabaseWrapper_Init(pSession, pUserHandle));
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pUserHandle);
  TMWTARG_UNUSED_PARAM(pSession);
  TMWDIAG_ERROR("smbdata_init has not been implemented\n");
  return(TMWDEFS_NULL);
#endif
}

/* function: smbdata_close */
void TMWDEFS_GLOBAL smbdata_close(
  void *pHandle)
{
#if TMWCNFG_USE_SIMULATED_DB
  smbsim_close(pHandle);
#elif TMWCNFG_USE_MANAGED_SCL
  SMBDatabaseWrapper_Close(pHandle);
#else 
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
#endif
}

/* ___________________________________________________________________________*/

#if SMBDATA_SUPPORT_FC_R_D_INPUTS
/* function: smbdata_DiscreteInputsValidateRange */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_DiscreteInputsValidateRange(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT quantity)
{
#if TMWCNFG_USE_SIMULATED_DB
  return(smbsim_DiscreteInputsValidateRange(pHandle, startAddr, quantity));
#elif TMWCNFG_USE_MANAGED_SCL
  return (SMBDatabaseWrapper_DiscreteInputsValidateRange(pHandle, startAddr, quantity));
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(startAddr);
  TMWTARG_UNUSED_PARAM(quantity);
  return(TMWDEFS_FALSE);
#endif
}
#endif

#if SMBDATA_SUPPORT_FC_R_D_INPUTS
/* function: smbdata_getDiscreteInputs */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_getDiscreteInputs(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT quantity,
  TMWTYPES_UCHAR *pValueArray)
{
#if TMWCNFG_USE_SIMULATED_DB
  return(smbsim_dInputBlockRead(pHandle, startAddr, quantity, pValueArray));
#elif TMWCNFG_USE_MANAGED_SCL
  return (SMBDatabaseWrapper_dInputBlockRead(pHandle, startAddr, quantity, pValueArray));
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(startAddr);
  TMWTARG_UNUSED_PARAM(quantity);
  TMWTARG_UNUSED_PARAM(pValueArray);
  return(TMWDEFS_FALSE);
#endif
}
#endif

/* ___________________________________________________________________________*/

/* function: smbdata_CoilsValidateRange */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_CoilsValidateRange(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT quantity)
{
#if TMWCNFG_USE_SIMULATED_DB
  return(smbsim_CoilsValidateRange(pHandle, startAddr, quantity));
#elif TMWCNFG_USE_MANAGED_SCL
  return (SMBDatabaseWrapper_CoilsValidateRange(pHandle, startAddr, quantity));
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(startAddr);
  TMWTARG_UNUSED_PARAM(quantity);
  return(TMWDEFS_FALSE);
#endif
}

#if SMBDATA_SUPPORT_FC_R_COILS
/* function: smbdata_getCoils */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_getCoils(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT quantity,
  TMWTYPES_UCHAR *pValueArray)
{
#if TMWCNFG_USE_SIMULATED_DB
  return(smbsim_CoilBlockRead(pHandle, startAddr, quantity, pValueArray));
#elif TMWCNFG_USE_MANAGED_SCL
  return (SMBDatabaseWrapper_CoilBlockRead(pHandle, startAddr, quantity, pValueArray));
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(startAddr);
  TMWTARG_UNUSED_PARAM(quantity);
  TMWTARG_UNUSED_PARAM(pValueArray);
  return(TMWDEFS_FALSE);
#endif
}
#endif

#if SMBDATA_SUPPORT_FC_W_COIL || SMBDATA_SUPPORT_FC_W_MCOILS
/* function: smbdata_storeCoils */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_storeCoils(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT quantity,
  TMWTYPES_UCHAR *pValueArray)
{
#if TMWCNFG_USE_SIMULATED_DB
  return(smbsim_CoilBlockWrite(pHandle, startAddr, quantity, pValueArray));
#elif TMWCNFG_USE_MANAGED_SCL
  return (SMBDatabaseWrapper_CoilBlockWrite(pHandle, startAddr, quantity, pValueArray));
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(startAddr);
  TMWTARG_UNUSED_PARAM(quantity);
  TMWTARG_UNUSED_PARAM(pValueArray);
  return(TMWDEFS_FALSE);
#endif
}
#endif

/* ___________________________________________________________________________*/

#if SMBDATA_SUPPORT_FC_R_I_REGISTERS
/* function: smbdata_InputRegistersValidateRange */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_InputRegistersValidateRange(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT quantity)
{
#if TMWCNFG_USE_SIMULATED_DB
  return(smbsim_InputRegistersValidateRange(pHandle, startAddr, quantity));
#elif TMWCNFG_USE_MANAGED_SCL
  return (SMBDatabaseWrapper_InputRegistersValidateRange(pHandle, startAddr, quantity));
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(startAddr);
  TMWTARG_UNUSED_PARAM(quantity);
  return(TMWDEFS_FALSE);
#endif
}
#endif

#if SMBDATA_SUPPORT_FC_R_I_REGISTERS
/* function: smbdata_getInputRegisters */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_getInputRegisters(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT quantity,
  TMWTYPES_USHORT *pValueArray)
{
#if TMWCNFG_USE_SIMULATED_DB
  return(smbsim_iRegBlockRead(pHandle, startAddr, quantity, pValueArray));
#elif TMWCNFG_USE_MANAGED_SCL
  return (SMBDatabaseWrapper_iRegBlockRead(pHandle, startAddr, quantity, pValueArray));
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(startAddr);
  TMWTARG_UNUSED_PARAM(quantity);
  TMWTARG_UNUSED_PARAM(pValueArray);
  return(TMWDEFS_FALSE);
#endif
}
#endif

/* ___________________________________________________________________________*/
  
#if SMBDATA_SUPPORT_FC_R_H_REGISTERS || SMBDATA_SUPPORT_FC_W_REGISTER \
  || SMBDATA_SUPPORT_FC_W_MHREGISTERS || SMBDATA_SUPPORT_FC_RW_MREGISTERS
/* function: smbdata_HoldingRegistersValidateRange */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_HoldingRegistersValidateRange(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT quantity)
{
#if TMWCNFG_USE_SIMULATED_DB
  return(smbsim_HoldingRegistersValidateRange(pHandle, startAddr, quantity));
#elif TMWCNFG_USE_MANAGED_SCL
  return (SMBDatabaseWrapper_HoldingRegistersValidateRange(pHandle, startAddr, quantity));
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(startAddr);
  TMWTARG_UNUSED_PARAM(quantity);
  return(TMWDEFS_FALSE);
#endif
}
#endif

#if SMBDATA_SUPPORT_FC_W_REGISTER || SMBDATA_SUPPORT_FC_R_H_REGISTERS
/* function: smbdata_getHoldingRegisters */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_getHoldingRegisters(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT quantity,
  TMWTYPES_USHORT *pValueArray)
{
#if TMWCNFG_USE_SIMULATED_DB
  return(smbsim_hRegBlockRead(pHandle, startAddr, quantity, pValueArray));
#elif TMWCNFG_USE_MANAGED_SCL
  return (SMBDatabaseWrapper_hRegBlockRead(pHandle, startAddr, quantity, pValueArray));
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(startAddr);
  TMWTARG_UNUSED_PARAM(quantity);
  TMWTARG_UNUSED_PARAM(pValueArray);
  return(TMWDEFS_FALSE);
#endif
}
#endif

#if SMBDATA_SUPPORT_FC_W_REGISTER || SMBDATA_SUPPORT_FC_W_MHREGISTERS || SMBDATA_SUPPORT_FC_RW_MREGISTERS
/* function: smbdata_storeHoldingRegisters */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_storeHoldingRegisters(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT quantity,
  TMWTYPES_USHORT *pValueArray)
{
#if TMWCNFG_USE_SIMULATED_DB
  return(smbsim_hRegBlockWrite(pHandle, startAddr, quantity, pValueArray));
#elif TMWCNFG_USE_MANAGED_SCL
  return (SMBDatabaseWrapper_hRegBlockWrite(pHandle, startAddr, quantity, pValueArray));
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(startAddr);
  TMWTARG_UNUSED_PARAM(quantity);
  TMWTARG_UNUSED_PARAM(pValueArray);
  return(TMWDEFS_FALSE);
#endif
}
#endif


#if SMBDATA_SUPPORT_FC_R_EXCEPTION
/* function: smbdata_readExceptionStatus */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_readExceptionStatus(  
  void *pHandle,
  TMWTYPES_UCHAR *pStatus)
{
#if TMWCNFG_USE_SIMULATED_DB
  return(smbsim_readExceptionStatus(pHandle, pStatus));
#elif TMWCNFG_USE_MANAGED_SCL
  return (SMBDatabaseWrapper_readExceptionStatus(pHandle, pStatus));
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(pStatus);
  return(TMWDEFS_FALSE);
#endif
}
#endif
  
#if SMBDATA_SUPPORT_FC_DIAGNOSTICS
/* function: smbdata_diagSubFunctionSupported */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_diagSubFunctionSupported(  
  void *pHandle,
  TMWTYPES_USHORT subFunction)
{
#if TMWCNFG_USE_SIMULATED_DB
  TMWTARG_UNUSED_PARAM(pHandle);
  switch(subFunction)
  {
  case MBDEFS_DIAG_QUERY_DATA:
  case MBDEFS_DIAG_RESTART_COMM:
  case MBDEFS_DIAG_CHANGE_ASCII_DELIM:
  case MBDEFS_DIAG_FORCE_LISTEN_ONLY:   
  case MBDEFS_DIAG_CLEAR_BUS_COUNTERS:
  case MBDEFS_DIAG_CLEAR_OVERRUN_COUNT:
  case MBDEFS_DIAG_RETURN_DIAG_REGISTER:
  case MBDEFS_DIAG_GET_BUS_MSG_COUNT:
  case MBDEFS_DIAG_GET_BUS_CRC_ERR_COUNT:
  case MBDEFS_DIAG_GET_BUS_EXC_COUNT:
  case MBDEFS_DIAG_GET_SERVER_MSG_COUNT:
  case MBDEFS_DIAG_GET_SERVER_NR_COUNT:
  case MBDEFS_DIAG_GET_SERVER_NAK_COUNT:
  case MBDEFS_DIAG_GET_SERVER_BUSY_COUNT:
  case MBDEFS_DIAG_GET_BUS_OVERRUN_COUNT:
    return(TMWDEFS_TRUE);

    /* Put target code here to implement other subfunctions */

    /* other subfunctions are not currently supported */
    default:
      break;
  }
  return(TMWDEFS_FALSE);
#elif TMWCNFG_USE_MANAGED_SCL
  return (SMBDatabaseWrapper_diagSubFunctionSupported(pHandle, subFunction));
#else
  /* Put target code here */  
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(subFunction);
  return(TMWDEFS_FALSE);
#endif
}

/* function: smbdata_diagRestart */
void TMWDEFS_GLOBAL smbdata_diagRestart(  
  void *pHandle,
  TMWTYPES_USHORT requestData)
{
#if TMWCNFG_USE_SIMULATED_DB
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(requestData);
#elif TMWCNFG_USE_MANAGED_SCL
  return (SMBDatabaseWrapper_diagRestart(pHandle, requestData));
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(requestData);
#endif
}

/* function: smbdata_diagListenOnly */
void TMWDEFS_GLOBAL smbdata_diagListenOnly(  
  void *pHandle)
{
#if TMWCNFG_USE_SIMULATED_DB
  TMWTARG_UNUSED_PARAM(pHandle);
#elif TMWCNFG_USE_MANAGED_SCL
  SMBDatabaseWrapper_diagListenOnly(pHandle);
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
#endif
}

/* function: smbdata_diagClearCount */
void TMWDEFS_GLOBAL smbdata_diagClearCount(  
  void *pHandle,
  TMWTYPES_USHORT subFunction)
{
#if TMWCNFG_USE_SIMULATED_DB
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(subFunction);
#elif TMWCNFG_USE_MANAGED_SCL
  SMBDatabaseWrapper_diagClearCount(pHandle, subFunction);
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(subFunction);
#endif
}

/* function: smbdata_diagGetValue */
void TMWDEFS_GLOBAL smbdata_diagGetValue(  
  void *pHandle,
  TMWTYPES_USHORT subFunction,
  TMWTYPES_USHORT *pValue)
{
#if TMWCNFG_USE_SIMULATED_DB
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(subFunction);
  *pValue = 0;
#elif TMWCNFG_USE_MANAGED_SCL
  SMBDatabaseWrapper_diagGetValue(pHandle, subFunction, pValue);
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(subFunction);
  *pValue = 0;
#endif
}

/* function: smbdata_diagnostics */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_diagnostics(  
  void *pHandle,
  TMWTYPES_USHORT subFunction,
  TMWTYPES_UCHAR  requestSize,
  TMWTYPES_UCHAR *pRequestData,
  TMWTYPES_UCHAR *pResponseSize,
  TMWTYPES_UCHAR *pResponseData)
{
#if TMWCNFG_USE_SIMULATED_DB
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(subFunction);
  TMWTARG_UNUSED_PARAM(requestSize);
  TMWTARG_UNUSED_PARAM(pRequestData);
  TMWTARG_UNUSED_PARAM(pResponseSize);
  TMWTARG_UNUSED_PARAM(pResponseData);

  /* if(subFunction == MBDEFS_DIAG_RESTART_COMM)
   *   device should restart after returning from this function.
   */

  return(TMWDEFS_FALSE);
#elif TMWCNFG_USE_MANAGED_SCL
  return SMBDatabaseWrapper_diagnostics(pHandle, subFunction, requestSize, pRequestData, pResponseSize, pResponseData);
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(subFunction);
  TMWTARG_UNUSED_PARAM(requestSize);
  TMWTARG_UNUSED_PARAM(pRequestData);
  TMWTARG_UNUSED_PARAM(pResponseSize);
  TMWTARG_UNUSED_PARAM(pResponseData);

  /* if(subFunction == MBDEFS_DIAG_RESTART_COMM)
   *   device should restart after returning from this function.
   */

  return(TMWDEFS_FALSE);
#endif
}
#endif

#if SMBDATA_SUPPORT_FC_READ_DEV_ID
TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_getNextDeviceId( 
  void *pHandle,
  TMWTYPES_UCHAR objectId,
  TMWTYPES_UCHAR *pNextObjectId)
{
#if TMWCNFG_USE_SIMULATED_DB
  return(smbsim_getNextDeviceId(pHandle, objectId, pNextObjectId));
#elif TMWCNFG_USE_MANAGED_SCL
  return SMBDatabaseWrapper_getNextDeviceId(pHandle, objectId, pNextObjectId);
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(objectId);
  TMWTARG_UNUSED_PARAM(pNextObjectId);
  return(TMWDEFS_FALSE);
#endif
}

TMWTYPES_BOOL TMWDEFS_GLOBAL smbdata_readDeviceId( 
  void *pHandle,
  TMWTYPES_UCHAR  objectId,
  TMWTYPES_UCHAR *pLength,
  TMWTYPES_UCHAR *pData)
{
#if TMWCNFG_USE_SIMULATED_DB
  void *pPoint = smbsim_deviceIdGetPoint(pHandle, objectId);
  if(pPoint != TMWDEFS_NULL)
    if(smbsim_readDeviceId(pPoint, pLength, pData))
      return TMWDEFS_TRUE;
  return TMWDEFS_FALSE;
#elif TMWCNFG_USE_MANAGED_SCL
  return SMBDatabaseWrapper_readDeviceId(pHandle, objectId, pLength, pData);
#else
  /* Put target code here */
  TMWTARG_UNUSED_PARAM(pHandle);
  TMWTARG_UNUSED_PARAM(objectId);
  TMWTARG_UNUSED_PARAM(pData);
  TMWTARG_UNUSED_PARAM(pLength);

  return(TMWDEFS_FALSE);
#endif
}
#endif

