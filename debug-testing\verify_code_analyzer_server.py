#!/usr/bin/env python3
"""
Verify Code Analysis server is accessible (required for tool to work)
"""

import requests
import json

CODE_ANALYZER_SERVER_URL = "http://home-ai-server:5002"

def test_code_analyzer_server():
    """Test if Code Analysis server is accessible"""
    print("🔍 Testing Code Analysis Server Accessibility")
    print("=" * 50)
    print(f"Target: {CODE_ANALYZER_SERVER_URL}")
    
    # Test endpoints the tool needs
    endpoints = [
        ("/health", "Health check"),
        ("/status", "Status info"),
        ("/tools/list_codebases", "List codebases"),
    ]
    
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"{CODE_ANALYZER_SERVER_URL}{endpoint}", timeout=10)
            print(f"📡 {endpoint}: {response.status_code} - {description}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if endpoint == "/tools/list_codebases":
                        codebases = data.get('result', {}).get('codebases', [])
                        print(f"   Found codebases: {codebases}")
                    elif endpoint == "/health":
                        status = data.get('status', 'Unknown')
                        print(f"   Server status: {status}")
                except:
                    print(f"   Response: {response.text[:100]}...")
            else:
                print(f"   Error: {response.text[:100]}...")
                
        except Exception as e:
            print(f"❌ {endpoint}: {e}")
    
    # Test a simple search
    print(f"\n🔍 Testing Code Search:")
    try:
        response = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/search_code",
            json={"query": "memory", "codebase_name": "utils", "n_results": 1},
            timeout=30
        )
        print(f"📡 Search test: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            results = data.get('result', {}).get('results', [])
            print(f"   Found {len(results)} results")
        else:
            print(f"   Error: {response.text[:100]}...")
            
    except Exception as e:
        print(f"❌ Search test: {e}")

def main():
    """Main function"""
    print("🧪 Code Analysis Server Verification")
    print("=" * 40)
    print("This tests if your Code Analysis server is accessible.")
    print("The OpenWebUI tool needs this to work.")
    
    test_code_analyzer_server()
    
    print(f"\n📋 Summary:")
    print("If Code Analysis server is working:")
    print("✅ Tool should be able to connect")
    print("✅ Check tool installation in OpenWebUI")
    print("")
    print("If Code Analysis server is not working:")
    print("❌ Tool cannot function")
    print("❌ Start Code Analysis server first")

if __name__ == "__main__":
    main()
