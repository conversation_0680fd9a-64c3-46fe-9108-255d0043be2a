import { PrismaClient } from '@prisma/client';
import { OrderStatus } from '../types/enums';

const prisma = new PrismaClient();

export class OrderModel {
  async add(order: { userId: string; bookIds: string[] }) {
    return prisma.order.create({
      data: {
        userId: order.userId,
        bookIds: order.bookIds.join(','), // simplify for now
        status: OrderStatus.PENDING,
      },
    });
  }

  async list() {
    return prisma.order.findMany();
  }
}
