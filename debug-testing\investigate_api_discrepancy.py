#!/usr/bin/env python3
"""
Investigate why API calls return different results than web interface
"""

import requests
import json
import time

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_different_tool_ids():
    """Test different tool IDs to see which one works"""
    print("🔍 Testing Different Tool IDs")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test different tool IDs that might be in use
    tool_ids_to_test = [
        "code_analyzer_tool",
        "codebase_analyzer", 
        "code_analysis_tool",
        "Code Analysis Tool",
        "code-analyzer-tools",
        None  # Test without tool_ids
    ]
    
    test_query = "select utils codebase"
    
    for tool_id in tool_ids_to_test:
        print(f"\n🧪 Testing tool_id: {tool_id}")
        print("-" * 30)
        
        try:
            payload = {
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": test_query}],
                "stream": False,
                "max_tokens": 200
            }
            
            if tool_id is not None:
                payload["tool_ids"] = [tool_id]
            
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                
                # Check for success indicators
                success_indicators = ["Selected Enhanced Codebase", "Codebase utils selected", "ready_enhanced", "Documents: 479"]
                if any(indicator in content for indicator in success_indicators):
                    print(f"✅ SUCCESS with tool_id: {tool_id}")
                    print(f"   Response: {content[:150]}...")
                    return tool_id  # Found working tool ID
                else:
                    print(f"❌ Generic response with tool_id: {tool_id}")
                    print(f"   Response: {content[:100]}...")
            else:
                print(f"❌ Request failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error with {tool_id}: {e}")
        
        time.sleep(1)  # Brief pause between tests
    
    return None

def test_different_parameters():
    """Test different API parameters"""
    print("\n🔍 Testing Different API Parameters")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    test_query = "select utils codebase"
    
    # Test different parameter combinations
    test_configs = [
        {
            "name": "Standard API call",
            "params": {
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": test_query}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 500
            }
        },
        {
            "name": "Without tool_ids",
            "params": {
                "model": "llama3:latest", 
                "messages": [{"role": "user", "content": test_query}],
                "stream": False,
                "max_tokens": 500
            }
        },
        {
            "name": "With tools parameter",
            "params": {
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": test_query}],
                "tools": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 500
            }
        },
        {
            "name": "Different model format",
            "params": {
                "model": "llama3",
                "messages": [{"role": "user", "content": test_query}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 500
            }
        }
    ]
    
    for config in test_configs:
        print(f"\n🧪 Testing: {config['name']}")
        print("-" * 30)
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json=config["params"],
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                
                # Check for success indicators
                if any(indicator in content for indicator in ["Selected Enhanced Codebase", "Codebase utils selected"]):
                    print(f"✅ SUCCESS: {config['name']}")
                    print(f"   Response: {content[:150]}...")
                else:
                    print(f"❌ Generic response: {config['name']}")
                    print(f"   Response: {content[:100]}...")
            else:
                print(f"❌ Request failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Main investigation function"""
    print("🔧 API vs Interface Discrepancy Investigation")
    print("=" * 70)
    
    # Test 1: Different tool IDs
    working_tool_id = test_different_tool_ids()
    
    # Test 2: Different parameters
    test_different_parameters()
    
    # Summary
    print("\n📋 INVESTIGATION SUMMARY")
    print("=" * 40)
    if working_tool_id:
        print(f"✅ Found working tool ID: {working_tool_id}")
    else:
        print("❌ No working tool ID found via API")
        print("🔍 This confirms the discrepancy between web interface and API")
        print("\nPossible causes:")
        print("1. Web interface uses different authentication/session")
        print("2. Tool is configured differently for web vs API access")
        print("3. Different execution context or middleware")
        print("4. Tool ID mapping issue in API layer")

if __name__ == "__main__":
    main()
