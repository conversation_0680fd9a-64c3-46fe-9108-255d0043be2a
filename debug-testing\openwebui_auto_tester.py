#!/usr/bin/env python3
"""
OpenWebUI Automatic Test Script
Sends test prompts directly to your OpenWebUI server and captures responses
"""

import requests
import json
import time
import asyncio
import os
from typing import List, Dict, Optional, Any
from datetime import datetime

# Configuration
OPENWEBUI_URL = "http://home-ai-server.local:8080"
DEFAULT_API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"
TEST_RESULTS_FILE = f"openwebui_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

class OpenWebUITester:
    def __init__(self, base_url: str, api_key: Optional[str] = None):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.results: List[Dict[str, Any]] = []

        # Set up authentication if API key provided
        if api_key:
            self.session.headers.update({
                "Authorization": f"Bearer {api_key}"
            })
        
    def test_connection(self) -> bool:
        """Test if OpenWebUI server is accessible"""
        try:
            # Try health endpoint first (usually doesn't require auth)
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                print(f"✅ OpenWebUI server accessible: {response.status_code}")
                return True
            elif response.status_code == 404:
                # Health endpoint might not exist, try root
                response = self.session.get(f"{self.base_url}/", timeout=10)
                if response.status_code == 200:
                    print(f"✅ OpenWebUI server accessible: {response.status_code}")
                    return True

            print(f"⚠️ OpenWebUI server responded with: {response.status_code}")
            print("   This might be normal if authentication is required")
            return True  # Server is responding, auth issues will be handled later

        except Exception as e:
            print(f"❌ Cannot connect to OpenWebUI: {e}")
            return False

    def test_api_permissions(self) -> Dict[str, bool]:
        """Test API key permissions for different endpoints"""
        endpoints_to_test = [
            ("/api/chat/completions", "Chat completions (main testing endpoint)"),
            ("/api/models", "Models list"),
            ("/api/chat", "Chat endpoint"),
            ("/health", "Health check"),
            ("/api/v1/chat/completions", "Alternative chat endpoint")
        ]

        results = {}
        print("\n🔐 Testing API Key Permissions:")
        print("-" * 40)

        for endpoint, description in endpoints_to_test:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}", timeout=10)
                accessible = response.status_code not in [401, 403]
                status_icon = "✅" if accessible else "❌"
                print(f"{status_icon} {endpoint}: {response.status_code} - {description}")
                results[endpoint] = accessible
            except Exception as e:
                print(f"❌ {endpoint}: Error - {str(e)}")
                results[endpoint] = False

        return results

    def send_chat_message(self, message: str, model: str = "llama3:latest", verbose: bool = True) -> Optional[Dict]:
        """Send a chat message to OpenWebUI and get response"""
        try:
            # Use the chat/completions endpoint with tools parameter
            chat_url = f"{self.base_url}/api/chat/completions"

            payload = {
                "model": model,
                "messages": [
                    {
                        "role": "user",
                        "content": message
                    }
                ],
                "tool_ids": ["code_analyzer_tool"],  # Enable your Code Analysis tool (correct format!)
                "stream": False,
                "temperature": 0.7,
                "max_tokens": 2000
            }
            
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            print(f"📤 Sending: '{message[:50]}{'...' if len(message) > 50 else ''}'")

            # Send the request with tools enabled
            response = self.session.post(
                chat_url,
                json=payload,
                headers=headers,
                timeout=120  # Increased timeout for codebase operations
            )

            # If tool_ids parameter causes issues, try without tool_ids as fallback
            if response.status_code not in [200, 201]:
                print(f"⚠️ Request with tool_ids failed ({response.status_code}), trying without tool_ids...")
                # Remove tool_ids from payload for fallback
                fallback_payload = {k: v for k, v in payload.items() if k != "tool_ids"}
                response = self.session.post(
                    chat_url,
                    json=fallback_payload,
                    headers=headers,
                    timeout=120  # Increased timeout for fallback requests
                )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                print(f"✅ Response received ({len(content)} chars)")

                # Print the actual response content if verbose mode
                if verbose:
                    print("📄 Response content:")
                    print("-" * 40)
                    if len(content) > 1000:
                        # For very long responses, show first 500 and last 500 chars
                        print(content[:500])
                        print(f"\n... [truncated {len(content) - 1000} chars] ...\n")
                        print(content[-500:])
                    else:
                        print(content)
                    print("-" * 40)

                return {
                    "success": True,
                    "content": content,
                    "model": model,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                print(f"❌ HTTP {response.status_code}: {response.text}")
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}",
                    "response": response.text
                }
                
        except Exception as e:
            print(f"❌ Error: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def ensure_codebase_selected(self, model: str = "llama3:latest") -> bool:
        """Ensure a codebase is selected for code analysis tests"""
        print("\n🔧 Ensuring codebase is selected for code analysis...")

        try:
            # Try to select utils codebase (most comprehensive)
            response = self.send_chat_message("select codebase utils", model, verbose=False)

            if response and response.get("success"):
                print("✅ Codebase 'utils' selected successfully")
                return True
            else:
                print("⚠️ Failed to select utils, trying alternative...")

                # Try to select any available codebase
                list_response = self.send_chat_message("list codebases", model, verbose=False)
                if list_response and list_response.get("success"):
                    # Try to select the first available codebase
                    alt_response = self.send_chat_message("select codebase z80emu", model, verbose=False)
                    if alt_response and alt_response.get("success"):
                        print("✅ Alternative codebase 'z80emu' selected")
                        return True

                print("❌ Could not select any codebase")
                return False

        except Exception as e:
            print(f"❌ Error selecting codebase: {e}")
            return False

    def run_test_suite(self, test_prompts: List[Dict[str, str]], delay: float = 2.0, model: str = "llama3:latest", verbose: bool = True):
        """Run a suite of test prompts"""
        print("\n🚀 Starting OpenWebUI Test Suite")
        print(f"📍 Server: {self.base_url}")
        print(f"🤖 Model: {model}")
        print(f"📝 Tests: {len(test_prompts)}")
        print(f"⏱️  Delay: {delay}s between tests")
        print(f"📄 Verbose output: {'Yes' if verbose else 'No'}")
        print("=" * 60)
        
        for i, test in enumerate(test_prompts, 1):
            prompt = test["prompt"]
            category = test.get("category", "Unknown")
            description = test.get("description", "")
            
            print(f"\n🧪 Test {i}/{len(test_prompts)}: {category}")
            print(f"📋 {description}")
            
            # Send the prompt
            result = self.send_chat_message(prompt, model, verbose)
            
            # Store result
            test_result = {
                "test_number": i,
                "category": category,
                "description": description,
                "prompt": prompt,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
            self.results.append(test_result)
            
            # Brief delay between tests
            if i < len(test_prompts):
                time.sleep(delay)
        
        # Save results
        self.save_results()
        self.print_summary()
    
    def save_results(self):
        """Save test results to JSON file"""
        try:
            with open(TEST_RESULTS_FILE, 'w') as f:
                json.dump({
                    "test_session": {
                        "timestamp": datetime.now().isoformat(),
                        "server": self.base_url,
                        "total_tests": len(self.results)
                    },
                    "results": self.results
                }, f, indent=2)
            print(f"\n💾 Results saved to: {TEST_RESULTS_FILE}")
        except Exception as e:
            print(f"❌ Failed to save results: {e}")
    
    def print_summary(self):
        """Print test summary"""
        successful = sum(1 for r in self.results if r["result"].get("success", False))
        failed = len(self.results) - successful
        
        print("\n📊 TEST SUMMARY")
        print("=" * 40)
        print(f"✅ Successful: {successful}/{len(self.results)}")
        print(f"❌ Failed: {failed}/{len(self.results)}")
        print(f"📈 Success Rate: {(successful/len(self.results)*100):.1f}%")

        # Add explanation about generic responses
        print("\n💡 IMPORTANT NOTE:")
        print("   If you see generic responses (not tool-specific), this is expected!")
        print("   • API calls with llama3 don't trigger tool execution")
        print("   • Tool works perfectly in OpenWebUI web interface")
        print("   • Test the tool manually: 'select utils codebase', 'list codebases', etc.")

        if failed > 0:
            print("\n❌ Failed Tests:")
            for result in self.results:
                if not result["result"].get("success", False):
                    print(f"   • {result['description']}: {result['result'].get('error', 'Unknown error')}")

def get_all_test_categories() -> Dict[str, List[Dict[str, str]]]:
    """Define test prompts organized by category"""
    return {
        "System Status": [
            {"description": "Basic status check", "prompt": "status"},
            {"description": "Detailed status", "prompt": "detailed status"},
            {"description": "System health", "prompt": "check system health"},
            {"description": "Comprehensive status", "prompt": "comprehensive status"},
            {"description": "Working query test", "prompt": "select utils codebase"},
            {"description": "Another working query", "prompt": "list codebases"},
        ],

        "Management": [
            {"description": "List codebases", "prompt": "list codebases"},
            {"description": "Select codebase", "prompt": "select codebase utils"},
            {"description": "Get statistics", "prompt": "get stats for utils"},
            {"description": "Switch codebase", "prompt": "select codebase z80emu"},
            {"description": "Show available", "prompt": "show available codebases"},
        ],

        "Code Search": [
            {"description": "Memory allocation search", "prompt": "find memory allocation functions"},
            {"description": "Error handling search", "prompt": "search for error handling code"},
            {"description": "Networking search", "prompt": "show me socket programming examples"},
            {"description": "Security search", "prompt": "look for encryption functions"},
            {"description": "Threading search", "prompt": "find threading code"},
        ],

        "AI Analysis": [
            {"description": "Memory management analysis", "prompt": "How does memory management work in this codebase?"},
            {"description": "Security analysis", "prompt": "What security measures are in place?"},
            {"description": "Architecture analysis", "prompt": "Explain the overall architecture"},
            {"description": "Error handling analysis", "prompt": "How is error handling implemented?"},
            {"description": "Performance analysis", "prompt": "What are potential performance bottlenecks?"},
        ],

        "Language Search": [
            {"description": "C++ classes", "prompt": "show me all C++ classes"},
            {"description": "C functions", "prompt": "find C function definitions"},
            {"description": "Struct definitions", "prompt": "search for struct definitions"},
            {"description": "Template code", "prompt": "find template implementations"},
        ],

        "Format Test": [
            {"description": "Simple search (clean format)", "prompt": "find malloc"},
            {"description": "Basic function search", "prompt": "show printf"},
            {"description": "Complex analysis (detailed format)", "prompt": "Analyze the complete memory management strategy across all modules and explain how it prevents leaks while maintaining performance"},
            {"description": "Comparative analysis", "prompt": "Compare error handling approaches between different modules"},
        ],

        "Edge Case": [
            {"description": "Invalid codebase", "prompt": "select codebase nonexistent"},
            {"description": "Non-code query", "prompt": "what's the weather like?"},
            {"description": "Empty query", "prompt": ""},
            {"description": "Very long query", "prompt": "I need a comprehensive analysis of how this codebase handles concurrent access to shared resources, including mutex usage, lock-free algorithms, thread safety guarantees, potential race conditions, deadlock prevention mechanisms, and performance implications of the chosen synchronization strategies, with specific examples from the code and recommendations for improvements."},
        ],

        "Quick Test": [
            {"description": "Basic status", "prompt": "status"},
            {"description": "List codebases", "prompt": "list codebases"},
            {"description": "Select utils", "prompt": "select codebase utils"},
            {"description": "Simple search", "prompt": "find memory allocation"},
            {"description": "Basic AI question", "prompt": "How does this code work?"},
        ]
    }

def get_test_prompts(categories: Optional[List[str]] = None) -> List[Dict[str, str]]:
    """Get test prompts for specified categories or all if none specified"""
    all_categories = get_all_test_categories()

    if categories is None:
        categories = list(all_categories.keys())

    prompts = []
    for category in categories:
        if category in all_categories:
            for test in all_categories[category]:
                prompts.append({
                    "category": category,
                    "description": test["description"],
                    "prompt": test["prompt"]
                })

    return prompts

def preview_category_prompts(category_name: str, prompts: List[Dict[str, str]]):
    """Preview prompts for a specific category"""
    print(f"\n🔍 Preview: {category_name} ({len(prompts)} tests)")
    print("=" * 50)

    for i, test in enumerate(prompts, 1):
        print(f"{i:2d}. {test['description']}")
        print(f"    💬 \"{test['prompt']}\"")
        if i < len(prompts):
            print()

    print("=" * 50)

def select_test_categories() -> List[str]:
    """Allow user to select which test categories to run"""
    all_categories = get_all_test_categories()

    print("\n📋 Available test categories:")
    print("=" * 40)

    category_list = list(all_categories.keys())
    for i, category in enumerate(category_list, 1):
        count = len(all_categories[category])
        print(f"   {i:2d}. {category} ({count} tests)")

    print(f"   {len(category_list)+1:2d}. All categories ({sum(len(tests) for tests in all_categories.values())} tests)")

    print("\n💡 Selection options:")
    print("   • Single number: Run one category (e.g., '1')")
    print("   • Multiple numbers: Run multiple categories (e.g., '1,3,5')")
    print("   • Range: Run range of categories (e.g., '1-4')")
    print("   • 'all' or last number: Run all categories")
    print("   • 'preview X': Preview prompts for category X (e.g., 'preview 1')")

    while True:
        try:
            selection = input("\nSelect categories to test: ").strip()

            if not selection:
                print("❌ Please enter a selection")
                continue

            # Handle preview requests
            if selection.lower().startswith('preview '):
                try:
                    preview_num = int(selection.split()[1])
                    if 1 <= preview_num <= len(category_list):
                        category_name = category_list[preview_num - 1]
                        preview_category_prompts(category_name, all_categories[category_name])
                        continue
                    else:
                        print(f"❌ Invalid category number for preview: {preview_num}")
                        continue
                except (IndexError, ValueError):
                    print("❌ Invalid preview format. Use 'preview X' where X is a category number")
                    continue

            # Handle 'all' or the last number (all categories)
            if selection.lower() == 'all' or selection == str(len(category_list)+1):
                return category_list

            selected_indices: List[int] = []

            # Handle comma-separated values
            if ',' in selection:
                parts = selection.split(',')
                for part in parts:
                    part = part.strip()
                    if '-' in part:
                        # Handle range (e.g., "1-4")
                        start, end = map(int, part.split('-'))
                        selected_indices.extend(range(start, end + 1))
                    else:
                        selected_indices.append(int(part))
            elif '-' in selection:
                # Handle single range
                start, end = map(int, selection.split('-'))
                selected_indices.extend(range(start, end + 1))
            else:
                # Handle single number
                selected_indices.append(int(selection))

            # Validate indices and convert to category names
            selected_categories = []
            for idx in selected_indices:
                if 1 <= idx <= len(category_list):
                    category_name = category_list[idx - 1]
                    if category_name not in selected_categories:
                        selected_categories.append(category_name)
                else:
                    print(f"❌ Invalid category number: {idx}")
                    break
            else:
                # All indices were valid
                if selected_categories:
                    return selected_categories
                else:
                    print("❌ No valid categories selected")
                    continue

        except ValueError:
            print("❌ Invalid input. Please enter numbers, ranges, or 'all'")
        except KeyboardInterrupt:
            print("\n👋 Selection cancelled")
            return []

def main():
    """Main function"""
    print("🧪 OpenWebUI Code Analysis Tool Auto-Tester")
    print("=" * 50)

    # Important limitation notice
    print("⚠️  IMPORTANT: API Limitation with llama3")
    print("   This tester uses API calls which have limitations with llama3:")
    print("   • llama3 doesn't support native function calling")
    print("   • API calls will return generic responses (this is expected)")
    print("   • For actual tool functionality, use the OpenWebUI web interface")
    print("   • Tool works perfectly in web interface: select codebase, search code, etc.")
    print()

    # Check if API key is needed
    print("ℹ️  OpenWebUI may require authentication for API access.")
    print("   If you get 403 errors, you may need to:")
    print("   1. Create an API key in OpenWebUI settings")
    print("   2. Set OPENWEBUI_API_KEY environment variable")
    print("   3. Or test directly in the OpenWebUI web interface")

    # Get API key from environment, default, or user input
    api_key = os.environ.get('OPENWEBUI_API_KEY')
    if not api_key:
        api_key = DEFAULT_API_KEY
        print(f"\n🔑 Using default API key: {api_key[:10]}...{api_key[-4:]}")

        # Ask if user wants to use a different key
        use_different = input("Use a different API key? (y/N): ").strip().lower()
        if use_different in ['y', 'yes']:
            api_key_input = input("Enter OpenWebUI API key: ").strip()
            api_key = api_key_input if api_key_input else DEFAULT_API_KEY
    else:
        print(f"\n🔑 Using API key from environment: {api_key[:10]}...{api_key[-4:]}")

    # Initialize tester
    tester = OpenWebUITester(OPENWEBUI_URL, api_key)

    # Test connection
    if not tester.test_connection():
        print("❌ Cannot connect to OpenWebUI server. Please check:")
        print(f"   • Server is running at {OPENWEBUI_URL}")
        print("   • Network connectivity")
        print("   • Firewall settings")
        return

    # Test API permissions if API key is provided
    if api_key:
        permissions = tester.test_api_permissions()
        chat_accessible = permissions.get("/api/chat/completions", False)

        if not chat_accessible:
            print("\n⚠️ API Key Issues Detected:")
            print("   The chat completions endpoint is not accessible.")
            print("   This could be due to:")
            print("   • Invalid API key")
            print("   • API key endpoint restrictions (ENABLE_API_KEY_ENDPOINT_RESTRICTIONS=True)")
            print("   • Missing /api/chat/completions in API_KEY_ALLOWED_ENDPOINTS")
            print("\n💡 Solutions:")
            print("   1. Check your API key is valid")
            print("   2. Add '/api/chat/completions' to API_KEY_ALLOWED_ENDPOINTS")
            print("   3. Or disable endpoint restrictions temporarily")
            print("   4. Use browser testing instead: python debug-testing/openwebui_browser_tester.py")

            choice = input("\nContinue anyway? (y/N): ").strip().lower()
            if choice not in ['y', 'yes']:
                return

    # Let user select categories
    selected_categories = select_test_categories()

    if not selected_categories:
        print("No categories selected. Exiting.")
        return

    # Get test prompts for selected categories
    test_prompts = get_test_prompts(selected_categories)

    print(f"\n🎯 Selected categories: {', '.join(selected_categories)}")
    print(f"📊 Total tests to run: {len(test_prompts)}")

    # Show breakdown by category
    for category in selected_categories:
        count = sum(1 for test in test_prompts if test["category"] == category)
        print(f"   • {category}: {count} tests")

    # Ask user for confirmation
    try:
        choice = input(f"\nRun {len(test_prompts)} tests? (y/N): ").strip().lower()
        if choice not in ['y', 'yes']:
            print("Test cancelled.")
            return

        # Ask for model selection
        model_input = input("Model to use (default llama3:latest): ").strip()
        model = model_input if model_input else "llama3:latest"

        # Ask for delay between tests
        delay_input = input("Delay between tests in seconds (default 2): ").strip()
        delay = float(delay_input) if delay_input else 2.0

        # Ask for verbose output
        verbose_input = input("Show full response content? (Y/n): ").strip().lower()
        verbose = verbose_input not in ['n', 'no', 'false']

        # Check if any code-related categories are selected
        code_categories = ["Code Search", "AI Analysis", "Language Search"]
        needs_codebase = any(cat in selected_categories for cat in code_categories)

        if needs_codebase:
            print(f"\n🔍 Code analysis categories detected: {[cat for cat in code_categories if cat in selected_categories]}")
            print("📋 A codebase must be selected for these tests to work properly.")

            # Ensure codebase is selected
            if not tester.ensure_codebase_selected(model):
                print("❌ Cannot proceed without a selected codebase for code analysis tests.")
                choice = input("Continue anyway? (y/N): ").strip().lower()
                if choice not in ['y', 'yes']:
                    print("Test cancelled.")
                    return

        # Run tests
        tester.run_test_suite(test_prompts, delay, model, verbose)

    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")

if __name__ == "__main__":
    main()
