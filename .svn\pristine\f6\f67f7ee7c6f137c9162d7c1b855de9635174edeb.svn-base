#!/usr/bin/env python3
"""
Core Test Runner for OpenWebUI Code Analyzer System

This script runs the core test suites that work reliably without external dependencies.
It focuses on testing the components we built: caching system, plugin functionality,
and performance validation.

Usage:
    python run_core_tests.py                    # Run all core tests
    python run_core_tests.py --caching          # Run caching tests only
    python run_core_tests.py --plugin           # Run plugin tests only
    python run_core_tests.py --performance      # Run performance tests only
    python run_core_tests.py --quick            # Run quick validation tests
"""

import subprocess
import sys
import os
import time
import argparse
from datetime import datetime
from typing import List, Tuple

class CoreTestRunner:
    """Runs core test suites with proper reporting"""
    
    def __init__(self):
        self.results = []
        self.start_time = datetime.now()
    
    def run_test_command(self, command: List[str], test_name: str) -> Tuple[bool, float, str]:
        """Run a test command and return success, duration, output"""
        print(f"\n{'='*60}")
        print(f"[TEST] RUNNING: {test_name}")
        print(f"Command: {' '.join(command)}")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=300,  # 5 minute timeout
                cwd=os.path.dirname(os.path.abspath(__file__))
            )
            
            duration = time.time() - start_time
            success = result.returncode == 0
            output = result.stdout + result.stderr
            
            status = "[PASS]" if success else "[FAIL]"
            print(f"{status} {test_name} ({duration:.1f}s)")
            
            if not success:
                print("Error details:")
                print(result.stderr[:500])  # Show first 500 chars of error
            
            return success, duration, output
            
        except subprocess.TimeoutExpired:
            duration = time.time() - start_time
            print(f"[TIMEOUT] {test_name} ({duration:.1f}s)")
            return False, duration, "Test timed out"
        
        except Exception as e:
            duration = time.time() - start_time
            print(f"[ERROR] {test_name} ({duration:.1f}s): {e}")
            return False, duration, f"Exception: {e}"
    
    def run_caching_tests(self) -> bool:
        """Run intelligent caching system tests"""
        print("\n[CACHE] RUNNING CACHING SYSTEM TESTS")
        
        tests = [
            (['python', 'test_suite.py', 'TestIntelligentCaching'], 'Caching System Tests'),
        ]
        
        all_passed = True
        for command, name in tests:
            success, duration, output = self.run_test_command(command, name)
            self.results.append((name, success, duration))
            if not success:
                all_passed = False
        
        return all_passed
    
    def run_plugin_tests(self) -> bool:
        """Run OpenWebUI plugin tests"""
        print("\n[PLUGIN] RUNNING PLUGIN TESTS")
        
        tests = [
            (['python', 'test_suite.py', 'TestOpenWebUIPlugin.test_plugin_initialization'], 'Plugin Initialization'),
            (['python', 'test_suite.py', 'TestOpenWebUIPlugin.test_query_intent_detection'], 'Query Intent Detection'),
            (['python', 'test_suite.py', 'TestOpenWebUIPlugin.test_help_system'], 'Help System'),
            (['python', 'test_suite.py', 'TestOpenWebUIPlugin.test_codebase_management'], 'Codebase Management'),
        ]
        
        all_passed = True
        for command, name in tests:
            success, duration, output = self.run_test_command(command, name)
            self.results.append((name, success, duration))
            if not success:
                all_passed = False
        
        return all_passed
    
    def run_performance_tests(self) -> bool:
        """Run performance tests"""
        print("\n[PERF] RUNNING PERFORMANCE TESTS")
        
        tests = [
            (['python', 'performance_test_suite.py', '--cache-test'], 'Cache Performance Tests'),
        ]
        
        all_passed = True
        for command, name in tests:
            success, duration, output = self.run_test_command(command, name)
            self.results.append((name, success, duration))
            if not success:
                all_passed = False
        
        return all_passed
    
    def run_quick_tests(self) -> bool:
        """Run quick validation tests"""
        print("\n[QUICK] RUNNING QUICK VALIDATION TESTS")
        
        tests = [
            (['python', 'test_suite.py', 'TestIntelligentCaching.test_cache_initialization'], 'Cache Initialization'),
            (['python', 'test_suite.py', 'TestOpenWebUIPlugin.test_plugin_initialization'], 'Plugin Initialization'),
            (['python', 'test_suite.py', 'TestOpenWebUIPlugin.test_query_intent_detection'], 'Intent Detection'),
        ]
        
        all_passed = True
        for command, name in tests:
            success, duration, output = self.run_test_command(command, name)
            self.results.append((name, success, duration))
            if not success:
                all_passed = False
        
        return all_passed
    
    def print_summary(self):
        """Print test execution summary"""
        total_duration = (datetime.now() - self.start_time).total_seconds()
        
        print(f"\n{'='*60}")
        print("[SUMMARY] CORE TEST EXECUTION SUMMARY")
        print(f"{'='*60}")
        
        total_tests = len(self.results)
        passed_tests = sum(1 for _, success, _ in self.results if success)
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {success_rate:.1f}%")
        print(f"Total Duration: {total_duration:.1f}s")
        
        print(f"\nDetailed Results:")
        for name, success, duration in self.results:
            status = "[PASS]" if success else "[FAIL]"
            print(f"  {status} {name:30} ({duration:.1f}s)")
        
        if failed_tests == 0:
            print(f"\n[SUCCESS] ALL CORE TESTS PASSED!")
            return True
        else:
            print(f"\n[WARNING] {failed_tests} TESTS FAILED!")
            return False


def main():
    parser = argparse.ArgumentParser(description='Core Test Runner')
    parser.add_argument('--caching', action='store_true', help='Run caching tests only')
    parser.add_argument('--plugin', action='store_true', help='Run plugin tests only')
    parser.add_argument('--performance', action='store_true', help='Run performance tests only')
    parser.add_argument('--quick', action='store_true', help='Run quick validation tests only')
    
    args = parser.parse_args()
    
    runner = CoreTestRunner()
    
    print("[ROCKET] STARTING CORE TEST EXECUTION")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        all_passed = True
        
        if args.caching:
            all_passed = runner.run_caching_tests()
        elif args.plugin:
            all_passed = runner.run_plugin_tests()
        elif args.performance:
            all_passed = runner.run_performance_tests()
        elif args.quick:
            all_passed = runner.run_quick_tests()
        else:
            # Run all core tests
            caching_passed = runner.run_caching_tests()
            plugin_passed = runner.run_plugin_tests()
            performance_passed = runner.run_performance_tests()
            all_passed = caching_passed and plugin_passed and performance_passed
        
        # Print summary
        final_success = runner.print_summary()
        
        # Exit with appropriate code
        if final_success:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n[STOP] Test execution interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n[ERROR] Test execution failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
