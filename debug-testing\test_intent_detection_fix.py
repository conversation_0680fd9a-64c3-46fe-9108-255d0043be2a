#!/usr/bin/env python3
"""
Test the intent detection fix for stats queries
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_intent_detection_fix():
    """Test that stats queries now trigger management intent"""
    print("🧪 Testing Intent Detection Fix")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test the specific failing queries
    test_queries = [
        "get stats for utils",
        "get statistics for utils", 
        "show stats utils",
        "codebase stats utils"
    ]
    
    success_count = 0
    
    for query in test_queries:
        print(f"\n🔍 Testing: '{query}'")
        print("-" * 40)
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": query}],
                    "tool_ids": ["code_analyzer_tool"],
                    "stream": False,
                    "max_tokens": 1000
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                print(f"✅ Response received ({len(content)} chars)")
                
                # Check for real codebase data
                real_indicators = [
                    "Total chunks: 479",
                    "Unique files: 43",
                    "Basic Codebase Statistics",
                    "Languages:",
                    "C: 460",
                    "Last updated: 2025-06-27"
                ]
                
                found_real = [ind for ind in real_indicators if ind in content]
                
                if found_real:
                    print(f"🎉 SUCCESS: Real codebase data found!")
                    print(f"   Data: {found_real[:2]}")
                    success_count += 1
                else:
                    print("❌ Still getting generic/fallback response")
                
                # Show preview
                preview = content[:200] + "..." if len(content) > 200 else content
                print(f"Preview: {preview}")
                
            else:
                print(f"❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return success_count, len(test_queries)

def main():
    """Main test function"""
    print("🔧 Intent Detection Fix Test")
    print("=" * 60)
    print("Testing that stats queries now trigger management routing")
    
    success, total = test_intent_detection_fix()
    
    print(f"\n📊 RESULTS")
    print("=" * 40)
    print(f"Success rate: {success}/{total} ({success/total*100:.1f}%)")
    
    if success == total:
        print("🎉 PERFECT: Intent detection fix working!")
        print("✅ All stats queries return real codebase data")
        print("✅ Management routing is working correctly")
    elif success > 0:
        print("👍 PARTIAL: Some improvement seen")
        print("⚠️ Some queries still need work")
    else:
        print("❌ ISSUE: Fix not working yet")
        print("🔧 Need to update tool in OpenWebUI")
    
    print(f"\n🎯 Expected:")
    print("All stats queries should return real utils codebase statistics")
    print("with 479 chunks, 43 files, and language breakdown")

if __name__ == "__main__":
    main()
