#!/usr/bin/env python3
"""
Diagnose why OpenWebUI might not be using the tool
"""

import requests
import json
from datetime import datetime

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_tool_response():
    """Test if tool is responding vs just LLM"""
    print("🔍 Tool Response Diagnostic Test")
    print("=" * 50)
    
    # Test queries that should trigger the tool
    test_queries = [
        ("status", "Should return formatted status with emojis"),
        ("list codebases", "Should return actual codebase list"),
        ("select codebase utils", "Should select utils codebase"),
        ("find memory allocation", "Should search code and return snippets"),
    ]
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    for query, expected in test_queries:
        print(f"\n🧪 Testing: '{query}'")
        print(f"Expected: {expected}")
        print("-" * 40)
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": query}],
                    "stream": False,
                    "max_tokens": 1000
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                print(f"✅ Response received ({len(content)} chars)")
                
                # Analyze response to determine if tool was used
                tool_indicators = [
                    "🔧", "📚", "✅", "❌", "🚀",  # Emojis
                    "**Code Analyzer System Status**", "**Available Codebases**",  # Tool formatting
                    "utils", "z80emu", "modbus", "networking_project",  # Your codebases
                    "ready_enhanced", "v3.0_enhanced",  # Tool-specific terms
                ]
                
                llm_indicators = [
                    "I don't have access", "I can't", "I'm not able to",
                    "As an AI", "I don't have real-time", "I cannot access",
                ]
                
                tool_score = sum(1 for indicator in tool_indicators if indicator in content)
                llm_score = sum(1 for indicator in llm_indicators if indicator in content)
                
                print(f"Tool indicators: {tool_score}")
                print(f"LLM-only indicators: {llm_score}")
                
                if tool_score > 0:
                    print("🎉 PLUGIN IS WORKING! Response contains tool-specific content")
                elif llm_score > 0:
                    print("❌ PLUGIN NOT WORKING: Response is generic LLM text")
                else:
                    print("⚠️ UNCLEAR: Response doesn't clearly indicate tool usage")
                
                # Show response preview
                print(f"\nResponse preview:")
                preview = content[:200] + "..." if len(content) > 200 else content
                print(f"'{preview}'")
                
            else:
                print(f"❌ HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def check_openwebui_functions():
    """Check if functions/tools are available in OpenWebUI"""
    print(f"\n🔧 Checking OpenWebUI Functions API")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Try to get functions/tools list
    endpoints_to_try = [
        "/api/functions",
        "/api/tools", 
        "/api/v1/functions",
        "/api/v1/tools",
    ]
    
    for endpoint in endpoints_to_try:
        try:
            response = session.get(f"{OPENWEBUI_URL}{endpoint}", timeout=10)
            print(f"📡 {endpoint}: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if isinstance(data, list):
                        print(f"   Found {len(data)} functions/tools")
                        for item in data[:3]:  # Show first 3
                            name = item.get('name', item.get('id', 'Unknown'))
                            print(f"   - {name}")
                    else:
                        print(f"   Response: {str(data)[:100]}...")
                except:
                    print(f"   Non-JSON response: {response.text[:100]}...")
                    
        except Exception as e:
            print(f"❌ {endpoint}: {e}")

def suggest_solutions():
    """Suggest solutions based on common issues"""
    print(f"\n💡 Common Solutions")
    print("=" * 50)
    
    print("1. **Install Tool in OpenWebUI:**")
    print("   - Go to http://home-ai-server.local:8080")
    print("   - Settings → Functions → Import Function")
    print("   - Upload open_webui_code_analyzer_tool.py")
    
    print("\n2. **Enable Tool for Model:**")
    print("   - Settings → Models → llama3:latest")
    print("   - Enable your Code Analyzer tool")
    
    print("\n3. **Check Tool Errors:**")
    print("   - Settings → Functions → Your Tool")
    print("   - Look for error messages or warnings")
    
    print("\n4. **Verify Code Analyzer Server:**")
    print("   - Test: curl http://home-ai-server:5002/health")
    print("   - Should return server status")
    
    print("\n5. **Test Tool Directly:**")
    print("   - Run: python debug-testing/interactive_tool_test.py")
    print("   - This tests tool code without OpenWebUI")
    
    print("\n6. **Check OpenWebUI Logs:**")
    print("   - Look at OpenWebUI container/service logs")
    print("   - Check for tool loading errors")

def main():
    """Main diagnostic function"""
    print("🩺 OpenWebUI Tool Diagnostic Tool")
    print("=" * 60)
    print(f"Target: {OPENWEBUI_URL}")
    print(f"Model: llama3:latest")
    print(f"Time: {datetime.now()}")
    
    # Run diagnostics
    test_tool_response()
    check_openwebui_functions()
    suggest_solutions()
    
    print(f"\n🎯 Next Steps:")
    print("1. Check the test results above")
    print("2. If tool indicators = 0, tool is not working")
    print("3. Follow the suggested solutions")
    print("4. Re-run this diagnostic after making changes")

if __name__ == "__main__":
    main()
