#!/usr/bin/env python3
"""
Test all plugin commands with the ! prefix
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_all_plugin_commands():
    """Test all plugin commands with ! prefix"""
    print("🎉 Testing Tool Commands with ! Prefix")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Tool commands to test
    plugin_commands = [
        ("!list_codebases", "List available codebases"),
        ("!get_code_analyzer_help", "Get plugin help"),
        ("!check_system_status", "Check system status"),
        ("!get_detailed_status", "Get detailed status"),
        ("!select_codebase utils", "Select utils codebase"),
        ("!get_codebase_stats utils", "Get utils statistics"),
        ("!search_code find memory allocation", "Search for memory allocation"),
        ("!ask_about_code How does memory management work?", "Ask AI about code"),
        ("!get_code_context memory allocation", "Get code context"),
    ]
    
    working_commands = []
    
    for command, description in plugin_commands:
        print(f"\n🧪 Testing: '{command}'")
        print(f"Description: {description}")
        print("-" * 40)
        
        try:
            payload = {
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": command}],
                "stream": False,
                "max_tokens": 1000
            }
            
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                print(f"✅ Response received ({len(content)} chars)")
                
                # Check for plugin indicators
                plugin_indicators = [
                    "🔧", "📚", "✅", "❌", "🚀", "📊",  # Emojis
                    "Code Analysis System", "Available Codebases", "Tool Server",  # Tool text
                    "utils", "z80emu", "modbus", "networking_project",  # Your codebases
                    "ready_enhanced", "v3.0_enhanced",  # Tool-specific terms
                    "Documents:", "Enhanced Metadata:",  # Tool formatting
                ]
                
                found = [ind for ind in plugin_indicators if ind in content]
                
                if found:
                    print(f"🎉 PLUGIN WORKING! Found: {found[:3]}...")
                    working_commands.append(command)
                    
                    # Show response preview
                    if len(content) > 300:
                        preview = content[:150] + "\n...\n" + content[-150:]
                    else:
                        preview = content
                    print(f"Response preview:")
                    print(preview)
                else:
                    print("⚠️ No clear plugin indicators")
                    # Show short preview anyway
                    preview = content[:100] + "..." if len(content) > 100 else content
                    print(f"Preview: {preview}")
                    
            else:
                print(f"❌ HTTP {response.status_code}: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return working_commands

def test_status_commands():
    """Test different status command variations"""
    print(f"\n📊 Testing Status Command Variations")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    status_commands = [
        "!check_system_status",
        "!get_detailed_status", 
        "!get_server_status",
        "status",  # Without !
        "!status",  # With !
    ]
    
    for command in status_commands:
        print(f"\n🧪 Testing: '{command}'")
        
        try:
            payload = {
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": command}],
                "stream": False,
                "max_tokens": 500
            }
            
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                # Check for status-specific indicators
                status_indicators = ["Tool Server", "Code Analysis Service", "Online", "Available Codebases"]
                found = [ind for ind in status_indicators if ind in content]
                
                if found:
                    print(f"✅ Status plugin working! Found: {found}")
                else:
                    print("⚠️ Generic response")
                    
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Main test function"""
    print("🧪 Complete Tool Command Test")
    print("=" * 70)
    print("Testing the ! prefix command format for code_analyzer_tools")
    
    # Test all commands
    working_commands = test_all_plugin_commands()
    
    # Test status variations
    test_status_commands()
    
    # Summary
    print(f"\n📋 SUMMARY")
    print("=" * 50)
    print(f"✅ Working commands found: {len(working_commands)}")
    
    if working_commands:
        print(f"\n🎉 Your plugin IS working! Use these commands:")
        for cmd in working_commands:
            print(f"   • {cmd}")
            
        print(f"\n💡 Command Format:")
        print("   • Use ! prefix for plugin commands")
        print("   • Example: !list_codebases")
        print("   • Example: !select_codebase utils")
        print("   • Example: !search_code memory allocation")
        
    else:
        print(f"\n❌ No working commands found")
        print("Tool may not be properly installed or enabled")
    
    print(f"\n🎯 Next Steps:")
    print("1. Update your testing scripts to use ! prefix")
    print("2. Test these commands in OpenWebUI web interface")
    print("3. Use working commands for your RAG testing")

if __name__ == "__main__":
    main()
