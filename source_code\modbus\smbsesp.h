/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2023 */
/*****************************************************************************/
/*                                                                           */
/* This file is the property of:                                             */
/*                                                                           */
/*                       Triangle MicroWorks, Inc.                           */
/*                      Raleigh, North Carolina USA                          */
/*                       www.TriangleMicroWorks.com                          */
/*                          (919) 870-6615                                   */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*                                                                           */
/*****************************************************************************/

/* file: smbsesp.h
 * description: This file is intended for internal SCL use only.
 *   Private Server Modbus session support
 */
#ifndef SMBSESP_DEFINED
#define SMBSESP_DEFINED

#include "tmwscl/utils/tmwsesn.h"
#include "tmwscl/modbus/mbsesn.h"

/* Server Modbus Session Context */
typedef struct SMBSessionStruct {

  /* Generic modbus Session info, must be first entry */
  MBSESN mb;

  TMWSESN_TX_DATA responseBuffer;

  /* The following variables may be set to limit the number of elements allowed to read
     or write.  These are set to the maximum allowed by the 
     protocol (see mbdefs.h) at initialization, but may be changed prior to opening a
     session in order to limit the amount of data exchanged for particular data types and
     function codes.  The protocol for example is capable of transfering 2000 Coils in one
     transaction, but this may present performance problems for legacy or process bound 
     target sytems.  Older Modicon PLCs for example, may limit coils read/written to 800.
     In the case that these are limited, user documentation should clearly specify this.
  */
  TMWTYPES_USHORT maxDI_Read;          /* Max num Discrete Inputs allowed to be read      */
  TMWTYPES_USHORT maxCoil_Read;        /* Max num Coils allowed to be read                */
  TMWTYPES_USHORT maxCoil_Write;       /* Max num Coils allowed to be written             */
  TMWTYPES_USHORT maxIReg_Read;        /* Max num Input registers allowed to be read      */
  TMWTYPES_USHORT maxHReg_Read;        /* Max num Holding Registers allowed to be read    */
  TMWTYPES_USHORT maxHreg_Write;       /* Max num Holding Registers allowed to be written */

  /* The following only apply to MB Function Code 23 
   */
  TMWTYPES_USHORT maxHReg_Read_FC23;   /* Max num Holding Registers allowed to be read    */
  TMWTYPES_USHORT maxHreg_Write_FC23;  /* Max num Holding Registers allowed to be written */

  /* For FC 43 MEI Type 15 Read Device Identification  
    Identification conformity level of the device and type of supported access
    0x01: basic identification (stream access only)
    0x02: regular identification (stream access only)
    0x03: extended identification (stream access only)
    0x81: basic identification (stream access and individual access)
    0x82: regular identification (stream access and individual access)
    0x83: extended identification(stream access and individual access)
  */
  TMWTYPES_UCHAR  conformityLevel;

  /* FC code 8 sub-function 1 and 4 control Listen Only Mode.
   * When in Listen Only Mode, any MODBUS messages are monitored but no actions are taken 
   * and no responses will be sent
   */
  TMWTYPES_BOOL listenOnlyMode;

} SMBSESN;

#ifdef __cplusplus
extern "C" {
#endif

  /* function: smbsesn_sendResponse */
  void TMWDEFS_GLOBAL smbsesn_sendResponse(
    TMWSESN_TX_DATA *pTxData);

  /* function: smbsesn_processFrame */
  TMWTYPES_BOOL TMWDEFS_GLOBAL smbsesn_processFrame(
    TMWSESN *pSession,
    TMWSESN_RX_DATA *pRxFrame);

#ifdef __cplusplus
}
#endif
#endif /* SMBSESN_DEFINED */
