extends ../layout

block content
  .page-header
    h3 Profile Information
    hr

  form.form-horizontal(action='/account/profile', method='POST')
    input(type='hidden', name='_csrf', value=_csrf)
    .form-group.row.justify-content-md-center
      label.col-sm-3.col-form-label.text-right.font-weight-bold(for='email') Email
      .col-sm-7
        input.form-control(type='email', name='email', id='email', value=user.email)
    .form-group.row.justify-content-md-center
      label.col-sm-3.col-form-label.text-right.font-weight-bold(for='name') Name
      .col-sm-7
        input.form-control(type='text', name='name', id='name', value=user.profile.name)
    .form-group.row.justify-content-md-center.align-items-center
      label.col-sm-3.col-form-label.text-right.font-weight-bold Gender
      .col-sm-7
        .form-check.form-check-inline
          input#option-male.form-check-input(type='radio', checked=user.profile.gender==='male', name='gender', value='male', data-toggle='radio')
          label.form-check-label(for="option-male") Male
        .form-check.form-check-inline
          input#option-female.form-check-input(type='radio', checked=user.profile.gender==='female', name='gender', value='female', data-toggle='radio')
          label.form-check-label(for="option-female") Female
        .form-check.form-check-inline
          input#option-other.form-check-input(type='radio', checked=user.profile.gender==='other', name='gender', value='other', data-toggle='radio')
          label.form-check-label(for="option-other") Other
    .form-group.row.justify-content-md-center
      label.col-sm-3.col-form-label.text-right.font-weight-bold(for='location') Location
      .col-sm-7
        input.form-control(type='text', name='location', id='location', value=user.profile.location)
    .form-group.row.justify-content-md-center
      label.col-sm-3.col-form-label.text-right.font-weight-bold(for='website') Website
      .col-sm-7
        input.form-control(type='text', name='website', id='website', value=user.profile.website)
    .form-group.row.justify-content-md-center
      label.col-sm-3.col-form-label.text-right.font-weight-bold Gravatar
      .col-sm-7
        img(src=user.gravatar(), class='profile', width='100', height='100', alt=((user.profile.name || user.email || user.id)))
    .form-group.row.justify-content-md-center
      .col-sm-4
        button.btn.btn.btn-primary(type='submit')
          i.fa.fa-pencil
          | Update Profile

  .page-header
    h3 Change Password
    hr

  form.form-horizontal(action='/account/password', method='POST')
    input(type='hidden', name='_csrf', value=_csrf)
    .form-group.row.justify-content-md-center
      label.col-sm-3.col-form-label.text-right.font-weight-bold(for='password') New Password
      .col-sm-7
        input.form-control(type='password', name='password', id='password')
    .form-group.row.justify-content-md-center
      label.col-sm-3.col-form-label.text-right.font-weight-bold(for='confirmPassword') Confirm Password
      .col-sm-7
        input.form-control(type='password', name='confirmPassword', id='confirmPassword')
    .form-group.row.justify-content-md-center
      .col-sm-4
        button.btn.btn-primary(type='submit')
          i.fa.fa-lock
          | Change Password

  .page-header
    h3 Delete Account
    hr

  form.form-horizontal(action='/account/delete', method='POST')
    .form-group.row.justify-content-md-center
      p.offset-sm-3.col-sm-7 You can delete your account, but keep in mind this action is irreversible.
      input(type='hidden', name='_csrf', value=_csrf)
      .col-sm-4
        button.btn.btn-danger(type='submit')
          i.fa.fa-trash
          | Delete my account

  .page-header
    h3 Linked Accounts
    hr
  .form-horizontal
    .form-group.row.justify-content-md-center
      .offset-sm-3.col-sm-7
        if user.facebook
          p: a.text-danger(href='/account/unlink/facebook') Unlink your Facebook account
        else
          p: a(href='/auth/facebook') Link your Facebook account

