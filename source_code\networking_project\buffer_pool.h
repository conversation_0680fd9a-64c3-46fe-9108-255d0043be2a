#ifndef BUFFER_POOL_H
#define BUFFER_POOL_H

#include <vector>
#include <mutex>
#include <memory>

/**
 * Thread-safe buffer pool for efficient memory management
 */
class BufferPool {
private:
    std::vector<std::unique_ptr<char[]>> available_buffers;
    std::mutex pool_mutex;
    size_t buffer_size;
    size_t max_pool_size;
    
public:
    BufferPool(size_t buf_size, size_t max_size);
    ~BufferPool();
    
    char* acquireBuffer();
    void releaseBuffer(char* buffer);
    size_t getAvailableCount() const;
    void preallocateBuffers(size_t count);
};

#endif // BUFFER_POOL_H
