#!/usr/bin/env python3
"""
Test harness for debugging the OpenWebUI Code Analysis tool locally
"""

import sys
import os
import asyncio
from typing import Optional

# Add the parent directory to the path so we can import the plugin
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the plugin class
from open_webui_code_analyzer_tool import Tools

class MockValves:
    """Mock valves object to simulate OpenWebUI configuration"""
    def __init__(self):
        self.CODE_ANALYZER_SERVER_URL = "http://home-ai-server.local:5002"
        self.request_timeout = 30
        self.auto_context_injection = True
        self.context_format = "detailed"
        self.current_codebase = None  # This is key for testing

class MockEventEmitter:
    """Mock event emitter for debugging"""
    def __init__(self, verbose=True):
        self.verbose = verbose
        
    async def __call__(self, event):
        if self.verbose:
            print(f"📡 Event: {event}")

class ToolDebugger:
    """Debug harness for the plugin"""
    
    def __init__(self, verbose=True):
        self.verbose = verbose
        self.plugin = Tools()
        self.plugin.valves = MockValves()
        self.event_emitter = MockEventEmitter(verbose)
    
    async def test_query(self, query: str, description: str = ""):
        """Test a single query with full debugging"""
        print(f"\n{'='*60}")
        print(f"🧪 TESTING: {query}")
        if description:
            print(f"📝 Description: {description}")
        print(f"{'='*60}")
        
        # Show current state
        print(f"🔧 Current codebase: {self.plugin.valves.current_codebase}")
        
        try:
            # Call the main plugin function
            result = await self.plugin.__call__(query, __event_emitter__=self.event_emitter)
            
            print(f"\n📊 RESULT:")
            print(f"Type: {type(result)}")
            print(f"Length: {len(str(result))} chars")
            print(f"Content: {str(result)[:200]}{'...' if len(str(result)) > 200 else ''}")
            
            return result
            
        except Exception as e:
            print(f"❌ EXCEPTION: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def test_intent_detection(self, query: str):
        """Test just the intent detection logic"""
        print(f"\n🔍 INTENT DETECTION TEST: '{query}'")
        
        try:
            intent = self.plugin._detect_query_intent(query)
            print(f"Detected intent: {intent}")
            return intent
        except Exception as e:
            print(f"❌ Intent detection error: {e}")
            return None
    
    async def test_management_routing(self, query: str):
        """Test just the management query routing"""
        print(f"\n🔀 MANAGEMENT ROUTING TEST: '{query}'")
        
        try:
            result = await self.plugin._handle_management_query(query)
            print(f"Management result: {str(result)[:200]}...")
            return result
        except Exception as e:
            print(f"❌ Management routing error: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def test_codebase_selection(self, codebase_name: str):
        """Test codebase selection specifically"""
        print(f"\n🎯 CODEBASE SELECTION TEST: '{codebase_name}'")
        
        try:
            result = await self.plugin.select_codebase(codebase_name, __event_emitter__=self.event_emitter)
            print(f"Selection result: {result}")
            print(f"Valves current_codebase: {self.plugin.valves.current_codebase}")
            return result
        except Exception as e:
            print(f"❌ Selection error: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def set_current_codebase(self, codebase_name: str):
        """Manually set current codebase for testing"""
        self.plugin.valves.current_codebase = codebase_name
        print(f"🔧 Manually set current codebase to: {codebase_name}")

async def main():
    """Main debug session"""
    print("🔧 OpenWebUI Code Analysis Tool Debug Harness")
    print("=" * 70)
    
    debugger = ToolDebugger(verbose=True)
    
    # Test sequence
    test_queries = [
        ("list codebases", "Should list all codebases"),
        ("select codebase utils", "Should select utils codebase"),
        ("get stats for utils", "Should get utils statistics"),
        ("show stats for utils", "Should get stats, not list (condition order test)"),
        ("get stats", "Should show 'no codebase selected' or use current"),
    ]
    
    print("🧪 Running test sequence...")
    
    for query, description in test_queries:
        # Test intent detection first
        await debugger.test_intent_detection(query)
        
        # Test management routing if it's a management query
        intent = debugger.plugin._detect_query_intent(query)
        if intent == "codebase_management":
            await debugger.test_management_routing(query)
        
        # Test full query
        await debugger.test_query(query, description)
        
        # Add a pause for readability
        input("\nPress Enter to continue to next test...")
    
    print("\n🎯 Debug session complete!")
    print("You can now set breakpoints and step through the code.")

async def interactive_debug():
    """Interactive debugging session"""
    print("🔧 Interactive Tool Debug Session")
    print("=" * 50)
    print("Available commands:")
    print("  test <query>           - Test a query")
    print("  intent <query>         - Test intent detection")
    print("  management <query>     - Test management routing")
    print("  select <codebase>      - Test codebase selection")
    print("  set_codebase <name>    - Manually set current codebase")
    print("  quit                   - Exit")
    print()
    
    debugger = ToolDebugger(verbose=True)
    
    while True:
        try:
            command = input("\n🔧 Debug> ").strip()
            
            if command.lower() in ['quit', 'exit', 'q']:
                break
            
            parts = command.split(' ', 1)
            cmd = parts[0].lower()
            arg = parts[1] if len(parts) > 1 else ""
            
            if cmd == "test" and arg:
                await debugger.test_query(arg)
            elif cmd == "intent" and arg:
                await debugger.test_intent_detection(arg)
            elif cmd == "management" and arg:
                await debugger.test_management_routing(arg)
            elif cmd == "select" and arg:
                await debugger.test_codebase_selection(arg)
            elif cmd == "set_codebase" and arg:
                debugger.set_current_codebase(arg)
            else:
                print("❌ Unknown command or missing argument")
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n👋 Debug session ended")

if __name__ == "__main__":
    print("Choose debug mode:")
    print("1. Automated test sequence")
    print("2. Interactive debugging")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        asyncio.run(main())
    elif choice == "2":
        asyncio.run(interactive_debug())
    else:
        print("Invalid choice")
