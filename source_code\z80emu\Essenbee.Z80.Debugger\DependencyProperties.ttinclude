﻿<#@ include     file        ="Header.ttinclude"             #>
<#
    // ----------------------------------------------------------------------------------------------
    // Copyright (c) Mårten Rånge.
    // ----------------------------------------------------------------------------------------------
    // This source code is subject to terms and conditions of the Microsoft Public License. A
    // copy of the license can be found in the License.html file at the root of this distribution.
    // If you cannot locate the  Microsoft Public License, please send an email to
    // <EMAIL>. By using this source code in any fashion, you are agreeing to be bound
    //  by the terms of the Microsoft Public License.
    // ----------------------------------------------------------------------------------------------
    // You must not remove this notice, or any other, from this software.
    // ----------------------------------------------------------------------------------------------
#>

// ReSharper disable CompareOfFloatsByEqualityOperator
// ReSharper disable InconsistentNaming
// ReSharper disable InvocationIsSkipped
// ReSharper disable PartialMethodWithSinglePart
// ReSharper disable PartialTypeWithSinglePart
// ReSharper disable PossibleUnintendedReferenceComparison
// ReSharper disable RedundantAssignment
// ReSharper disable RedundantCast
// ReSharper disable RedundantUsingDirective
// ReSharper disable UnusedMember.Local

namespace <#=Namespace#>
{
    using System.Collections;
    using System.Collections.ObjectModel;
    using System.Collections.Specialized;

    using System.Windows;
    using System.Windows.Media;

<#
    // ------------------------------------------------------------------------
    // +++ Template
    // ------------------------------------------------------------------------
    foreach (var classDef in Model)
    {
#>
    // ------------------------------------------------------------------------
    // <#=classDef.Name#>
    // ------------------------------------------------------------------------
    <#=classDef.IsStatic ? "static " : ""#>partial class <#=classDef.Name#>
    {
        #region Uninteresting generated code
<#
        foreach (var propertyDef in classDef)
        {

            if (propertyDef.IsReadOnly)
            {
#>
        static readonly DependencyPropertyKey <#=propertyDef.DependencyPropertyKeyName#> = DependencyProperty.Register<#=propertyDef.IsAttached ? "Attached" : ""#>ReadOnly (
            "<#=propertyDef.Name#>",
            typeof (<#=propertyDef.Type#>),
            typeof (<#=classDef.Name#>),
            new FrameworkPropertyMetadata (
                <#=propertyDef.DefaultValue#>,
                <#=propertyDef.MetaDataOptions#>,
                <#=propertyDef.IsGeneratingChangedEvent ? propertyDef.PropertyChangedEventName  : "null"    #>,
                <#=propertyDef.IsGeneratingCoerceEvent  ? propertyDef.PropertyCoerceEventName   : "null"    #>
            ));

        public static readonly DependencyProperty <#=propertyDef.DependencyPropertyName#> = <#=propertyDef.DependencyPropertyKeyName#>.DependencyProperty;
<#
            }
            else if (propertyDef.OwnerType != null)
            {
#>
        public static readonly DependencyProperty <#=propertyDef.DependencyPropertyName#> = <#=propertyDef.OwnerType#>.<#=propertyDef.DependencyPropertyName#>.AddOwner (
            typeof (<#=classDef.Name#>),
            new FrameworkPropertyMetadata (
                <#=propertyDef.IsGeneratingChangedEvent ? propertyDef.PropertyChangedEventName  : "null"    #>,
                <#=propertyDef.IsGeneratingCoerceEvent  ? propertyDef.PropertyCoerceEventName   : "null"    #>
            ));
<#
            }
            else
            {
#>
        public static readonly DependencyProperty <#=propertyDef.DependencyPropertyName#> = DependencyProperty.Register<#=propertyDef.IsAttached ? "Attached" : ""#> (
            "<#=propertyDef.Name#>",
            typeof (<#=propertyDef.Type#>),
            typeof (<#=classDef.Name#>),
            new FrameworkPropertyMetadata (
                <#=propertyDef.DefaultValue#>,
                <#=propertyDef.MetaDataOptions#>,
                <#=propertyDef.IsGeneratingChangedEvent ? propertyDef.PropertyChangedEventName  : "null"    #>,
                <#=propertyDef.IsGeneratingCoerceEvent  ? propertyDef.PropertyCoerceEventName   : "null"    #>
            ));
<#
            }
#>

<#
            if (propertyDef.IsAttached)
            {
#>
<#
                if (propertyDef.IsGeneratingChangedEvent)
                {
#>
        static void <#=propertyDef.PropertyChangedEventName#> (DependencyObject dependencyObject, DependencyPropertyChangedEventArgs eventArgs)
        {
            if (dependencyObject != null)
            {
                var oldValue = (<#=propertyDef.Type#>)eventArgs.OldValue;
                var newValue = (<#=propertyDef.Type#>)eventArgs.NewValue;

<#
    if (propertyDef.IsCollection && propertyDef.IsGeneratingCollectionChangedEvent)
    {
#>
                if (oldValue != null)
                {
                    oldValue.CollectionChanged -= <#=propertyDef.CollectionChangedEventName#>;
                }

                if (newValue != null)
                {
                    newValue.CollectionChanged += <#=propertyDef.CollectionChangedEventName#>;
                }

<#
    }
#>
                <#=propertyDef.PropertyChangedEventName#> (dependencyObject, oldValue, newValue);
            }
        }

<#
    if (propertyDef.IsCollection && propertyDef.IsGeneratingCollectionChangedEvent)
    {
#>
        static void <#=propertyDef.CollectionChangedEventName#>(object sender, NotifyCollectionChangedEventArgs e)
        {
            <#=propertyDef.CollectionChangedEventName#> (
                sender,
                e.Action,
                e.OldStartingIndex,
                e.OldItems,
                e.NewStartingIndex,
                e.NewItems
                );
        }
<#
    }
#>
<#
                }
#>
<#
                if (propertyDef.IsGeneratingCoerceEvent)
                {
#>
        static object <#=propertyDef.PropertyCoerceEventName#> (DependencyObject dependencyObject, object basevalue)
        {
            if (dependencyObject == null)
            {
                return basevalue;
            }
            var value = (<#=propertyDef.Type#>)basevalue;

            <#=propertyDef.PropertyCoerceEventName#> (dependencyObject, ref value);

<#
            if (propertyDef.IsCollection)
            {
#>
            if (value == null)
            {
               value = new <#=propertyDef.Type#> ();
            }

<#
            }
#>
            return value;
        }
<#
                }
#>
<#
            }
            else
            {
#>
<#
                if (propertyDef.IsGeneratingChangedEvent)
                {
#>
        static void <#=propertyDef.PropertyChangedEventName#> (DependencyObject dependencyObject, DependencyPropertyChangedEventArgs eventArgs)
        {
            var instance = dependencyObject as <#=classDef.Name#>;
            if (instance != null)
            {
                var oldValue = (<#=propertyDef.Type#>)eventArgs.OldValue;
                var newValue = (<#=propertyDef.Type#>)eventArgs.NewValue;

<#
    if (propertyDef.IsCollection && propertyDef.IsGeneratingCollectionChangedEvent)
    {
#>
                if (oldValue != null)
                {
                    oldValue.CollectionChanged -= instance.<#=propertyDef.CollectionChangedEventName#>;
                }

                if (newValue != null)
                {
                    newValue.CollectionChanged += instance.<#=propertyDef.CollectionChangedEventName#>;
                }

<#
    }
#>
                instance.<#=propertyDef.PropertyChangedEventName#> (oldValue, newValue);
            }
        }

<#
    if (propertyDef.IsCollection && propertyDef.IsGeneratingCollectionChangedEvent)
    {
#>
        void <#=propertyDef.CollectionChangedEventName#>(object sender, NotifyCollectionChangedEventArgs e)
        {
            <#=propertyDef.CollectionChangedEventName#> (
                sender,
                e.Action,
                e.OldStartingIndex,
                e.OldItems,
                e.NewStartingIndex,
                e.NewItems
                );
        }
<#
    }
#>
<#
                }
#>

<#
                if (propertyDef.IsGeneratingCoerceEvent)
                {
#>
        static object <#=propertyDef.PropertyCoerceEventName#> (DependencyObject dependencyObject, object basevalue)
        {
            var instance = dependencyObject as <#=classDef.Name#>;
            if (instance == null)
            {
                return basevalue;
            }
            var value = (<#=propertyDef.Type#>)basevalue;

            instance.<#=propertyDef.PropertyCoerceEventName#> (ref value);

<#
            if (propertyDef.IsCollection)
            {
#>
            if (value == null)
            {
               value = new <#=propertyDef.Type#> ();
            }
<#
            }
#>

            return value;
        }

<#
                }
#>
<#
            }
        }
#>
        #endregion

<#
        if (!classDef.IsStatic)
        {
#>
<#
        if (!classDef.IsSuppressingDefaultConstructor)
        {
#>
        // --------------------------------------------------------------------
        // Constructor
        // --------------------------------------------------------------------
        public <#=classDef.Name#> ()
        {
            CoerceAllProperties ();
            Constructed__<#=classDef.Name#> ();
        }
        // --------------------------------------------------------------------
        partial void Constructed__<#=classDef.Name#> ();
        // --------------------------------------------------------------------
<#
        }
#>
        void CoerceAllProperties ()
        {
<#
        foreach (var propertyDef in classDef)
        {
#>
            CoerceValue (<#=propertyDef.DependencyPropertyName#>);
<#
        }
#>
        }

<#
        }
#>

        // --------------------------------------------------------------------
        // Properties
        // --------------------------------------------------------------------

<#
        foreach (var propertyDef in classDef)
        {
#>
<#
            if (propertyDef.IsAttached)
            {
#>

        // --------------------------------------------------------------------
        public static <#=propertyDef.Type#> Get<#=propertyDef.Name#> (DependencyObject dependencyObject)
        {
            if (dependencyObject == null)
            {
                return <#=propertyDef.DefaultValue#>;
            }

            return (<#=propertyDef.Type#>)dependencyObject.GetValue (<#=propertyDef.DependencyPropertyName#>);
        }

<#
            if (propertyDef.IsReadOnly)
            {
#>
        private static void Set<#=propertyDef.Name#> (DependencyObject dependencyObject, <#=propertyDef.Type#> value)
        {
            if (dependencyObject != null)
            {
                if (Get<#=propertyDef.Name#> (dependencyObject) != value)
                {
                    SetValue (<#=propertyDef.DependencyPropertyKeyName#>, value);
                }
            }
        }

        private static void Clear<#=propertyDef.Name#> (DependencyObject dependencyObject, <#=propertyDef.Type#> value)
        {
            if (dependencyObject != null)
            {
                ClearValue (<#=propertyDef.DependencyPropertyKeyName#>);
            }
        }
<#
            }
            else
            {
#>
        public static void Set<#=propertyDef.Name#> (DependencyObject dependencyObject, <#=propertyDef.Type#> value)
        {
            if (dependencyObject != null)
            {
                if (Get<#=propertyDef.Name#> (dependencyObject) != value)
                {
                    dependencyObject.SetValue (<#=propertyDef.DependencyPropertyName#>, value);
                }
            }
        }

        public static void Clear<#=propertyDef.Name#> (DependencyObject dependencyObject)
        {
            if (dependencyObject != null)
            {
                dependencyObject.ClearValue (<#=propertyDef.DependencyPropertyName#>);
            }
        }
<#
            }
#>
        // --------------------------------------------------------------------
<#
                if (propertyDef.IsGeneratingChangedEvent)
                {
#>
        static partial void <#=propertyDef.PropertyChangedEventName#> (DependencyObject dependencyObject, <#=propertyDef.Type#> oldValue, <#=propertyDef.Type#> newValue);
<#
                }
#>
<#
                if (propertyDef.IsGeneratingCoerceEvent)
                {
#>
        static partial void <#=propertyDef.PropertyCoerceEventName#> (DependencyObject dependencyObject, ref <#=propertyDef.Type#> coercedValue);
<#
                }
#>
<#
    if (propertyDef.IsCollection && propertyDef.IsGeneratingCollectionChangedEvent)
    {
#>
        static partial void <#=propertyDef.CollectionChangedEventName#> (object sender, NotifyCollectionChangedAction action, int oldStartingIndex, IList oldItems, int newStartingIndex, IList newItems);
<#
    }
#>
        // --------------------------------------------------------------------

<#
            }
            else
            {
#>

        // --------------------------------------------------------------------
        public <#=propertyDef.Type#> <#=propertyDef.Name#>
        {
            get
            {
                return (<#=propertyDef.Type#>)GetValue (<#=propertyDef.DependencyPropertyName#>);
            }
<#
            if (propertyDef.IsReadOnly)
            {
#>
            private set
            {
                if (<#=propertyDef.Name#> != value)
                {
                    SetValue (<#=propertyDef.DependencyPropertyKeyName#>, value);
                }
            }
<#
            }
            else
            {
#>
            set
            {
                if (<#=propertyDef.Name#> != value)
                {
                    SetValue (<#=propertyDef.DependencyPropertyName#>, value);
                }
            }
<#
            }
#>
        }
        // --------------------------------------------------------------------
<#
                if (propertyDef.IsGeneratingChangedEvent)
                {
#>
        partial void <#=propertyDef.PropertyChangedEventName#> (<#=propertyDef.Type#> oldValue, <#=propertyDef.Type#> newValue);
<#
                }
#>
<#
                if (propertyDef.IsGeneratingCoerceEvent)
                {
#>
        partial void <#=propertyDef.PropertyCoerceEventName#> (ref <#=propertyDef.Type#> coercedValue);
<#
                }
#>
<#
    if (propertyDef.IsCollection && propertyDef.IsGeneratingCollectionChangedEvent)
    {
#>
        partial void <#=propertyDef.CollectionChangedEventName#> (object sender, NotifyCollectionChangedAction action, int oldStartingIndex, IList oldItems, int newStartingIndex, IList newItems);
<#
    }
#>
        // --------------------------------------------------------------------

<#
            }
#>

<#
        }
#>
    }
    // ------------------------------------------------------------------------

<#
    }
    // ------------------------------------------------------------------------
#>
}
<#+

    static bool         Default_GenerateChangedEvent            = true;
    static bool         Default_GenerateCoerceEvent             = true;
    static bool         Default_GenerateCollectionChangedEvent  = true;

    string              Namespace   = "T4Include"               ;
    ClassDefinition[]   Model       = new ClassDefinition[0]    ;

    class ClassDefinition : BaseContainer<Root, PropertyDefinition>
    {
        public readonly string  Name                            ;
        public readonly bool    IsStatic                        ;
        public readonly bool    IsSuppressingDefaultConstructor ;

        public readonly bool    GenerateChangedEvent            ;
        public readonly bool    GenerateCoerceEvent             ;
        public readonly bool    GenerateCollectionChangedEvent  ;
        public ClassDefinition (
            string  name                                                ,
            bool    isStatic                            = false         ,
            bool    isSuppressingDefaultConstructor     = false         ,
            bool?   generateChangedEvent                = null          ,
            bool?   generateCoerceEvent                 = null          ,
            bool?   generateCollectionChangedEvent      = null
            )
        {
            Name                            = name ?? "NoName"                  ;
            IsStatic                        = isStatic                          ;
            IsSuppressingDefaultConstructor = isSuppressingDefaultConstructor   ;
            GenerateChangedEvent            = generateChangedEvent              ?? Default_GenerateChangedEvent             ;
            GenerateCoerceEvent             = generateCoerceEvent               ?? Default_GenerateCoerceEvent              ;
            GenerateCollectionChangedEvent  = generateCollectionChangedEvent    ?? Default_GenerateCollectionChangedEvent   ;
        }
    }

    enum PropertyFlags
    {
        None                                = 0x0000    ,
        IsCollection                        = 0x0001    ,
        IsReadOnly                          = 0x0002    ,
        IsAttached                          = 0x0004    ,
    }

    PropertyDefinition P (
        string type                                                 ,
        string name                                                 ,
        string defaultValue                     = null              ,
        string metaDataOptions                  = null              ,
        string ownerType                        = null              ,
        bool?  generateChangedEvent             = null              ,
        bool?  generateCoerceEvent              = null              ,
        PropertyFlags flags                     = PropertyFlags.None
        )
    {
        return new PropertyDefinition
        {
            ItemType                = S_NoType                                                          ,
            Type                    = type                  ?? S_NoType                                 ,
            Name                    = name                  ?? S_NoName                                 ,
            DefaultValue            = defaultValue          ?? "default (" + (type ?? S_NoType) + ")"   ,
            OwnerType               = ownerType                                                         ,
            MetaDataOptions         = metaDataOptions       ?? "FrameworkPropertyMetadataOptions.None"  ,
            Flags                   = flags                                                             ,
            GenerateChangedEvent    = generateChangedEvent                                              ,
            GenerateCoerceEvent     = generateCoerceEvent                                               ,
        };
    }

    PropertyDefinition C (
        string itemType                                             ,
        string name                                                 ,
        string metaDataOptions                  = null              ,
        bool?  generateChangedEvent             = null              ,
        bool?  generateCoerceEvent              = null              ,
        bool?  generateCollectionChangedEvent   = null              ,
        string ownerType                        = null              ,
        PropertyFlags flags                     = PropertyFlags.None
        )
    {
        return new PropertyDefinition
        {
            ItemType                        = itemType                      ?? S_NoType                                 ,
            Type                            = "ObservableCollection<" + (itemType ?? S_NoType) + ">"                    ,
            Name                            = name                          ?? S_NoName                                 ,
            DefaultValue                    = "null"                                                                    ,
            OwnerType                       = ownerType                                                                 ,
            MetaDataOptions                 = metaDataOptions               ?? "FrameworkPropertyMetadataOptions.None"  ,
            Flags                           = flags | PropertyFlags.IsCollection                                        ,
            GenerateChangedEvent            = generateChangedEvent                                                      ,
            GenerateCoerceEvent             = generateCoerceEvent                                                       ,
            GenerateCollectionChangedEvent  = generateCollectionChangedEvent                                            ,
        };
    }

    static bool IsOn (PropertyFlags flags, PropertyFlags test)
    {
        return (flags & test) == test;
    }

    class PropertyDefinition : BaseEntity<ClassDefinition>
    {
        public string               ItemType                        ;
        public string               Type                            ;
        public string               Name                            ;
        public string               DefaultValue                    ;
        public string               MetaDataOptions                 ;
        public string               OwnerType                       ;
        public bool?                GenerateChangedEvent            ;
        public bool?                GenerateCoerceEvent             ;
        public bool?                GenerateCollectionChangedEvent  ;
        public PropertyFlags        Flags                           = PropertyFlags.None                        ;

        public string DependencyPropertyName
        {
            get
            {
                return Name + "Property";
            }
        }

        public string DependencyPropertyKeyName
        {
            get
            {
                return Name + "PropertyKey";
            }
        }

        public string PropertyCoerceEventName
        {
            get
            {
                return "Coerce_" + Name;
            }
        }

        public string PropertyChangedEventName
        {
            get
            {
                return "Changed_" + Name;
            }
        }

        public string CollectionChangedEventName
        {
            get
            {
                return "CollectionChanged_" + Name;
            }
        }

        public bool IsCollection
        {
            get
            {
               return IsOn (Flags, PropertyFlags.IsCollection);
            }
        }

        public bool IsAttached
        {
            get
            {
               return IsOn (Flags, PropertyFlags.IsAttached);
            }
        }

        public bool IsReadOnly
        {
            get
            {
               return IsOn (Flags, PropertyFlags.IsReadOnly) && OwnerType == null;
            }

        }

        public bool IsGeneratingChangedEvent
        {
            get {return GenerateChangedEvent ?? Parent.GenerateChangedEvent;}
        }

        public bool IsGeneratingCoerceEvent
        {
            get {return GenerateCoerceEvent ?? Parent.GenerateCoerceEvent;}
        }

        public bool IsGeneratingCollectionChangedEvent
        {
            get {return (GenerateCollectionChangedEvent ?? Parent.GenerateCollectionChangedEvent) && IsGeneratingChangedEvent;}
        }
    }

#>