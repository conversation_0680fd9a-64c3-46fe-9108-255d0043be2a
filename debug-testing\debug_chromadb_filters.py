#!/usr/bin/env python3
"""
Debug script to test ChromaDB filter syntax directly
"""

import requests
import json

CODE_ANALYZER_SERVER_URL = "http://home-ai-server.local:5002"

def test_filter_combinations():
    """Test different filter combinations to debug the issue"""
    print("🔍 Testing ChromaDB Filter Combinations")
    print("=" * 50)
    
    test_cases = [
        {
            "name": "No filters",
            "payload": {
                "query": "memory allocation",
                "codebase_name": "utils",
                "n_results": 1
            }
        },
        {
            "name": "Single filter: type only",
            "payload": {
                "query": "memory allocation",
                "codebase_name": "utils", 
                "n_results": 1,
                "filter_type": "function"
            }
        },
        {
            "name": "Single filter: language only",
            "payload": {
                "query": "memory allocation",
                "codebase_name": "utils",
                "n_results": 1,
                "filter_language": "c"
            }
        },
        {
            "name": "Two filters: type + language",
            "payload": {
                "query": "memory allocation",
                "codebase_name": "utils",
                "n_results": 1,
                "filter_type": "function",
                "filter_language": "c"
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📋 {test_case['name']}")
        print(f"   Payload: {json.dumps(test_case['payload'], indent=2)}")
        
        try:
            response = requests.post(
                f"{CODE_ANALYZER_SERVER_URL}/tools/enhanced_search",
                json=test_case['payload'],
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                result_text = result.get('result', '')
                
                if "Expected where to have exactly one operator" in result_text:
                    print("❌ CHROMADB SYNTAX ERROR")
                    # Extract the actual filter that was sent
                    if "got {" in result_text:
                        start = result_text.find("got {")
                        end = result_text.find("}", start) + 1
                        if start != -1 and end != -1:
                            filter_str = result_text[start+4:end]
                            print(f"   Problematic filter: {filter_str}")
                elif "Error in enhanced search" in result_text:
                    print("❌ OTHER ERROR")
                    print(f"   Error: {result_text[:150]}...")
                else:
                    print("✅ SUCCESS")
                    if "Found" in result_text:
                        print("   Got search results")
                    else:
                        print("   No results but no error")
            else:
                print(f"❌ HTTP {response.status_code}")
                print(f"   Response: {response.text[:100]}...")
                
        except Exception as e:
            print(f"❌ Request failed: {e}")
    
    print("\n" + "=" * 50)

def test_manual_chromadb_syntax():
    """Test what the actual ChromaDB syntax should be"""
    print("\n🧪 Manual ChromaDB Syntax Test")
    print("=" * 40)
    
    # Let's see what the current _build_enhanced_filters method would generate
    print("Expected filter outputs:")
    
    # Single filter
    single_filter = {"type": {"$eq": "function"}}
    print(f"Single filter: {json.dumps(single_filter)}")
    
    # Multiple filters with $and
    multiple_filters = {
        "$and": [
            {"type": {"$eq": "function"}},
            {"language": {"$eq": "c"}}
        ]
    }
    print(f"Multiple filters: {json.dumps(multiple_filters)}")
    
    # According to ChromaDB docs, this should be valid
    print("\nAccording to ChromaDB docs, both should be valid...")

if __name__ == "__main__":
    test_filter_combinations()
    test_manual_chromadb_syntax()
