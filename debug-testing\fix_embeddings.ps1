# PowerShell script to fix embedding dimension mismatches
$CODE_ANALYZER_SERVER_URL = "http://home-ai-server:5002"

Write-Host "🚀 Fixing embedding dimension mismatches..." -ForegroundColor Green

# Fix utils codebase
Write-Host "`n🗑️ Deleting utils codebase..." -ForegroundColor Yellow
$deleteUtils = @{
    codebase_name = "utils"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$CODE_ANALYZER_SERVER_URL/tools/delete_codebase" -Method POST -Body $deleteUtils -ContentType "application/json"
    Write-Host "✅ Utils deleted successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to delete utils: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n⚙️ Processing utils codebase..." -ForegroundColor Yellow
$processUtils = @{
    codebase_name = "utils"
    exclude_dirs = @("build", "test", "bin", "obj", "__pycache__", ".git")
} | ConvertTo-<PERSON><PERSON>

try {
    $response = Invoke-RestMethod -Uri "$CODE_ANALYZER_SERVER_URL/tools/process_codebase" -Method POST -Body $processUtils -ContentType "application/json"
    Write-Host "✅ Utils processed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to process utils: $($_.Exception.Message)" -ForegroundColor Red
}

# Fix modbus codebase
Write-Host "`n🗑️ Deleting modbus codebase..." -ForegroundColor Yellow
$deleteModbus = @{
    codebase_name = "modbus"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$CODE_ANALYZER_SERVER_URL/tools/delete_codebase" -Method POST -Body $deleteModbus -ContentType "application/json"
    Write-Host "✅ Modbus deleted successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to delete modbus: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n⚙️ Processing modbus codebase..." -ForegroundColor Yellow
$processModbus = @{
    codebase_name = "modbus"
    exclude_dirs = @("build", "test", "bin", "obj", "__pycache__", ".git")
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$CODE_ANALYZER_SERVER_URL/tools/process_codebase" -Method POST -Body $processModbus -ContentType "application/json"
    Write-Host "✅ Modbus processed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to process modbus: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🧪 Testing utils search..." -ForegroundColor Yellow
$testUtils = @{
    query = "function"
    codebase_name = "utils"
    n_results = 1
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$CODE_ANALYZER_SERVER_URL/tools/search_code" -Method POST -Body $testUtils -ContentType "application/json"
    if ($response.result -like "*dimension*mismatch*") {
        Write-Host "❌ Utils still has embedding issues" -ForegroundColor Red
    } else {
        Write-Host "✅ Utils search working correctly" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Failed to test utils: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🧪 Testing modbus search..." -ForegroundColor Yellow
$testModbus = @{
    query = "function"
    codebase_name = "modbus"
    n_results = 1
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$CODE_ANALYZER_SERVER_URL/tools/search_code" -Method POST -Body $testModbus -ContentType "application/json"
    if ($response.result -like "*dimension*mismatch*") {
        Write-Host "❌ Modbus still has embedding issues" -ForegroundColor Red
    } else {
        Write-Host "✅ Modbus search working correctly" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Failed to test modbus: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 Embedding dimension fix completed!" -ForegroundColor Green
Write-Host "You can now run the test script again to verify all collections are working." -ForegroundColor Cyan
