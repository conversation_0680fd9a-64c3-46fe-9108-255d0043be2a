#include <sys/socket.h>
#include <netinet/in.h>
#include <memory>
#include <cstring>

/**
 * Manages TCP socket connections with automatic resource cleanup
 */
class SocketManager {
private:
    int socket_fd;
    std::unique_ptr<struct sockaddr_in> address;
    bool is_connected;
    
public:
    SocketManager(int port) : socket_fd(-1), is_connected(false) {
        socket_fd = socket(AF_INET, SOCK_STREAM, 0);
        if (socket_fd < 0) {
            throw std::runtime_error("Failed to create socket");
        }
        
        address = std::make_unique<struct sockaddr_in>();
        memset(address.get(), 0, sizeof(*address));
        address->sin_family = AF_INET;
        address->sin_port = htons(port);
        address->sin_addr.s_addr = INADDR_ANY;
    }
    
    ~SocketManager() {
        cleanup();
    }
    
    bool bindAndListen(int backlog = 5) {
        if (bind(socket_fd, (struct sockaddr*)address.get(), sizeof(*address)) != 0) {
            return false;
        }
        return listen(socket_fd, backlog) == 0;
    }
    
    void* allocateBuffer(size_t size) {
        void* buffer = malloc(size);
        if (!buffer) {
            throw std::bad_alloc();
        }
        memset(buffer, 0, size);
        return buffer;
    }
    
    void deallocateBuffer(void* buffer) {
        if (buffer) {
            free(buffer);
        }
    }
    
private:
    void cleanup() {
        if (socket_fd >= 0) {
            close(socket_fd);
            socket_fd = -1;
        }
        is_connected = false;
    }
};
