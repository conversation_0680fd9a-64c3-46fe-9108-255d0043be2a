"""
Configurable Processing Pipeline with Dependency Resolution
Implements Phase 0, Section 2 from TODO_SOFTWARE.md
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Set, Optional
from dataclasses import dataclass
from enum import Enum
import asyncio
import logging
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

class StageStatus(Enum):
    """Status of a processing stage"""
    NOT_STARTED = "not_started"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

@dataclass
class StageResult:
    """Result of a processing stage execution"""
    stage_name: str
    status: StageStatus
    output_data: Any
    execution_time: float
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None

class ProcessingStage(ABC):
    """Abstract base for configurable processing stages"""
    
    @abstractmethod
    async def process(self, input_data: Any, context: Dict[str, Any]) -> Any:
        """Process input data and return results"""
        pass
    
    @abstractmethod
    def get_dependencies(self) -> List[str]:
        """Return list of required predecessor stage names"""
        pass
    
    @abstractmethod
    def get_stage_name(self) -> str:
        """Return unique stage identifier"""
        pass
    
    def get_stage_description(self) -> str:
        """Return human-readable description of the stage"""
        return f"Processing stage: {self.get_stage_name()}"
    
    def can_run_parallel(self) -> bool:
        """Return True if this stage can run in parallel with others"""
        return True
    
    def get_required_input_keys(self) -> List[str]:
        """Return list of required keys in input data"""
        return []
    
    def validate_input(self, input_data: Any, context: Dict[str, Any]) -> bool:
        """Validate input data before processing"""
        return True

class ProcessingPipeline:
    """Configurable processing pipeline with dependency resolution"""
    
    def __init__(self, name: str = "default"):
        self.name = name
        self.stages: Dict[str, ProcessingStage] = {}
        self.execution_order: List[str] = []
        self.parallel_groups: List[List[str]] = []
        self._dependency_graph: Dict[str, Set[str]] = defaultdict(set)
        
    def register_stage(self, stage: ProcessingStage) -> None:
        """Register a processing stage"""
        stage_name = stage.get_stage_name()
        
        if stage_name in self.stages:
            logger.warning(f"Stage {stage_name} already registered, replacing...")
        
        self.stages[stage_name] = stage
        
        # Update dependency graph
        dependencies = stage.get_dependencies()
        self._dependency_graph[stage_name] = set(dependencies)
        
        # Recalculate execution order
        self._resolve_execution_order()
        
        logger.info(f"Registered stage: {stage_name} with dependencies: {dependencies}")
    
    def unregister_stage(self, stage_name: str) -> bool:
        """Unregister a processing stage"""
        if stage_name not in self.stages:
            return False
        
        # Check if other stages depend on this one
        dependents = self._find_dependents(stage_name)
        if dependents:
            logger.error(f"Cannot remove stage {stage_name}, required by: {dependents}")
            return False
        
        del self.stages[stage_name]
        del self._dependency_graph[stage_name]
        
        # Remove from other stages' dependencies
        for deps in self._dependency_graph.values():
            deps.discard(stage_name)
        
        self._resolve_execution_order()
        logger.info(f"Unregistered stage: {stage_name}")
        return True
    
    def _find_dependents(self, stage_name: str) -> List[str]:
        """Find stages that depend on the given stage"""
        dependents = []
        for stage, deps in self._dependency_graph.items():
            if stage_name in deps:
                dependents.append(stage)
        return dependents
    
    def _resolve_execution_order(self) -> None:
        """Resolve stage dependencies and determine execution order using topological sort"""
        if not self.stages:
            self.execution_order = []
            self.parallel_groups = []
            return
        
        # Validate all dependencies exist
        for stage_name, deps in self._dependency_graph.items():
            missing_deps = deps - set(self.stages.keys())
            if missing_deps:
                raise ValueError(f"Stage {stage_name} has missing dependencies: {missing_deps}")
        
        # Topological sort with parallel group detection
        in_degree = defaultdict(int)
        for stage_name in self.stages:
            in_degree[stage_name] = len(self._dependency_graph[stage_name])
        
        execution_order = []
        parallel_groups = []
        
        while in_degree:
            # Find all stages with no dependencies (can run in parallel)
            ready_stages = [stage for stage, degree in in_degree.items() if degree == 0]
            
            if not ready_stages:
                # Circular dependency detected
                remaining = list(in_degree.keys())
                raise ValueError(f"Circular dependency detected among stages: {remaining}")
            
            # Add parallel group
            parallel_groups.append(ready_stages)
            execution_order.extend(ready_stages)
            
            # Remove ready stages and update in-degrees
            for stage in ready_stages:
                del in_degree[stage]
                
                # Update in-degrees for dependent stages
                for dependent_stage in self.stages:
                    if stage in self._dependency_graph[dependent_stage]:
                        in_degree[dependent_stage] -= 1
        
        self.execution_order = execution_order
        self.parallel_groups = parallel_groups
        
        logger.info(f"Resolved execution order: {self.execution_order}")
        logger.info(f"Parallel groups: {self.parallel_groups}")
    
    async def execute_pipeline(self, input_data: Any, context: Optional[Dict[str, Any]] = None) -> Dict[str, StageResult]:
        """Execute all stages in dependency order"""
        if context is None:
            context = {}
        
        results: Dict[str, StageResult] = {}
        stage_outputs: Dict[str, Any] = {"input": input_data}
        
        logger.info(f"Starting pipeline execution: {self.name}")
        
        try:
            for parallel_group in self.parallel_groups:
                # Execute stages in parallel group
                if len(parallel_group) == 1:
                    # Single stage - execute directly
                    stage_name = parallel_group[0]
                    result = await self._execute_stage(stage_name, stage_outputs, context)
                    results[stage_name] = result
                    
                    if result.status == StageStatus.COMPLETED:
                        stage_outputs[stage_name] = result.output_data
                    elif result.status == StageStatus.FAILED:
                        logger.error(f"Stage {stage_name} failed: {result.error_message}")
                        break
                
                else:
                    # Multiple stages - execute in parallel
                    tasks = []
                    for stage_name in parallel_group:
                        task = self._execute_stage(stage_name, stage_outputs, context)
                        tasks.append((stage_name, task))
                    
                    # Wait for all parallel stages to complete
                    for stage_name, task in tasks:
                        result = await task
                        results[stage_name] = result
                        
                        if result.status == StageStatus.COMPLETED:
                            stage_outputs[stage_name] = result.output_data
                        elif result.status == StageStatus.FAILED:
                            logger.error(f"Parallel stage {stage_name} failed: {result.error_message}")
                    
                    # Check if any parallel stage failed
                    failed_stages = [name for name in parallel_group if results[name].status == StageStatus.FAILED]
                    if failed_stages:
                        logger.error(f"Parallel execution failed for stages: {failed_stages}")
                        break
        
        except Exception as e:
            logger.error(f"Pipeline execution failed: {e}")
            raise
        
        logger.info(f"Pipeline execution completed: {self.name}")
        return results
    
    async def _execute_stage(self, stage_name: str, stage_outputs: Dict[str, Any], context: Dict[str, Any]) -> StageResult:
        """Execute a single stage"""
        import time
        
        stage = self.stages[stage_name]
        start_time = time.time()
        
        try:
            logger.info(f"Executing stage: {stage_name}")
            
            # Validate dependencies are satisfied
            for dep in stage.get_dependencies():
                if dep not in stage_outputs:
                    raise ValueError(f"Dependency {dep} not satisfied for stage {stage_name}")
            
            # Validate input
            if not stage.validate_input(stage_outputs, context):
                raise ValueError(f"Input validation failed for stage {stage_name}")
            
            # Execute stage
            output_data = await stage.process(stage_outputs, context)
            execution_time = time.time() - start_time
            
            logger.info(f"Stage {stage_name} completed in {execution_time:.2f}s")
            
            return StageResult(
                stage_name=stage_name,
                status=StageStatus.COMPLETED,
                output_data=output_data,
                execution_time=execution_time
            )
        
        except Exception as e:
            execution_time = time.time() - start_time
            error_message = str(e)
            
            logger.error(f"Stage {stage_name} failed after {execution_time:.2f}s: {error_message}")
            
            return StageResult(
                stage_name=stage_name,
                status=StageStatus.FAILED,
                output_data=None,
                execution_time=execution_time,
                error_message=error_message
            )
    
    def get_pipeline_info(self) -> Dict[str, Any]:
        """Get information about the pipeline configuration"""
        return {
            "name": self.name,
            "total_stages": len(self.stages),
            "execution_order": self.execution_order,
            "parallel_groups": self.parallel_groups,
            "stage_details": {
                name: {
                    "description": stage.get_stage_description(),
                    "dependencies": stage.get_dependencies(),
                    "can_run_parallel": stage.can_run_parallel(),
                    "required_inputs": stage.get_required_input_keys()
                }
                for name, stage in self.stages.items()
            }
        }
    
    def validate_pipeline(self) -> Dict[str, Any]:
        """Validate the pipeline configuration"""
        issues = []
        warnings = []
        
        # Check for missing dependencies
        for stage_name, deps in self._dependency_graph.items():
            missing_deps = deps - set(self.stages.keys())
            if missing_deps:
                issues.append(f"Stage {stage_name} has missing dependencies: {missing_deps}")
        
        # Check for circular dependencies
        try:
            self._resolve_execution_order()
        except ValueError as e:
            issues.append(str(e))
        
        # Check for isolated stages
        isolated_stages = []
        for stage_name in self.stages:
            has_dependencies = bool(self._dependency_graph[stage_name])
            has_dependents = bool(self._find_dependents(stage_name))
            
            if not has_dependencies and not has_dependents and len(self.stages) > 1:
                isolated_stages.append(stage_name)
        
        if isolated_stages:
            warnings.append(f"Isolated stages (no dependencies or dependents): {isolated_stages}")
        
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "warnings": warnings,
            "total_stages": len(self.stages),
            "execution_groups": len(self.parallel_groups)
        }
