extends ../layout

block content
  .page-header
    h3 Sign in
    hr

  form.form-horizontal(method='POST')
    input(type='hidden', name='_csrf', value=_csrf)
    .form-group.row.justify-content-md-center
      label.col-sm-3.col-form-label.text-right.font-weight-bold(for='email') Email
      .col-sm-7
        input.form-control(type='email', name='email', id='email', placeholder='Email', autofocus, required)
    .form-group.row.justify-content-md-center
      label.col-sm-3.col-form-label.text-right.font-weight-bold(for='password') Password
      .col-sm-7
        input.form-control(type='password', name='password', id='password', placeholder='Password', required)
    .form-group.row.justify-content-md-center
      .offset-sm-3.col-sm-7
        button.col-sm-3.btn.btn-primary(type='submit')
          i.fa.fa-user
          | Login
        a.btn.btn-link(href='/forgot') Forgot your password?
    .form-group.row.justify-content-md-center
      .offset-sm-3.col-sm-7
        hr
    .form-group.row.justify-content-md-center
      .offset-sm-3.col-sm-7
        a.btn.btn-block.btn-facebook.btn-social(href='/auth/facebook')
          i.fa.fa-facebook
          | Sign in with Facebook
