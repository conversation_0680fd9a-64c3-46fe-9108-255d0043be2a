#!/usr/bin/env python3
"""
Test script to check embedding dimensions and compatibility
"""

import requests
import json

# Configuration
CODE_ANALYZER_SERVER_URL = "http://home-ai-server:5002"

def test_health_check():
    """Check server health and embedding configuration"""
    print("🏥 Checking server health and embedding configuration...")
    try:
        response = requests.get(f"{CODE_ANALYZER_SERVER_URL}/health", timeout=10)
        if response.status_code == 200:
            health = response.json()
            print("✅ Server is healthy")
            print(f"   Embedding provider: {health.get('embedding_provider', 'unknown')}")
            print(f"   Embedding dimensions: {health.get('embedding_dimensions', 'unknown')}")
            print(f"   Embedding test: {health.get('embedding_test', 'unknown')}")
            print(f"   Available models: {health.get('available_models', 'unknown')}")
            print(f"   Collections: {health.get('chromadb_collections', 'unknown')}")
            return health
        else:
            print(f"❌ Server health check failed: HTTP {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return None

def test_search_with_dimension_check():
    """Test search functionality and check for dimension errors"""
    print("\n🔍 Testing search functionality...")
    
    # First list codebases
    try:
        response = requests.post(f"{CODE_ANALYZER_SERVER_URL}/tools/list_codebases", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("📚 Available codebases:")
            print(data.get("result", "No result"))
        else:
            print(f"❌ Failed to list codebases: HTTP {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Error listing codebases: {e}")
        return
    
    # Ask user which codebase to test
    codebase_name = input("\n🔍 Enter the name of a codebase to test search (or 'skip' to skip): ").strip()
    
    if codebase_name.lower() == 'skip' or not codebase_name:
        print("⏭️ Skipping search test")
        return
    
    # Test search
    print(f"\n🔍 Testing search on codebase: {codebase_name}")
    try:
        payload = {
            "query": "function definition",
            "codebase_name": codebase_name,
            "n_results": 3
        }
        
        response = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/search_code",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            result = data.get("result", "No result")
            
            if "dimension" in result.lower() and "mismatch" in result.lower():
                print("❌ EMBEDDING DIMENSION MISMATCH DETECTED!")
                print(f"   Error: {result}")
                print("\n🔧 SOLUTION:")
                print(f"   1. Delete the codebase: delete_codebase('{codebase_name}')")
                print(f"   2. Re-process it: process_codebase('{codebase_name}')")
                print("   This will recreate the collection with correct embedding dimensions.")
            else:
                print("✅ Search completed successfully")
                # Show first few lines of result
                lines = result.split('\n')[:5]
                for line in lines:
                    print(f"   {line}")
                if len(result.split('\n')) > 5:
                    print("   ...")
        else:
            print(f"❌ Search failed: HTTP {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Error during search: {e}")

def show_embedding_info():
    """Show information about embedding dimensions and compatibility"""
    print("\n📊 EMBEDDING DIMENSION INFORMATION:")
    print("=" * 50)
    print("🔍 Common Embedding Models and Their Dimensions:")
    print("   • nomic-embed-text (Ollama): 384 dimensions")
    print("   • ChromaDB default: 384 dimensions")
    print("   • text-embedding-ada-002 (OpenAI): 1536 dimensions")
    print("   • all-MiniLM-L6-v2: 384 dimensions")
    print("")
    print("⚠️ DIMENSION MISMATCH ISSUES:")
    print("   • Collections created with different embedding models")
    print("   • Switching between Ollama and ChromaDB default embeddings")
    print("   • Using different Ollama models")
    print("")
    print("🔧 SOLUTIONS:")
    print("   1. Delete affected codebase: delete_codebase('name')")
    print("   2. Re-process with current settings: process_codebase('name')")
    print("   3. Ensure consistent embedding configuration")
    print("")
    print("✅ PREVENTION:")
    print("   • Use consistent embedding settings across all collections")
    print("   • Check health endpoint for current embedding dimensions")
    print("   • Test search after changing embedding configuration")

if __name__ == "__main__":
    print("🚀 Code Analysis Server Embedding Dimension Test")
    print("=" * 50)
    
    # Check server health and embedding config
    health = test_health_check()
    if not health:
        print("❌ Server is not available. Please ensure the Code Analysis server is running.")
        exit(1)
    
    # Test search functionality
    test_search_with_dimension_check()
    
    # Show embedding information
    show_embedding_info()
    
    print("\n✅ Test completed!")
    print("\n💡 TIP: If you encounter dimension mismatch errors,")
    print("   use the delete_codebase() and process_codebase() functions")
    print("   to recreate collections with correct embedding dimensions.")
