#!/usr/bin/env python3
"""
Test the condition order fix for stats queries
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_condition_order_fix():
    """Test that stats queries with 'show' don't get routed to list_codebases"""
    print("🧪 Testing Condition Order Fix")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test queries that previously would have been misrouted
    test_queries = [
        ("show stats for utils", "Should get stats, not list codebases"),
        ("show statistics for utils", "Should get stats, not list codebases"),
        ("get stats for utils", "Should get stats"),
        ("list codebases", "Should list codebases"),
        ("show available codebases", "Should list codebases"),
    ]
    
    results = []
    
    for query, expected in test_queries:
        print(f"\n🔍 Testing: '{query}'")
        print(f"Expected: {expected}")
        print("-" * 50)
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": query}],
                    "tool_ids": ["code_analyzer_tool"],
                    "stream": False,
                    "max_tokens": 1000
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                print(f"✅ Response received ({len(content)} chars)")
                
                # Check what type of response we got
                list_indicators = [
                    "Here is the list of codebases:",
                    "1. **utils**",
                    "2. **test_project**",
                    "available codebases are:"
                ]
                
                stats_indicators = [
                    "Total chunks: 479",
                    "Unique files: 43",
                    "Basic Codebase Statistics",
                    "C: 460",
                    "No codebase selected for statistics"
                ]
                
                generic_indicators = [
                    "I'll help you",
                    "Here are some statistics",
                    "blast from the past"
                ]
                
                found_list = [ind for ind in list_indicators if ind in content]
                found_stats = [ind for ind in stats_indicators if ind in content]
                found_generic = [ind for ind in generic_indicators if ind in content]
                
                # Determine what we actually got
                if found_list and not found_stats:
                    actual_type = "list_codebases"
                    print(f"📋 GOT LIST RESPONSE: {found_list[:1]}")
                elif found_stats and not found_list:
                    actual_type = "stats_response"
                    print(f"📊 GOT STATS RESPONSE: {found_stats[:1]}")
                elif found_generic:
                    actual_type = "generic_response"
                    print(f"⚠️ GOT GENERIC RESPONSE: {found_generic[:1]}")
                else:
                    actual_type = "unknown"
                    print("❓ UNKNOWN RESPONSE TYPE")
                
                # Check if routing is correct
                if "show stats" in query or "get stats" in query:
                    # Should get stats response
                    if actual_type == "stats_response":
                        print("🎉 CORRECT: Stats query → Stats response")
                        status = "correct"
                    elif actual_type == "list_codebases":
                        print("❌ WRONG: Stats query → List response (condition order bug)")
                        status = "wrong_routing"
                    else:
                        print("⚠️ UNEXPECTED: Stats query → Generic response")
                        status = "unexpected"
                elif "list" in query or ("show" in query and "available" in query):
                    # Should get list response
                    if actual_type == "list_codebases":
                        print("🎉 CORRECT: List query → List response")
                        status = "correct"
                    else:
                        print("⚠️ UNEXPECTED: List query → Non-list response")
                        status = "unexpected"
                else:
                    status = "unclear"
                
                results.append({
                    "query": query,
                    "expected_type": "stats" if "stats" in query else "list",
                    "actual_type": actual_type,
                    "status": status
                })
                
                # Show preview
                preview = content[:150] + "..." if len(content) > 150 else content
                print(f"Preview: {preview}")
                
            else:
                print(f"❌ HTTP {response.status_code}")
                results.append({"query": query, "status": "error"})
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append({"query": query, "status": "error"})
    
    return results

def analyze_routing_results(results):
    """Analyze the routing test results"""
    print(f"\n📊 CONDITION ORDER FIX ANALYSIS")
    print("=" * 60)
    
    valid_results = [r for r in results if r.get("status") != "error"]
    correct_routing = len([r for r in valid_results if r.get("status") == "correct"])
    wrong_routing = len([r for r in valid_results if r.get("status") == "wrong_routing"])
    
    print(f"📈 Routing Results:")
    print(f"  ✅ Correct routing: {correct_routing}/{len(valid_results)}")
    print(f"  ❌ Wrong routing: {wrong_routing}/{len(valid_results)}")
    
    print(f"\n📋 Query-by-Query Analysis:")
    for result in valid_results:
        query = result.get("query", "")[:30] + "..." if len(result.get("query", "")) > 30 else result.get("query", "")
        status = result.get("status", "unknown")
        actual = result.get("actual_type", "unknown")
        status_emoji = {"correct": "✅", "wrong_routing": "❌", "unexpected": "⚠️", "unclear": "❓"}.get(status, "❓")
        print(f"  {status_emoji} {query} → {actual}")
    
    # Overall assessment
    if wrong_routing == 0:
        print(f"\n🎉 EXCELLENT: Condition order fix working!")
        print("✅ No stats queries misrouted to list_codebases")
        print("✅ Routing logic is working correctly")
    elif wrong_routing <= 1:
        print(f"\n👍 GOOD: Major improvement in routing")
        print("✅ Most queries routed correctly")
        print("⚠️ Minor edge cases remain")
    else:
        print(f"\n❌ ISSUE: Condition order still problematic")
        print("🔧 Need to update tool in OpenWebUI")

def main():
    """Main test function"""
    print("🔧 Condition Order Fix Test")
    print("=" * 70)
    print("Testing that stats queries don't get misrouted to list_codebases")
    
    # Test the condition order fix
    results = test_condition_order_fix()
    
    # Analyze results
    analyze_routing_results(results)
    
    print(f"\n🎯 Key Fix:")
    print("Moved stats condition BEFORE list condition to prevent misrouting")
    print("'show stats for utils' should now get stats, not list codebases")

if __name__ == "__main__":
    main()
