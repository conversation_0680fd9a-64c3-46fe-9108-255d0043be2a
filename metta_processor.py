"""
Specialized Language Processor for Metta (OpenCog Hyperon)
Example of how to create a specialized processor for advanced language support
"""

import re
from pathlib import Path
from typing import Dict, List, Set, Any
from language_framework import LanguageProcessor, FileRelationship, LanguageContext, AnalysisScope
from semantic_patterns import semantic_registry
import logging

logger = logging.getLogger(__name__)

class MettaProcessor(LanguageProcessor):
    """Specialized processor for Metta language with advanced AI/symbolic features"""
    
    def get_language_name(self) -> str:
        return "metta"
    
    def get_supported_extensions(self) -> Set[str]:
        return {'.metta', '.mta'}
    
    def get_processing_priority(self) -> int:
        return 2  # High priority for specialized processing
    
    def detect_file_relationships(self, file_path: str) -> List[FileRelationship]:
        """Handle Metta module and import relationships"""
        relationships = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            imports = self._extract_imports(content)
            spaces = self._extract_spaces(content)
            
            if imports or spaces:
                relationships.append(FileRelationship(
                    file_path=file_path,
                    relationship_type="metta_dependencies",
                    related_files=self._resolve_metta_imports(file_path, imports),
                    analysis_scope=AnalysisScope.MODULE_GROUP,
                    metadata={
                        "imports": imports,
                        "spaces": spaces,
                        "metta_type": self._determine_metta_type(content)
                    }
                ))
        
        except Exception as e:
            logger.error(f"Error detecting Metta relationships for {file_path}: {e}")
        
        return relationships
    
    def extract_context(self, file_path: str, content: str) -> LanguageContext:
        """Extract Metta-specific context with AI/symbolic analysis"""
        
        # Use comprehensive semantic patterns
        semantic_elements = semantic_registry.extract_semantic_elements(content, "metta")
        
        metadata = {
            "functions": semantic_elements.get("function_patterns", []),
            "types": semantic_elements.get("class_patterns", []),  # Types in Metta
            "imports": semantic_elements.get("import_patterns", []),
            "variables": semantic_elements.get("variable_patterns", []),
            "spaces": self._extract_spaces(content),
            "atoms": self._extract_atoms(content),
            "expressions": self._extract_expressions(content),
            "pattern_matches": self._extract_pattern_matches(content),
            "knowledge_base_elements": self._extract_kb_elements(content),
            "ai_constructs": self._identify_ai_constructs(content),
            "symbolic_complexity": self._calculate_symbolic_complexity(content),
            "hyperon_features": self._identify_hyperon_features(content)
        }
        
        relationships = self.detect_file_relationships(file_path)
        
        return LanguageContext(
            language="metta",
            file_path=file_path,
            content=content,
            relationships=relationships,
            metadata=metadata
        )
    
    def generate_architectural_insights(self, contexts: List[LanguageContext]) -> Dict[str, Any]:
        """Generate Metta-specific architectural insights for AI systems"""
        insights: Dict[str, Any] = {
            "knowledge_spaces": [],
            "symbolic_hierarchies": [],
            "ai_reasoning_patterns": [],
            "hyperon_architecture": {},
            "cognitive_modules": []
        }
        
        for context in contexts:
            if context.language == "metta":
                # Analyze knowledge spaces
                spaces = context.metadata.get("spaces", [])
                if spaces:
                    insights["knowledge_spaces"].extend([
                        {"file": context.file_path, "spaces": spaces}
                    ])
                
                # Analyze AI constructs
                ai_constructs = context.metadata.get("ai_constructs", [])
                if ai_constructs:
                    insights["ai_reasoning_patterns"].append({
                        "file": context.file_path,
                        "constructs": ai_constructs
                    })
                
                # Analyze Hyperon features
                hyperon_features = context.metadata.get("hyperon_features", [])
                if hyperon_features:
                    insights["hyperon_architecture"][context.file_path] = hyperon_features
        
        return insights
    
    def _extract_imports(self, content: str) -> List[str]:
        """Extract Metta import statements"""
        import_patterns = [
            r'\(\s*import!\s+([^\s)]+)',  # (import! module)
            r'\(\s*include\s+([^\s)]+)',  # (include file)
            r'\(\s*load\s+([^\s)]+)',     # (load file)
            r'\(\s*use\s+([^\s)]+)'       # (use module)
        ]
        
        imports = []
        for pattern in import_patterns:
            imports.extend(re.findall(pattern, content))
        
        return list(set(imports))
    
    def _extract_spaces(self, content: str) -> List[str]:
        """Extract Metta space definitions"""
        space_patterns = [
            r'\(\s*new-space\s+([^\s)]+)',  # (new-space name)
            r'\(\s*space\s+([^\s)]+)',      # (space name)
            r'\(\s*add-atom\s+([^\s)]+)',   # (add-atom space ...)
        ]
        
        spaces = []
        for pattern in space_patterns:
            spaces.extend(re.findall(pattern, content))
        
        return list(set(spaces))
    
    def _extract_atoms(self, content: str) -> List[str]:
        """Extract Metta atom definitions"""
        atom_patterns = [
            r'\(\s*:\s+([^\s)]+)\s+Atom\)',  # (: name Atom)
            r'\(\s*add-atom\s+[^\s)]+\s+([^\s)]+)',  # (add-atom space atom)
        ]
        
        atoms = []
        for pattern in atom_patterns:
            atoms.extend(re.findall(pattern, content))
        
        return list(set(atoms))
    
    def _extract_expressions(self, content: str) -> List[str]:
        """Extract Metta expressions and S-expressions"""
        # Count nested expressions
        expressions = []
        depth = 0
        current_expr = ""
        
        for char in content:
            if char == '(':
                if depth == 0:
                    current_expr = "("
                else:
                    current_expr += char
                depth += 1
            elif char == ')':
                depth -= 1
                current_expr += char
                if depth == 0 and len(current_expr) > 2:
                    expressions.append(current_expr.strip())
                    current_expr = ""
            elif depth > 0:
                current_expr += char
        
        return expressions[:50]  # Limit to first 50 expressions
    
    def _extract_pattern_matches(self, content: str) -> List[str]:
        """Extract pattern matching constructs"""
        pattern_patterns = [
            r'\(\s*match\s+([^)]+)\)',      # (match pattern)
            r'\(\s*case\s+([^)]+)\)',       # (case pattern)
            r'\(\s*if\s+([^)]+)\)',         # (if condition)
        ]
        
        patterns = []
        for pattern in pattern_patterns:
            patterns.extend(re.findall(pattern, content))
        
        return patterns
    
    def _extract_kb_elements(self, content: str) -> List[str]:
        """Extract knowledge base elements"""
        kb_patterns = [
            r'\(\s*fact\s+([^)]+)\)',       # (fact ...)
            r'\(\s*rule\s+([^)]+)\)',       # (rule ...)
            r'\(\s*query\s+([^)]+)\)',      # (query ...)
            r'\(\s*belief\s+([^)]+)\)',     # (belief ...)
        ]
        
        elements = []
        for pattern in kb_patterns:
            elements.extend(re.findall(pattern, content))
        
        return elements
    
    def _identify_ai_constructs(self, content: str) -> List[str]:
        """Identify AI-specific constructs in Metta code"""
        constructs = []
        
        ai_keywords = {
            'reasoning': ['infer', 'deduce', 'conclude', 'reason'],
            'learning': ['learn', 'train', 'adapt', 'update'],
            'planning': ['plan', 'goal', 'action', 'strategy'],
            'perception': ['perceive', 'sense', 'observe', 'detect'],
            'memory': ['remember', 'recall', 'store', 'retrieve'],
            'attention': ['focus', 'attend', 'select', 'filter'],
            'cognition': ['think', 'process', 'analyze', 'understand']
        }
        
        content_lower = content.lower()
        for category, keywords in ai_keywords.items():
            if any(keyword in content_lower for keyword in keywords):
                constructs.append(category)
        
        return constructs
    
    def _calculate_symbolic_complexity(self, content: str) -> str:
        """Calculate symbolic complexity based on Metta constructs"""
        expressions = self._extract_expressions(content)
        atoms = self._extract_atoms(content)
        spaces = self._extract_spaces(content)
        
        total_complexity = len(expressions) + len(atoms) * 2 + len(spaces) * 3
        
        if total_complexity < 10:
            return "low"
        elif total_complexity < 50:
            return "medium"
        else:
            return "high"
    
    def _identify_hyperon_features(self, content: str) -> List[str]:
        """Identify OpenCog Hyperon specific features"""
        features = []
        
        hyperon_keywords = [
            'atomspace', 'hypergraph', 'metagraph', 'distributed',
            'neural-symbolic', 'probabilistic', 'temporal',
            'causal', 'hierarchical', 'emergent'
        ]
        
        content_lower = content.lower()
        for keyword in hyperon_keywords:
            if keyword in content_lower:
                features.append(keyword.replace('-', '_'))
        
        return features
    
    def _resolve_metta_imports(self, file_path: str, imports: List[str]) -> List[str]:
        """Resolve Metta import statements to actual file paths"""
        resolved_paths = []
        base_dir = Path(file_path).parent
        
        for imp in imports:
            # Remove quotes if present
            clean_import = imp.strip('"\'')
            
            # Try different extensions
            for ext in ['.metta', '.mta']:
                potential_path = base_dir / f"{clean_import}{ext}"
                if potential_path.exists():
                    resolved_paths.append(str(potential_path))
                    break
        
        return resolved_paths
    
    def _determine_metta_type(self, content: str) -> str:
        """Determine the type of Metta file"""
        if any(keyword in content.lower() for keyword in ['space', 'atomspace']):
            return "knowledge_base"
        elif any(keyword in content.lower() for keyword in ['rule', 'infer']):
            return "reasoning_module"
        elif any(keyword in content.lower() for keyword in ['learn', 'train']):
            return "learning_module"
        elif any(keyword in content.lower() for keyword in ['query', 'search']):
            return "query_module"
        else:
            return "general_module"
