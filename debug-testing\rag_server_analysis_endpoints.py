#!/usr/bin/env python3
"""
Phase 2: RAG Server Analysis Endpoints
Comprehensive endpoints for dynamic codebase analysis and enhancement.
"""

from flask import Flask, request, jsonify, Blueprint
import json
import os
import asyncio
from typing import Dict, List, Optional
import logging
from datetime import datetime
from vector_db_interface import VectorDBInterface, create_vector_db_interface

class CodebaseAnalysisService:
    """Service for managing codebase analysis and enhancement patterns"""
    
    def __init__(self, vector_db: VectorDBInterface, cache_dir: str = "./analysis_cache"):
        self.vector_db = vector_db
        self.cache_dir = cache_dir
        self.logger = logging.getLogger(__name__)
        
        # Ensure cache directory exists
        os.makedirs(cache_dir, exist_ok=True)
        
        # In-memory cache for patterns
        self.patterns_cache = {}
    
    def get_cache_filename(self, codebase_name: str) -> str:
        """Get cache filename for codebase patterns"""
        return os.path.join(self.cache_dir, f"{codebase_name}_patterns.json")
    
    async def analyze_codebase(self, codebase_name: str, force_refresh: bool = False) -> Dict:
        """Analyze a codebase and build enhancement patterns"""
        try:
            cache_filename = self.get_cache_filename(codebase_name)
            
            # Check if we have cached patterns and they're not stale
            if not force_refresh and os.path.exists(cache_filename):
                try:
                    with open(cache_filename, 'r') as f:
                        cached_patterns = json.load(f)
                    
                    # Check if cache is recent (less than 24 hours old)
                    cache_time = cached_patterns.get('analysis_metadata', {}).get('analyzed_at')
                    if cache_time:
                        cache_datetime = datetime.fromisoformat(cache_time)
                        age_hours = (datetime.now() - cache_datetime).total_seconds() / 3600
                        
                        if age_hours < 24:  # Cache is fresh
                            self.patterns_cache[codebase_name] = cached_patterns
                            self.logger.info(f"Using cached patterns for {codebase_name}")
                            return {
                                'status': 'success',
                                'source': 'cache',
                                'codebase': codebase_name,
                                'patterns': cached_patterns
                            }
                except Exception as e:
                    self.logger.warning(f"Failed to load cached patterns: {e}")
            
            # Fetch all chunks for analysis
            self.logger.info(f"Starting fresh analysis for {codebase_name}")
            all_chunks = await self._fetch_all_chunks(codebase_name)
            
            if not all_chunks:
                return {
                    'status': 'error',
                    'error': f'No chunks found for codebase: {codebase_name}'
                }
            
            # Analyze chunks to build patterns
            patterns = await self._analyze_chunks(all_chunks, codebase_name)
            
            # Cache the patterns
            with open(cache_filename, 'w') as f:
                json.dump(patterns, f, indent=2, default=str)
            
            # Store in memory cache
            self.patterns_cache[codebase_name] = patterns
            
            self.logger.info(f"Analysis complete for {codebase_name}: {len(patterns.get('functions', []))} functions")
            
            return {
                'status': 'success',
                'source': 'fresh_analysis',
                'codebase': codebase_name,
                'patterns': patterns
            }
            
        except Exception as e:
            self.logger.error(f"Analysis failed for {codebase_name}: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _fetch_all_chunks(self, codebase_name: str) -> List[Dict]:
        """Fetch all chunks for a codebase"""
        all_chunks = []
        offset = 0
        limit = 1000
        
        while True:
            chunks = self.vector_db.get_all_chunks_for_codebase(
                codebase_name, offset=offset, limit=limit
            )
            
            if not chunks:
                break
            
            all_chunks.extend(chunks)
            
            if len(chunks) < limit:  # Last batch
                break
            
            offset += limit
        
        self.logger.info(f"Fetched {len(all_chunks)} total chunks for {codebase_name}")
        return all_chunks
    
    async def _analyze_chunks(self, chunks: List[Dict], codebase_name: str) -> Dict:
        """Analyze chunks to build enhancement patterns"""
        from collections import defaultdict, Counter
        import re
        
        patterns = {
            'functions': [],
            'domains': defaultdict(list),
            'keywords': set(),
            'prefixes': defaultdict(list),
            'types': set(),
            'constants': set(),
            'usage_frequency': defaultdict(int),
            'semantic_clusters': defaultdict(list),
            'cross_references': defaultdict(set),
            'enhancement_rules': {},
            'analysis_metadata': {
                'analyzed_at': datetime.now().isoformat(),
                'chunk_count': len(chunks),
                'codebase_name': codebase_name
            }
        }
        
        # Analyze each chunk
        for chunk in chunks:
            content = chunk.get('content', '')
            metadata = chunk.get('metadata', {})
            
            # Extract functions
            functions = self._extract_functions(content)
            patterns['functions'].extend(functions)
            
            # Extract keywords
            keywords = self._extract_keywords(content)
            patterns['keywords'].update(keywords)
            
            # Extract types and constants
            types = self._extract_types(content)
            patterns['types'].update(types)
            
            constants = self._extract_constants(content)
            patterns['constants'].update(constants)
            
            # Use semantic tags from metadata if available
            semantic_tags = metadata.get('semantic_tags', [])
            for tag in semantic_tags:
                if tag in ['memory_management', 'error_handling', 'network_operations', 
                          'timer_operations', 'io_operations', 'configuration']:
                    patterns['domains'][tag].extend(functions)
        
        # Build domain patterns based on prefixes
        self._build_domain_patterns(patterns)
        
        # Build enhancement rules
        self._build_enhancement_rules(patterns)
        
        # Build semantic clusters
        self._build_semantic_clusters(patterns)
        
        # Calculate usage frequency
        function_counts = Counter(patterns['functions'])
        patterns['usage_frequency'] = dict(function_counts)
        
        # Convert sets to lists for JSON serialization
        patterns['keywords'] = list(patterns['keywords'])
        patterns['types'] = list(patterns['types'])
        patterns['constants'] = list(patterns['constants'])
        patterns['domains'] = dict(patterns['domains'])
        patterns['prefixes'] = dict(patterns['prefixes'])
        patterns['semantic_clusters'] = dict(patterns['semantic_clusters'])
        patterns['cross_references'] = {k: list(v) for k, v in patterns['cross_references'].items()}
        
        return patterns
    
    def _extract_functions(self, content: str) -> List[str]:
        """Extract function names from code content"""
        functions = []
        
        # C/C++ function patterns
        patterns = [
            r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(',  # Function calls
            r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)\s*{',  # Function definitions
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content)
            functions.extend(matches)
        
        # Filter out common keywords
        keywords_to_exclude = {
            'if', 'for', 'while', 'switch', 'return', 'sizeof', 'malloc', 'free',
            'printf', 'scanf', 'strlen', 'strcpy', 'strcmp', 'memcpy', 'memset'
        }
        
        functions = [f for f in functions if f not in keywords_to_exclude and len(f) > 2]
        return list(set(functions))
    
    def _extract_keywords(self, content: str) -> set:
        """Extract relevant keywords and identifiers"""
        keywords = set()
        identifiers = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', content)
        
        common_keywords = {
            'void', 'char', 'int', 'long', 'short', 'float', 'double', 'const',
            'static', 'extern', 'auto', 'register', 'volatile', 'signed', 'unsigned',
            'struct', 'union', 'enum', 'typedef', 'sizeof', 'return', 'break',
            'continue', 'goto', 'case', 'default', 'switch', 'else', 'while',
            'for', 'do', 'if', 'NULL', 'true', 'false'
        }
        
        for identifier in identifiers:
            if len(identifier) > 3 and identifier.lower() not in common_keywords:
                keywords.add(identifier)
        
        return keywords
    
    def _extract_types(self, content: str) -> set:
        """Extract type definitions"""
        types = set()
        patterns = [
            r'typedef\s+struct\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            r'typedef\s+enum\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            r'struct\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            r'enum\s+([a-zA-Z_][a-zA-Z0-9_]*)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content)
            types.update(matches)
        
        return types
    
    def _extract_constants(self, content: str) -> set:
        """Extract constant definitions"""
        constants = set()
        defines = re.findall(r'#define\s+([A-Z_][A-Z0-9_]*)', content)
        constants.update(defines)
        enum_values = re.findall(r'\b([A-Z_][A-Z0-9_]*)\s*[,}]', content)
        constants.update(enum_values)
        return constants
    
    def _build_domain_patterns(self, patterns: Dict):
        """Build domain patterns based on function prefixes"""
        prefix_domains = {
            'tmwmem': 'memory_management',
            'tmwerr': 'error_handling', 
            'tmwtimer': 'timer_operations',
            'tmwlink': 'network_operations',
            'tmwphys': 'network_operations',
            'tmwcnfg': 'configuration',
            'tmwcrypto': 'cryptography',
            'tmwsim': 'simulation'
        }
        
        for function in patterns['functions']:
            for prefix, domain in prefix_domains.items():
                if function.startswith(prefix):
                    patterns['domains'][domain].append(function)
                    patterns['prefixes'][prefix].append(function)
    
    def _build_enhancement_rules(self, patterns: Dict):
        """Build enhancement rules based on discovered patterns"""
        patterns['enhancement_rules'] = {}
        
        for domain, functions in patterns['domains'].items():
            if functions:
                unique_functions = list(set(functions))[:6]
                patterns['enhancement_rules'][domain] = unique_functions
    
    def _build_semantic_clusters(self, patterns: Dict):
        """Build semantic clusters of related functions"""
        for function in patterns['functions']:
            if function.endswith('_init') or function.endswith('Init'):
                patterns['semantic_clusters']['initialization'].append(function)
            elif function.endswith('_free') or function.endswith('_destroy'):
                patterns['semantic_clusters']['cleanup'].append(function)
            elif function.endswith('_callback') or function.endswith('Callback'):
                patterns['semantic_clusters']['callbacks'].append(function)
    
    def get_enhancement_for_query(self, codebase_name: str, query: str) -> List[str]:
        """Get enhancement terms for a query using cached patterns"""
        if codebase_name not in self.patterns_cache:
            return []
        
        patterns = self.patterns_cache[codebase_name]
        query_lower = query.lower()
        enhancements = []
        
        # Domain mappings
        domain_mappings = {
            'memory': 'memory_management',
            'error': 'error_handling', 
            'timer': 'timer_operations',
            'network': 'network_operations',
            'socket': 'network_operations',
            'config': 'configuration',
            'crypto': 'cryptography'
        }
        
        # Find matching domain
        for keyword, domain in domain_mappings.items():
            if keyword in query_lower:
                domain_functions = patterns.get('enhancement_rules', {}).get(domain, [])
                enhancements.extend(domain_functions[:4])
                break
        
        return enhancements

def create_analysis_blueprint(analysis_service: CodebaseAnalysisService) -> Blueprint:
    """Create Flask blueprint with analysis endpoints"""
    
    bp = Blueprint('codebase_analysis', __name__)
    
    @bp.route('/codebases/<codebase_name>/all_chunks', methods=['GET'])
    def get_all_chunks(codebase_name: str):
        """Get ALL chunks for a codebase for analysis purposes"""
        try:
            offset = int(request.args.get('offset', 0))
            limit = int(request.args.get('limit', 1000))
            
            chunks = analysis_service.vector_db.get_all_chunks_for_codebase(
                codebase_name=codebase_name,
                offset=offset,
                limit=limit
            )
            
            total_chunks = analysis_service.vector_db.count_chunks_for_codebase(codebase_name)
            
            return jsonify({
                'codebase': codebase_name,
                'chunks': chunks,
                'pagination': {
                    'offset': offset,
                    'limit': limit,
                    'total': total_chunks,
                    'has_more': offset + limit < total_chunks
                }
            })
            
        except Exception as e:
            return jsonify({'error': f'Failed to get chunks: {str(e)}'}), 500
    
    @bp.route('/codebases/<codebase_name>/analyze', methods=['POST'])
    def analyze_codebase(codebase_name: str):
        """Analyze a codebase and build enhancement patterns"""
        try:
            force_refresh = request.json.get('force_refresh', False) if request.json else False
            
            # Run analysis (this is async, so we need to handle it properly)
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(analysis_service.analyze_codebase(codebase_name, force_refresh))
            loop.close()
            
            if result['status'] == 'success':
                patterns = result['patterns']
                return jsonify({
                    'status': 'success',
                    'source': result['source'],
                    'codebase': codebase_name,
                    'functions_discovered': len(patterns.get('functions', [])),
                    'domains_identified': list(patterns.get('domains', {}).keys()),
                    'enhancement_rules': len(patterns.get('enhancement_rules', {})),
                    'analysis_metadata': patterns.get('analysis_metadata', {})
                })
            else:
                return jsonify(result), 500
                
        except Exception as e:
            return jsonify({'error': f'Analysis failed: {str(e)}'}), 500
    
    @bp.route('/codebases/<codebase_name>/patterns', methods=['GET'])
    def get_patterns(codebase_name: str):
        """Get enhancement patterns for a codebase"""
        try:
            if codebase_name in analysis_service.patterns_cache:
                patterns = analysis_service.patterns_cache[codebase_name]
            else:
                # Try to load from cache file
                cache_filename = analysis_service.get_cache_filename(codebase_name)
                if os.path.exists(cache_filename):
                    with open(cache_filename, 'r') as f:
                        patterns = json.load(f)
                    analysis_service.patterns_cache[codebase_name] = patterns
                else:
                    return jsonify({'error': f'No patterns found for {codebase_name}. Run analysis first.'}), 404
            
            return jsonify({
                'codebase': codebase_name,
                'patterns': patterns
            })
            
        except Exception as e:
            return jsonify({'error': f'Failed to get patterns: {str(e)}'}), 500
    
    @bp.route('/enhance_query', methods=['POST'])
    def enhance_query():
        """Enhance a query using dynamic patterns"""
        try:
            data = request.get_json()
            query = data.get('query', '')
            codebase_name = data.get('codebase_name', '')
            
            if not query or not codebase_name:
                return jsonify({'error': 'Query and codebase_name are required'}), 400
            
            enhancements = analysis_service.get_enhancement_for_query(codebase_name, query)
            enhanced_query = f"{query} {' '.join(enhancements)}" if enhancements else query
            
            return jsonify({
                'original_query': query,
                'enhanced_query': enhanced_query,
                'enhancements': enhancements,
                'codebase': codebase_name
            })
            
        except Exception as e:
            return jsonify({'error': f'Enhancement failed: {str(e)}'}), 500
    
    return bp

# Phase 3: Complete Server Integration
def integrate_with_existing_server(app: Flask, vector_db_config: Dict):
    """
    Phase 3: Complete integration with existing RAG server
    This function should be called from your main server file
    """

    # Initialize vector database interface
    vector_db = create_vector_db_interface(**vector_db_config)

    # Initialize analysis service
    analysis_service = CodebaseAnalysisService(vector_db)

    # Register the analysis blueprint
    analysis_bp = create_analysis_blueprint(analysis_service)
    app.register_blueprint(analysis_bp, url_prefix='/api/v1')

    # Add health check for analysis service
    @app.route('/analysis/health', methods=['GET'])
    def analysis_health():
        """Health check for analysis service"""
        return jsonify({
            'status': 'healthy',
            'service': 'codebase_analysis',
            'version': '1.0.0',
            'features': [
                'chunk_retrieval',
                'pattern_analysis',
                'query_enhancement',
                'semantic_clustering'
            ]
        })

    # Add analysis status endpoint
    @app.route('/analysis/status', methods=['GET'])
    def analysis_status():
        """Get status of all analyzed codebases"""
        try:
            cached_codebases = list(analysis_service.patterns_cache.keys())

            status_info = []
            for codebase in cached_codebases:
                patterns = analysis_service.patterns_cache[codebase]
                metadata = patterns.get('analysis_metadata', {})

                status_info.append({
                    'codebase': codebase,
                    'analyzed_at': metadata.get('analyzed_at'),
                    'chunk_count': metadata.get('chunk_count', 0),
                    'functions_count': len(patterns.get('functions', [])),
                    'domains_count': len(patterns.get('domains', {})),
                    'status': 'ready'
                })

            return jsonify({
                'analyzed_codebases': len(cached_codebases),
                'codebases': status_info
            })

        except Exception as e:
            return jsonify({'error': f'Failed to get status: {str(e)}'}), 500

    return analysis_service

# Example integration script for existing RAG server
def example_integration():
    """
    Example of how to integrate with existing RAG server
    """

    # This would be added to your main server file
    app = Flask(__name__)

    # Configure your vector database
    vector_db_config = {
        'db_type': 'chromadb',  # or 'pinecone', 'weaviate'
        'client': None,  # Your actual vector DB client
        'logger': logging.getLogger(__name__)
    }

    # For ChromaDB example:
    # import chromadb
    # vector_db_config = {
    #     'db_type': 'chromadb',
    #     'client': chromadb.Client(),
    #     'logger': logging.getLogger(__name__)
    # }

    # For Pinecone example:
    # import pinecone
    # vector_db_config = {
    #     'db_type': 'pinecone',
    #     'client': pinecone,
    #     'index_name': 'your-index-name',
    #     'logger': logging.getLogger(__name__)
    # }

    # Integrate analysis service
    analysis_service = integrate_with_existing_server(app, vector_db_config)

    # Your existing endpoints would go here...

    print("🚀 RAG Server with Dynamic Analysis Integration")
    print("📋 New Analysis Endpoints:")
    print("   GET  /api/v1/codebases/<name>/all_chunks - Get all chunks")
    print("   POST /api/v1/codebases/<name>/analyze - Analyze codebase")
    print("   GET  /api/v1/codebases/<name>/patterns - Get patterns")
    print("   POST /api/v1/enhance_query - Enhance query")
    print("   GET  /analysis/health - Analysis health check")
    print("   GET  /analysis/status - Analysis status")

    return app, analysis_service

if __name__ == "__main__":
    # Example standalone server for testing
    app, analysis_service = example_integration()
    app.run(host='0.0.0.0', port=5002, debug=True)
