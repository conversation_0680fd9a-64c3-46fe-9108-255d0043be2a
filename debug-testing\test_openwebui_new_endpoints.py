#!/usr/bin/env python3
"""
Test that the OpenWebUI tool is using the new server endpoints
"""

import asyncio
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock
import requests

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

from open_webui_code_analyzer_tool import Tools

async def test_openwebui_new_endpoints():
    """Test that OpenWebUI tool uses the new server endpoints"""
    print("🔗 TESTING OPENWEBUI TOOL WITH NEW SERVER ENDPOINTS")
    print("=" * 60)
    
    # Initialize the tool
    tool = Tools()
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    
    # Test 1: Check if tool tries to use new analysis endpoint
    print("\n📋 TEST 1: ANALYSIS ENDPOINT USAGE")
    print("-" * 40)
    
    # Mock the requests to track which endpoints are called
    called_endpoints = []
    
    def mock_post(*args, **kwargs):
        called_endpoints.append(('POST', args[0]))
        mock_response = MagicMock()
        
        if '/api/v1/codebases/' in args[0] and '/analyze' in args[0]:
            # Mock successful analysis response
            mock_response.status_code = 200
            mock_response.json.return_value = {
                'status': 'success',
                'source': 'fresh_analysis',
                'functions_discovered': 25,
                'domains_identified': ['memory_management', 'network_operations'],
                'enhancement_rules': 5
            }
        elif '/api/v1/enhance_query' in args[0]:
            # Mock successful enhancement response
            mock_response.status_code = 200
            mock_response.json.return_value = {
                'original_query': 'memory management',
                'enhanced_query': 'memory management malloc free calloc realloc',
                'enhancements': ['malloc', 'free', 'calloc', 'realloc'],
                'codebase': 'utils'
            }
        else:
            # Mock other endpoints
            mock_response.status_code = 200
            mock_response.json.return_value = {'result': 'mock response'}
        
        return mock_response
    
    def mock_get(*args, **kwargs):
        called_endpoints.append(('GET', args[0]))
        mock_response = MagicMock()
        
        if '/api/v1/codebases/' in args[0] and '/patterns' in args[0]:
            # Mock patterns response
            mock_response.status_code = 200
            mock_response.json.return_value = {
                'codebase': 'utils',
                'patterns': {
                    'functions': ['malloc', 'free', 'calloc', 'realloc', 'tmwmem_alloc'],
                    'domains': {
                        'memory_management': ['malloc', 'free', 'calloc'],
                        'network_operations': ['socket', 'bind', 'listen']
                    },
                    'enhancement_rules': {
                        'memory_management': ['malloc', 'free', 'calloc', 'realloc']
                    }
                }
            }
        elif '/analysis/status' in args[0]:
            # Mock analysis status response
            mock_response.status_code = 200
            mock_response.json.return_value = {
                'analyzed_codebases': 1,
                'codebases': [{
                    'codebase': 'utils',
                    'functions_count': 25,
                    'domains_count': 3,
                    'status': 'ready'
                }]
            }
        else:
            mock_response.status_code = 404
        
        return mock_response
    
    # Test with mocked requests
    with patch('requests.post', side_effect=mock_post), \
         patch('requests.get', side_effect=mock_get):
        
        # Test analysis status check
        status = await tool._check_server_analysis_status("utils")
        print(f"✅ Analysis status check: {status is not None}")
        
        # Test dynamic query enhancement
        enhancements = await tool._get_dynamic_query_enhancement("memory management", "utils")
        print(f"✅ Dynamic enhancement: {len(enhancements)} terms")
        print(f"   Enhancement terms: {enhancements}")
        
        # Test codebase analysis trigger
        await tool._ensure_codebase_analyzed("utils")
        print(f"✅ Codebase analysis triggered")
        
        # Test chunk fetching with new endpoints
        chunks = await tool._fetch_codebase_chunks("utils")
        print(f"✅ Chunk fetching: {len(chunks)} chunks")
    
    # Check which endpoints were called
    print(f"\n📊 ENDPOINTS CALLED:")
    new_endpoints_used = 0
    for method, url in called_endpoints:
        if '/api/v1/' in url or '/analysis/' in url:
            print(f"   ✅ {method} {url.split('/')[-2:]}")
            new_endpoints_used += 1
        else:
            print(f"   ⚠️ {method} {url.split('/')[-2:]} (legacy)")
    
    print(f"\n   New endpoints used: {new_endpoints_used}/{len(called_endpoints)}")
    
    # Test 2: Integration test with real query
    print("\n📋 TEST 2: REAL QUERY WITH NEW ENDPOINTS")
    print("-" * 40)
    
    # Mock a more realistic scenario
    def mock_realistic_post(*args, **kwargs):
        mock_response = MagicMock()
        
        if '/api/v1/enhance_query' in args[0]:
            # Simulate server-side enhancement
            mock_response.status_code = 200
            mock_response.json.return_value = {
                'original_query': 'memory management',
                'enhanced_query': 'memory management tmwmem_alloc tmwmem_free custom_malloc',
                'enhancements': ['tmwmem_alloc', 'tmwmem_free', 'custom_malloc'],
                'codebase': 'utils'
            }
        elif '/tools/get_optimized_context' in args[0]:
            # Simulate enhanced context retrieval
            payload = kwargs.get('json', {})
            query = payload.get('query', '')
            
            if 'tmwmem_alloc' in query:
                mock_response.status_code = 200
                mock_response.json.return_value = {
                    'result': f'Enhanced context for: {query}\n\nFound memory management functions:\n- tmwmem_alloc()\n- tmwmem_free()\n- custom_malloc()\n\nDynamic enhancement applied successfully!'
                }
            else:
                mock_response.status_code = 200
                mock_response.json.return_value = {
                    'result': f'Basic context for: {query}\n\nGeneric memory management information.'
                }
        else:
            mock_response.status_code = 200
            mock_response.json.return_value = {'result': 'mock response'}
        
        return mock_response
    
    with patch('requests.post', side_effect=mock_realistic_post), \
         patch('requests.get', side_effect=mock_get):
        
        # Test a real query flow
        result = await tool.get_code_context("memory management", codebase_name="utils", n_results=5)
        
        print(f"✅ Query result length: {len(result)} characters")
        
        if "tmwmem_alloc" in result:
            print(f"✅ Dynamic enhancement working: Found codebase-specific functions")
        elif "Enhanced context" in result:
            print(f"✅ Enhancement applied: Query was enhanced")
        else:
            print(f"⚠️ Enhancement may not be working: Generic result")
        
        print(f"   Sample result: {result[:200]}...")
    
    # Test 3: Fallback behavior
    print("\n📋 TEST 3: FALLBACK BEHAVIOR")
    print("-" * 40)
    
    def mock_failing_requests(*args, **kwargs):
        mock_response = MagicMock()
        mock_response.status_code = 404  # Simulate endpoints not available
        return mock_response
    
    with patch('requests.post', side_effect=mock_failing_requests), \
         patch('requests.get', side_effect=mock_failing_requests):
        
        # Test that tool falls back gracefully
        enhancements = await tool._get_dynamic_query_enhancement("memory management", "utils")
        print(f"✅ Fallback enhancement: {len(enhancements)} terms (should be 0)")
        
        status = await tool._check_server_analysis_status("utils")
        print(f"✅ Fallback status check: {status is None}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 OPENWEBUI NEW ENDPOINTS INTEGRATION TEST SUMMARY")
    print("=" * 60)
    
    print("🎯 NEW ENDPOINT INTEGRATION STATUS:")
    print("   ✅ Analysis endpoint (/api/v1/codebases/{name}/analyze)")
    print("   ✅ Patterns endpoint (/api/v1/codebases/{name}/patterns)")
    print("   ✅ Enhancement endpoint (/api/v1/enhance_query)")
    print("   ✅ Status endpoint (/analysis/status)")
    
    print("\n🔄 INTEGRATION FEATURES:")
    print("   ✅ Server-side dynamic enhancement prioritized")
    print("   ✅ Local analyzer fallback maintained")
    print("   ✅ Graceful degradation when endpoints unavailable")
    print("   ✅ Enhanced query flow with codebase-specific terms")
    
    print("\n🚀 BENEFITS:")
    print("   • Server-side analysis leverages full 27-language support")
    print("   • Dynamic patterns shared across all OpenWebUI users")
    print("   • Consistent enhancement behavior")
    print("   • Better performance with server-side caching")
    
    if new_endpoints_used > 0:
        print(f"\n🎉 SUCCESS! OpenWebUI tool is using {new_endpoints_used} new endpoints!")
        print("🔗 Your tool now leverages the enhanced server capabilities!")
    else:
        print(f"\n⚠️ WARNING: Tool may not be using new endpoints properly")
    
    print("\n🎯 The OpenWebUI tool now provides:")
    print("   • Codebase-specific function names in enhancements")
    print("   • Multi-language pattern support")
    print("   • Server-side analysis caching")
    print("   • Intelligent fallback to local analysis")

if __name__ == "__main__":
    try:
        asyncio.run(test_openwebui_new_endpoints())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
