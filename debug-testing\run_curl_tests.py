#!/usr/bin/env python3
"""
Actually run the curl tests and show results
"""

import subprocess
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def run_curl_test(test_name, prompt, description):
    """Run a single curl test and analyze the response"""
    print(f"\n🧪 {test_name}")
    print(f"📋 {description}")
    print(f"💬 Prompt: '{prompt}'")
    print("-" * 60)
    
    # Build curl command
    curl_cmd = [
        "curl", "-s", "-X", "POST",
        f"{OPENWEBUI_URL}/api/chat/completions",
        "-H", "Content-Type: application/json",
        "-H", f"Authorization: Bearer {API_KEY}",
        "-d", json.dumps({
            "model": "llama3:latest",
            "messages": [{"role": "user", "content": prompt}],
            "tool_ids": ["code_analyzer_tool"],  # Enable Code Analysis tool
            "stream": False,
            "max_tokens": 1000
        })
    ]
    
    try:
        # Run curl command
        result = subprocess.run(curl_cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            try:
                # Parse JSON response
                response_data = json.loads(result.stdout)
                
                # Extract content
                content = ""
                if 'choices' in response_data and len(response_data['choices']) > 0:
                    content = response_data['choices'][0].get('message', {}).get('content', '')
                elif 'error' in response_data:
                    content = f"Error: {response_data['error']}"
                else:
                    content = f"Unexpected response format: {str(response_data)[:200]}..."
                
                print(f"✅ Response received ({len(content)} chars)")
                
                # Analyze for tool indicators
                tool_indicators = [
                    "🔧", "📚", "✅", "❌", "🚀", "📊",  # Emojis
                    "Code Analysis System Status", "Available Codebases", "Tool Server",  # Tool text
                    "utils", "z80emu", "modbus", "networking_project",  # Your codebases
                    "ready_enhanced", "v3.0_enhanced",  # Tool-specific terms
                ]
                
                found_indicators = [ind for ind in tool_indicators if ind in content]
                
                if found_indicators:
                    print(f"🎉 PLUGIN WORKING! Found: {found_indicators[:3]}...")
                    tool_working = True
                else:
                    print("⚠️ No tool indicators - likely generic LLM response")
                    tool_working = False
                
                # Show response content
                print(f"\n📄 Response content:")
                print("-" * 40)
                if len(content) > 500:
                    print(content[:250])
                    print(f"\n... [truncated {len(content) - 500} chars] ...\n")
                    print(content[-250:])
                else:
                    print(content)
                print("-" * 40)
                
                return tool_working, content
                
            except json.JSONDecodeError:
                print(f"❌ Invalid JSON response:")
                print(result.stdout[:500])
                return False, result.stdout
                
        else:
            print(f"❌ Curl failed (exit code {result.returncode}):")
            print(f"stdout: {result.stdout}")
            print(f"stderr: {result.stderr}")
            return False, ""
            
    except subprocess.TimeoutExpired:
        print("❌ Request timed out")
        return False, ""
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, ""

def main():
    """Run all curl tests"""
    print("🧪 OpenWebUI Code Analysis Tool Curl Tests")
    print("=" * 60)
    print(f"Target: {OPENWEBUI_URL}")
    print(f"Model: llama3:latest")
    print(f"Using API key: {API_KEY[:10]}...{API_KEY[-4:]}")
    
    # Test cases
    tests = [
        ("Test 1: Basic Status", "status", "Should return formatted status with emojis"),
        ("Test 2: List Codebases", "list codebases", "Should return actual codebase list"),
        ("Test 3: Select Codebase", "select codebase utils", "Should select utils codebase"),
        ("Test 4: Code Search", "find memory allocation functions", "Should search code and return snippets"),
        ("Test 5: AI Analysis", "How does memory management work in this codebase?", "Should provide AI analysis of code"),
        ("Test 6: Tool Command", "!list_codebases", "Test ! prefix command format"),
        ("Test 7: Status Command", "!check_system_status", "Test ! prefix status command"),
    ]
    
    working_tests = []
    total_tests = len(tests)
    
    # Run each test
    for test_name, prompt, description in tests:
        tool_working, _ = run_curl_test(test_name, prompt, description)
        if tool_working:
            working_tests.append(test_name)
    
    # Summary
    print(f"\n📊 SUMMARY")
    print("=" * 60)
    print(f"Total tests: {total_tests}")
    print(f"Tool working: {len(working_tests)}")
    print(f"Success rate: {(len(working_tests)/total_tests*100):.1f}%")
    
    if working_tests:
        print(f"\n✅ Tests where tool worked:")
        for test in working_tests:
            print(f"   • {test}")
    else:
        print(f"\n❌ Tool not working in any tests")
        print(f"\n💡 This indicates:")
        print("   • Tool not installed in OpenWebUI")
        print("   • Tool not enabled for llama3:latest model")
        print("   • Tool has configuration issues")
        
    print(f"\n🎯 Next Steps:")
    if working_tests:
        print("✅ Tool is working! Update testing scripts to use working formats")
    else:
        print("🔧 Install and enable tool in OpenWebUI web interface")
        print("📋 Check Settings → Functions → Import Function")
        print("⚙️ Enable tool for llama3:latest model")

if __name__ == "__main__":
    main()
