#!/usr/bin/env python3
"""
Test that caching is disabled by default
"""

import asyncio
import sys
import os

# Add the current directory to Python path so we can import the tool
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the tool
from open_webui_code_analyzer_tool import Tools

class MockEventEmitter:
    """Mock event emitter for testing"""
    def __init__(self, verbose=True):
        self.verbose = verbose
        self.events = []
    
    async def __call__(self, event):
        """Mock event emission"""
        if self.verbose:
            event_type = event.get("type", "unknown")
            data = event.get("data", {})
            description = data.get("description", "No description")
            done = data.get("done", False)
            status = "✅" if done else "🔄"
            print(f"   {status} Event: {description}")
        
        self.events.append(event)

async def test_cache_disabled():
    """Test that caching is disabled by default"""
    print("🧪 TESTING CACHE DISABLED BY DEFAULT")
    print("="*60)
    
    # Create tool with default settings
    tool = Tools()
    emitter = MockEventEmitter(verbose=True)
    
    # Configure for our test environment
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    tool.valves.current_codebase = "utils"
    
    print(f"\n🔧 Tool Configuration:")
    print(f"   Caching Enabled: {tool.valves.enable_caching}")
    print(f"   Cache Object: {tool.cache}")
    print(f"   Server: {tool.valves.code_analyzer_server_url}")
    
    # Test a simple query twice to see if it hits cache
    query = "tmwmem_alloc"
    
    print(f"\n🔧 Testing query: '{query}' (first call)")
    result1 = await tool.get_code_context(
        query=query,
        codebase_name="utils",
        n_results=3,
        __event_emitter__=emitter
    )
    
    print(f"\n🔧 Testing query: '{query}' (second call)")
    emitter.events = []  # Reset events
    result2 = await tool.get_code_context(
        query=query,
        codebase_name="utils", 
        n_results=3,
        __event_emitter__=emitter
    )
    
    # Check if second call was cached
    cache_hit = any("cache" in str(event).lower() for event in emitter.events)
    
    print(f"\n📊 Results:")
    print(f"   Caching Enabled: {tool.valves.enable_caching}")
    print(f"   Cache Object Exists: {tool.cache is not None}")
    print(f"   Second Call Hit Cache: {cache_hit}")
    print(f"   First Result Length: {len(result1) if result1 else 0}")
    print(f"   Second Result Length: {len(result2) if result2 else 0}")
    
    if tool.valves.enable_caching:
        print("   ❌ FAIL: Caching should be disabled by default")
    else:
        print("   ✅ PASS: Caching is disabled by default")
    
    if cache_hit and not tool.valves.enable_caching:
        print("   ❌ FAIL: Cache hit detected even though caching is disabled")
    else:
        print("   ✅ PASS: No cache hit when caching is disabled")

async def test_cache_can_be_enabled():
    """Test that caching can still be enabled if desired"""
    print(f"\n{'='*60}")
    print("🧪 TESTING CACHE CAN BE ENABLED")
    print("="*60)
    
    # Create tool and enable caching
    tool = Tools()
    tool.valves.enable_caching = True
    
    # Re-initialize to pick up the caching setting
    tool.__init__()
    
    emitter = MockEventEmitter(verbose=True)
    
    # Configure for our test environment
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    tool.valves.current_codebase = "utils"
    
    print(f"\n🔧 Tool Configuration:")
    print(f"   Caching Enabled: {tool.valves.enable_caching}")
    print(f"   Cache Object: {tool.cache}")
    
    # Test a simple query twice to see if it hits cache
    query = "tmwmem_free"
    
    print(f"\n🔧 Testing query: '{query}' (first call)")
    result1 = await tool.get_code_context(
        query=query,
        codebase_name="utils",
        n_results=3,
        __event_emitter__=emitter
    )
    
    print(f"\n🔧 Testing query: '{query}' (second call)")
    emitter.events = []  # Reset events
    result2 = await tool.get_code_context(
        query=query,
        codebase_name="utils",
        n_results=3,
        __event_emitter__=emitter
    )
    
    # Check if second call was cached
    cache_hit = any("cache" in str(event).lower() for event in emitter.events)
    
    print(f"\n📊 Results:")
    print(f"   Caching Enabled: {tool.valves.enable_caching}")
    print(f"   Cache Object Exists: {tool.cache is not None}")
    print(f"   Second Call Hit Cache: {cache_hit}")
    print(f"   First Result Length: {len(result1) if result1 else 0}")
    print(f"   Second Result Length: {len(result2) if result2 else 0}")
    
    if tool.valves.enable_caching and tool.cache is not None:
        print("   ✅ PASS: Caching can be enabled when requested")
    else:
        print("   ❌ FAIL: Caching should work when enabled")

async def main():
    """Main test execution"""
    await test_cache_disabled()
    await test_cache_can_be_enabled()
    
    print(f"\n{'='*60}")
    print("🎯 SUMMARY")
    print("="*60)
    print("✅ Caching is now disabled by default")
    print("✅ This prevents stale failed results from being served")
    print("✅ All queries will be fresh and use latest server improvements")
    print("✅ Users can still enable caching if they want it")
    print("💡 Consider removing caching entirely if no performance benefit")

if __name__ == "__main__":
    asyncio.run(main())
