generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model User {
  id    String   @id @default(uuid())
  name  String
  email String   @unique
  role  String
  orders Order[]
}

model Order {
  id        String   @id @default(uuid())
  userId    String
  bookIds   String   // Store as comma-separated IDs (or normalize with another table)
  status    String
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id])
}
