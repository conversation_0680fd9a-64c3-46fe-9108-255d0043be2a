#!/usr/bin/env python3
"""
Test if we can get actual code from the utils codebase
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"
CODE_ANALYZER_SERVER_URL = "http://home-ai-server:5002"

def test_code_analyzer_server_direct():
    """Test Code Analysis server directly"""
    print("🔧 Testing Code Analysis Server Directly")
    print("=" * 50)
    
    try:
        # Test search endpoint directly
        response = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/search_code",
            json={
                "query": "memory allocation",
                "codebase_name": "utils",
                "n_results": 3
            },
            timeout=30
        )
        
        print(f"📡 Code Analysis server response: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ Code Analysis server JSON response received")
                
                # Check the structure
                if 'result' in data:
                    result = data['result']
                    if 'results' in result:
                        results = result['results']
                        print(f"📊 Found {len(results)} search results")
                        
                        if results:
                            print("🎉 Code Analysis server has actual code data!")
                            for i, result in enumerate(results[:2]):
                                content = result.get('content', '')
                                filename = result.get('metadata', {}).get('filename', 'unknown')
                                print(f"   Result {i+1} ({filename}): {content[:100]}...")
                        else:
                            print("❌ Code Analysis server returned empty results")
                    else:
                        print(f"⚠️ No 'results' key in response: {list(result.keys())}")
                else:
                    print(f"⚠️ No 'result' key in response: {list(data.keys())}")
                    
            except json.JSONDecodeError:
                print(f"❌ Invalid JSON response: {response.text[:200]}")
                
        else:
            print(f"❌ Code Analysis server error: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Code Analysis server connection error: {e}")

def test_tool_search_functions():
    """Test specific tool search functions"""
    print(f"\n🔍 Testing Tool Search Functions")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # First ensure codebase is selected
    print("1. Selecting utils codebase...")
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json={
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": "select codebase utils"}],
                "tool_ids": ["code_analyzer_tool"],
                "stream": False,
                "max_tokens": 500
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            if "ready_enhanced" in content or "✅" in content:
                print("✅ Codebase selected successfully")
            else:
                print("⚠️ Codebase selection unclear")
        else:
            print(f"❌ Selection failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Selection error: {e}")
    
    # Test different search approaches
    search_tests = [
        ("Direct search", "search for malloc in utils codebase"),
        ("Context request", "get code context for memory allocation"),
        ("Smart context", "smart_code_context memory allocation"),
        ("Find functions", "find functions that allocate memory"),
        ("Show code", "show me memory allocation code from utils"),
    ]
    
    for test_name, query in search_tests:
        print(f"\n🧪 {test_name}: '{query}'")
        print("-" * 40)
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": query}],
                    "tool_ids": ["code_analyzer_tool"],
                    "stream": False,
                    "max_tokens": 1000
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                print(f"✅ Response ({len(content)} chars)")
                
                # Check for actual C/C++ code indicators from utils
                code_indicators = [
                    "#include", "malloc(", "free(", "void*", "struct", 
                    "FILE*", "printf", "return", "int main", "static",
                    ".c:", ".h:", "line", "function"
                ]
                
                found_code = [ind for ind in code_indicators if ind in content]
                
                if found_code:
                    print(f"🎉 ACTUAL CODE FOUND! Indicators: {found_code[:3]}...")
                    # Show a longer preview for actual code
                    lines = content.split('\n')
                    code_lines = [line for line in lines if any(ind in line for ind in code_indicators)]
                    if code_lines:
                        print(f"Code lines: {code_lines[:2]}")
                else:
                    print("⚠️ No actual code found - likely generic response")
                    
                # Show preview
                preview = content[:200] + "..." if len(content) > 200 else content
                print(f"Preview: {preview}")
                
            else:
                print(f"❌ Search failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Search error: {e}")

def main():
    """Main test function"""
    print("🔍 Actual Code Retrieval Test")
    print("=" * 70)
    print("Testing if we can retrieve actual code from utils codebase")
    
    # Test Code Analysis server directly
    test_code_analyzer_server_direct()
    
    # Test tool search functions
    test_tool_search_functions()
    
    print(f"\n📋 ANALYSIS")
    print("=" * 50)
    print("🎯 Key Questions:")
    print("1. Does Code Analysis server have actual utils code data?")
    print("2. Are tool search functions being called correctly?")
    print("3. Is the selected codebase being used in searches?")
    
    print(f"\n💡 If Code Analysis server has data but tool doesn't return it:")
    print("   → Tool routing issue - queries not reaching search functions")
    print("   → Check tool's query intent detection")
    print("   → Verify search function implementations")

if __name__ == "__main__":
    main()
