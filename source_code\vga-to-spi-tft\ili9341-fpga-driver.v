module ili9341_driver (
    input wire clk_50m,       // 50MHz system clock
    input wire rst_n,         // Active low reset
    
    // SPI Interface
    output reg spi_clk,       // SPI clock
    output reg spi_mosi,      // SPI data out
    output reg spi_cs_n,      // SPI chip select (active low)
    output reg dc,            // Data/Command control (1=data, 0=command)
    output reg rst_disp_n,    // Display reset (active low)
    
    // Frame buffer interface
    input wire [15:0] pixel_data,     // 16-bit RGB565 pixel data
    output reg [7:0] pixel_addr_x,    // X coordinate
    output reg [8:0] pixel_addr_y,    // Y coordinate
    output reg busy                   // Indicates when the driver is busy
);

    // FSM States
    localparam INIT          = 4'd0;
    localparam RESET_DISPLAY = 4'd1;
    localparam SEND_COMMANDS = 4'd2;
    localparam SET_WINDOW   = 4'd3;
    localparam WRITE_PIXELS = 4'd4;

    reg [3:0] state, next_state;
    reg [7:0] init_cmd_cnt;
    reg [15:0] pixel_cnt;
    reg [7:0] spi_cnt;
    reg [7:0] bit_cnt;
    reg [2:0] spi_clk_div;  // Modified for 50MHz operation

    // SPI clock generator (12.5MHz from 50MHz)
    always @(posedge clk_50m or negedge rst_n) begin
        if (!rst_n)
            spi_clk_div <= 3'd0;
        else
            spi_clk_div <= spi_clk_div + 3'd1;
    end
    assign spi_clk = spi_clk_div[2];  // Divide by 8 to get ~6.25MHz SPI clock


    // Main FSM
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            state <= INIT;
            rst_disp_n <= 1'b0;
            spi_cs_n <= 1'b1;
            dc <= 1'b1;
            init_cmd_cnt <= 8'd0;
            pixel_cnt <= 16'd0;
            pixel_addr_x <= 8'd0;
            pixel_addr_y <= 9'd0;
        end
        else begin
            case (state)
                INIT: begin
                    rst_disp_n <= 1'b0;
                    state <= RESET_DISPLAY;
                end

                RESET_DISPLAY: begin
                    rst_disp_n <= 1'b1;
                    if (init_cmd_cnt == 8'd100) begin  // Wait 100 cycles
                        state <= SEND_COMMANDS;
                        init_cmd_cnt <= 8'd0;
                    end
                    else
                        init_cmd_cnt <= init_cmd_cnt + 8'd1;
                end

                SEND_COMMANDS: begin
                    if (init_cmd_cnt < 8'd18) begin  // Number of initialization commands
                        spi_cs_n <= 1'b0;
                        dc <= (init_cmd_cnt[0] == 1'b0) ? 1'b0 : 1'b1;  // Toggle DC for command/data
                        if (bit_cnt == 8'd8) begin
                            bit_cnt <= 8'd0;
                            init_cmd_cnt <= init_cmd_cnt + 8'd1;
                        end
                        else begin
                            spi_mosi <= init_commands[init_cmd_cnt][7-bit_cnt];
                            bit_cnt <= bit_cnt + 8'd1;
                        end
                    end
                    else begin
                        state <= SET_WINDOW;
                        spi_cs_n <= 1'b1;
                    end
                end

                SET_WINDOW: begin
                    spi_cs_n <= 1'b0;
                    if (bit_cnt == 8'd0) begin
                        dc <= 1'b0;
                        spi_mosi <= 8'h2A;  // Column Address Set
                        bit_cnt <= bit_cnt + 8'd1;
                    end
                    else if (bit_cnt == 8'd8) begin
                        dc <= 1'b1;
                        spi_mosi <= 8'h00;  // Start Column High
                        bit_cnt <= bit_cnt + 8'd1;
                    end
                    else if (bit_cnt == 8'd16) begin
                        spi_mosi <= 8'h00;  // Start Column Low
                        bit_cnt <= bit_cnt + 8'd1;
                    end
                    else if (bit_cnt == 8'd24) begin
                        spi_mosi <= 8'h00;  // End Column High
                        bit_cnt <= bit_cnt + 8'd1;
                    end
                    else if (bit_cnt == 8'd32) begin
                        spi_mosi <= 8'hEF;  // End Column Low (239)
                        bit_cnt <= 8'd0;
                        state <= WRITE_PIXELS;
                    end
                end

                WRITE_PIXELS: begin
                    spi_cs_n <= 1'b0;
                    dc <= 1'b1;
                    
                    if (pixel_cnt < 16'd76800) begin  // 240*320 pixels
                        if (bit_cnt < 8'd16) begin
                            spi_mosi <= pixel_data[15-bit_cnt];
                            bit_cnt <= bit_cnt + 8'd1;
                        end
                        else begin
                            bit_cnt <= 8'd0;
                            pixel_cnt <= pixel_cnt + 16'd1;
                            
                            // Update pixel address
                            if (pixel_addr_x == 8'd239) begin
                                pixel_addr_x <= 8'd0;
                                pixel_addr_y <= pixel_addr_y + 9'd1;
                            end
                            else
                                pixel_addr_x <= pixel_addr_x + 8'd1;
                        end
                    end
                    else begin
                        pixel_cnt <= 16'd0;
                        pixel_addr_x <= 8'd0;
                        pixel_addr_y <= 9'd0;
                    end
                end

                default: state <= INIT;
            endcase
        end
    end

endmodule