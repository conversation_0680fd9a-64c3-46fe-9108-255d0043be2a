﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <Authors>Essenbee</Authors>
    <Company>Codebase Alpha Live-coding Stream</Company>
    <Description>A Z80 emulator</Description>
    <RepositoryType>Git</RepositoryType>
    <RepositoryUrl>https://github.com/essenbee/z80emu</RepositoryUrl>
    <PackageProjectUrl>https://github.com/essenbee/z80emu</PackageProjectUrl>
    <PackageLicenseFile>LICENSE</PackageLicenseFile>
    <PackageTags>z80</PackageTags>
    <PackageReleaseNotes>A sample implementation using this code can be found in the repo. It is a ZX Spectrum 48 emulator (without sound).</PackageReleaseNotes>
    <Version>1.0.1</Version>
  </PropertyGroup>

  <ItemGroup>
    <Content Include=".cr\images\CF3C03E9AEA956395291BE98C580C27F.png" />
    <Content Include=".cr\images\E5D9CCCCCD8B82BB1159F822CFE086F8.png" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.CodeAnalysis.FxCopAnalyzers" Version="2.9.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <None Include="..\LICENSE">
      <Pack>True</Pack>
      <PackagePath></PackagePath>
    </None>
  </ItemGroup>

</Project>
