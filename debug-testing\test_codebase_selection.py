#!/usr/bin/env python3
"""
Test the codebase selection feature in auto-tester
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from openwebui_auto_tester import OpenWebUITester

def test_codebase_selection():
    """Test the ensure_codebase_selected method"""
    print("🧪 Testing Codebase Selection Feature")
    print("=" * 60)
    
    # Create tester instance with API key
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"  # Default API key
    tester = OpenWebUITester("http://home-ai-server.local:8080", api_key)
    
    # Test codebase selection
    print("\n🔧 Testing ensure_codebase_selected method...")
    
    try:
        success = tester.ensure_codebase_selected()
        
        if success:
            print("✅ Codebase selection successful!")
            
            # Test a code search to verify it works
            print("\n🔍 Testing code search with selected codebase...")
            response = tester.send_chat_message("find memory allocation functions", verbose=False)
            
            if response and response.get("success"):
                content = response.get("content", "")
                if len(content) > 100:  # Reasonable response length
                    print("✅ Code search working with selected codebase!")
                    print(f"📊 Response length: {len(content)} chars")
                else:
                    print("⚠️ Code search response seems short")
            else:
                print("❌ Code search failed after codebase selection")
                
        else:
            print("❌ Codebase selection failed")
            
    except Exception as e:
        print(f"❌ Error during test: {e}")

def test_category_detection():
    """Test the code category detection logic"""
    print(f"\n🔍 Testing Category Detection Logic")
    print("-" * 40)
    
    # Test categories
    test_cases = [
        (["System Status"], False, "Status only - no codebase needed"),
        (["Management"], False, "Management only - no codebase needed"),
        (["Code Search"], True, "Code Search - codebase needed"),
        (["AI Analysis"], True, "AI Analysis - codebase needed"),
        (["Language Search"], True, "Language Search - codebase needed"),
        (["System Status", "Code Search"], True, "Mixed with code category - codebase needed"),
        (["Management", "AI Analysis"], True, "Mixed with AI category - codebase needed"),
    ]
    
    code_categories = ["Code Search", "AI Analysis", "Language Search"]
    
    for categories, expected, description in test_cases:
        needs_codebase = any(cat in categories for cat in code_categories)
        
        if needs_codebase == expected:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - Expected: {expected}, Got: {needs_codebase}")

def main():
    """Main test function"""
    print("🔧 Codebase Selection Feature Test")
    print("=" * 70)
    print("Testing the automatic codebase selection for code analysis tests")
    
    # Test category detection logic
    test_category_detection()
    
    # Test actual codebase selection
    test_codebase_selection()
    
    print(f"\n📋 Summary:")
    print("✅ Auto-tester now automatically selects a codebase")
    print("✅ Code analysis tests will have proper context")
    print("✅ Non-code tests run without codebase selection")
    
    print(f"\n🎯 Usage:")
    print("When running code analysis categories:")
    print("• Code Search, AI Analysis, Language Search")
    print("• Auto-tester will automatically select 'utils' codebase")
    print("• If 'utils' fails, it tries 'z80emu' as backup")
    print("• Tests proceed with proper codebase context")

if __name__ == "__main__":
    main()
