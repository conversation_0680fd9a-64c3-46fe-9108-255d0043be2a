/*            Navigation menu                */
.nav-pills {
    > li + li {
        margin-left: 0;
    }
    > li > a {
        border: 1px solid $info-color;
        border-radius: 0;
        color: $info-color;
        margin-left: -1px;

        &:hover,
        &:focus{
            background-color: #F5F5F5;
        }
    }
    > li.active > a,
    > li.active > a:hover,
    > li.active > a:focus {
        background-color: $info-color;
        color: #FFFFFF !important;
    }
    > li:first-child > a{
        border-radius: $border-radius-base 0 0 $border-radius-base;
        margin: 0;
    }
    > li:last-child > a{
        border-radius: 0 $border-radius-base $border-radius-base 0;
    }
}

// style for pagination
.pagination.pagination-no-border > li > a,
.pagination.pagination-no-border > li > span{
    border: 0;
}
.pagination > li > a, .pagination > li > span, .pagination > li:first-child > a, .pagination > li:first-child > span, .pagination > li:last-child > a, .pagination > li:last-child > span{
    border-radius: 50%;
    margin: 0 2px;
    color: $default-states-color;
}
.pagination > li.active > a, .pagination > li.active > span, .pagination > li.active > a:hover, .pagination > li.active > span:hover, .pagination > li.active > a:focus, .pagination > li.active > span:focus {
    background-color: $info-color;
    border: 0;
    color: #FFFFFF;
    padding: 7px 13px;
}

.nav-pills.ct-blue > li.active > a,
.nav-pills.ct-blue > li.active > a:hover,
.nav-pills.ct-blue > li.active > a:focus,
.pagination.ct-blue > li.active > a,
.pagination.ct-blue > li.active > span,
.pagination.ct-blue > li.active > a:hover,
.pagination.ct-blue > li.active > span:hover,
.pagination.ct-blue > li.active > a:focus,
.pagination.ct-blue > li.active > span:focus{
   background-color: $primary-color;
}

.nav-pills.ct-azzure > li.active > a,
.nav-pills.ct-azzure > li.active > a:hover,
.nav-pills.ct-azzure > li.active > a:focus,
.pagination.ct-azzure > li.active > a,
.pagination.ct-azzure > li.active > span,
.pagination.ct-azzure > li.active > a:hover,
.pagination.ct-azzure > li.active > span:hover,
.pagination.ct-azzure > li.active > a:focus,
.pagination.ct-azzure > li.active > span:focus{
   background-color: $info-color;
}

.nav-pills.ct-green >  li.active > a,
.nav-pills.ct-green >  li.active > a:hover,
.nav-pills.ct-green >  li.active > a:focus,
.pagination.ct-green > li.active > a,
.pagination.ct-green > li.active > span,
.pagination.ct-green > li.active > a:hover,
.pagination.ct-green > li.active > span:hover,
.pagination.ct-green > li.active > a:focus,
.pagination.ct-green > li.active > span:focus{
   background-color: $success-color;
}

.nav-pills.ct-orange > li.active > a,
.nav-pills.ct-orange > li.active > a:hover,
.nav-pills.ct-orange > li.active > a:focus,
.pagination.ct-orange > li.active > a,
.pagination.ct-orange > li.active > span,
.pagination.ct-orange > li.active > a:hover,
.pagination.ct-orange > li.active > span:hover,
.pagination.ct-orange > li.active > a:focus,
.pagination.ct-orange > li.active > span:focus{
   background-color: $warning-color;
}

.nav-pills.ct-red > li.active > a,
.nav-pills.ct-red > li.active > a:hover,
.nav-pills.ct-red > li.active > a:focus,
.pagination.ct-red > li.active > a,
.pagination.ct-red > li.active > span,
.pagination.ct-red > li.active > a:hover,
.pagination.ct-red > li.active > span:hover,
.pagination.ct-red > li.active > a:focus,
.pagination.ct-red > li.active > span:focus{
   background-color: $danger-color;
}
.nav-pills.ct-blue > li > a {
    @include pill-style($primary-color);
}
.nav-pills.ct-azzure > li > a {
    @include pill-style($info-color);
}
.nav-pills.ct-green > li > a {
    @include pill-style($success-color);
}
.nav-pills.ct-orange > li > a {
    @include pill-style($warning-color);
}
.nav-pills.ct-red > li > a {
    @include pill-style($danger-color);
}