/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2023 */
/*****************************************************************************/
/*                                                                           */
/* This file is the property of:                                             */
/*                                                                           */
/*                       Triangle MicroWorks, Inc.                           */
/*                      Raleigh, North Carolina USA                          */
/*                       www.TriangleMicroWorks.com                          */
/*                          (919) 870-6615                                   */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*                                                                           */
/*****************************************************************************/

/* file: smbsim.h
 * description: Simulates a MB Server database.
 *  This file is an example of a simulated MB Server database interface.
 *  It should NOT be included in the final version of a MB Server device.
 */
#ifndef SMBSIM_DEFINED
#define SMBSIM_DEFINED

#include "tmwscl/utils/tmwcnfg.h"

#if TMWCNFG_USE_SIMULATED_DB

#include "tmwscl/utils/tmwdefs.h"
#include "tmwscl/utils/tmwdtime.h"
#include "tmwscl/utils/tmwsesn.h"
#include "tmwscl/utils/tmwdlist.h"
#include "tmwscl/utils/tmwsim.h"

/* Specify how many points of each data type to simulate */
#define SMBSIM_NUM_COILS               100
#define SMBSIM_NUM_DISCRETE_INPUTS     100
#define SMBSIM_NUM_INPUT_REGISTERS     100
#define SMBSIM_NUM_HOLDING_REGISTERS   100

/* Specify callback */
typedef void (*SMBSIM_CALLBACK_FUNC)(
  void *pCallbackParam, 
  TMWSIM_EVENT_TYPE type,
  TMWTYPES_UCHAR dataType, 
  TMWTYPES_USHORT address);

/* Define simulated database context */
typedef struct SMBSimDatabaseStruct {
  TMWSIM_TABLE_HEAD   coils;
  TMWSIM_TABLE_HEAD   discreteInputs;
  TMWSIM_TABLE_HEAD   inputRegisters;
  TMWSIM_TABLE_HEAD   holdingRegisters;

  TMWSIM_TABLE_HEAD   deviceIds;

  /* 8 exception status bits */
  TMWTYPES_UCHAR      exceptionStatus;

  /* User callbacks */
  SMBSIM_CALLBACK_FUNC pUpdateCallback;
  void *pUpdateCallbackParam;

  /* Manged SCL database handle*/
  void *managedDBhandle;

} SMBSIM_DATABASE;

#ifdef __cplusplus
extern "C" {
#endif

  /* function: smbsim_init */
  void * TMWDEFS_GLOBAL smbsim_init(
    TMWSESN *pSession);

  /* function: smbsim_close */
  void TMWDEFS_GLOBAL smbsim_close(
    void *pHandle);

  /* function: smbsim_clear */
  TMWDEFS_SCL_API void TMWDEFS_GLOBAL smbsim_clear(
    void *pHandle);

  /* function: smbsim_reset */
  TMWDEFS_SCL_API void TMWDEFS_GLOBAL smbsim_reset(
    void *pHandle);

  /* Set update callback and parameter */
  TMWDEFS_SCL_API void smbsim_setCallback(
    void *pHandle,
    SMBSIM_CALLBACK_FUNC pUpdateCallback,
    void *pUpdateCallbackParam);

  /* function: smbsim_enablePoint */
  TMWDEFS_SCL_API void TMWDEFS_GLOBAL smbsim_enablePoint(
    void *pPoint,
    TMWTYPES_BOOL enabled);

  /* function: smbsim_isPointEnabled */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_isPointEnabled(
    void *pPoint);

  /* function: smbsim_getPointNumber */
  TMWDEFS_SCL_API TMWTYPES_USHORT TMWDEFS_GLOBAL smbsim_getPointNumber(
    void *pPoint);

  /* Discrete Inputs */

  /* function: smbsim_addDiscreteInput
   * purpose: Add a simulated discrete input
   * arguments:
   * returns:
   */
  TMWDEFS_SCL_API void * TMWDEFS_GLOBAL smbsim_addDiscreteInput(
    void *pHandle,
    TMWTYPES_USHORT pointNum,
    TMWTYPES_BOOL value);

  /* function: smbsim_deleteDiscreteInput
   * purpose: Delete a simulated discrete input
   * arguments:
   * returns:
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_deleteDiscreteInput(
    void *pHandle,
    TMWTYPES_USHORT pointNumber);

  /* function: smbsim_DiscreteInputsValidateRange
   * purpose: Validate the range of discrete inputs in the 
   *  specified database.
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   * returns:
   *  TMWDEFS_TRUE if range exists, otherwise TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_DiscreteInputsValidateRange(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT length);

  TMWDEFS_SCL_API void * TMWDEFS_GLOBAL smbsim_DiscreteInputGetPoint(
    void *pHandle,
    TMWTYPES_USHORT pointNum);
    
  /* function: smbsim_dInputGetPointByIndex */
  TMWDEFS_SCL_API void * TMWDEFS_GLOBAL smbsim_dInputGetPointByIndex(
    void *pHandle,
    TMWTYPES_USHORT index);
  
  /* function: smbsim_dInputGetLastPoint */
  TMWDEFS_SCL_API void * TMWDEFS_GLOBAL smbsim_dInputGetLastPoint(
    void *pHandle);
 
  /* function: smbsim_dInputBlockRead
   * purpose: Read the specified discrete input.
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   *  startAddr - 0 based address of first point to read
   *  length - number of discrete inputs to read
   *  pValueArray - pointer to array of values
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_dInputBlockRead(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT length,
    TMWTYPES_UCHAR *pValueArray);

  /* function: smbsim_dInputRead
   * purpose: Read the specified discrete input.
   * arguments:
   *  pPoint - handle to database point
   *  pValue - pointer to value
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_dInputRead(
    void *pPoint,
    TMWTYPES_UCHAR *pValue);

  /* function: smbsim_dInputWrite
   * purpose: Write the specified discrete input
   * arguments:
   *  pPoint - point to write
   *  value - value to write
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_dInputWrite(
    void *pPoint, 
    TMWTYPES_BOOL value);
  
  /* Coils */

  /* function: smbsim_addCoil
   * purpose: Add a simulated coil
   * arguments:
   * returns:
   */
  TMWDEFS_SCL_API void * TMWDEFS_GLOBAL smbsim_addCoil(
    void *pHandle,
    TMWTYPES_USHORT pointNum,
    TMWTYPES_BOOL value);

  /* function: smbsim_deleteCoil
   * purpose: Delete a simulated coil
   * arguments:
   * returns:
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_deleteCoil(
    void *pHandle,
    TMWTYPES_USHORT pointNumber);

  /* function: smbsim_CoilsValidateRange
   * purpose: Validate the range of coils in the 
   *  specified database.
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   * returns:
   *  TMWDEFS_TRUE if range exists, otherwise TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_CoilsValidateRange(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT length);

  TMWDEFS_SCL_API void * TMWDEFS_GLOBAL smbsim_coilGetPoint(
    void *pHandle,
    TMWTYPES_USHORT pointNum);

  /* function: smbsim_coilGetPointByIndex */
  TMWDEFS_SCL_API void * TMWDEFS_GLOBAL smbsim_coilGetPointByIndex(
    void *pHandle,
    TMWTYPES_USHORT index);

  /* function: smbsim_coilGetLastPoint */
  TMWDEFS_SCL_API void * TMWDEFS_GLOBAL smbsim_coilGetLastPoint(
    void *pHandle);

  /* function: smbsim_CoilBlockRead
   * purpose: Read the specified Coil.
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   *  startAddr - 0 based address of first point to read
   *  length - number of coils to read
   *  pValueArray - pointer to array of values
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_CoilBlockRead(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT length,
    TMWTYPES_UCHAR *pValueArray);

  /* function: smbsim_CoilRead
   * purpose: Read the specified Coil.
   * arguments:
   *  pPoint - point to write
   *  value - pointer to array of values
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_CoilRead(
    void *pPoint, 
    TMWTYPES_BOOL *Value);

  /* function: smbsim_CoilBlockWrite
   * purpose: Write the specified holding registers
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   *  startAddr - 0 based address of first point to write
   *  length - number of coils to write
   *  pValueArray - pointer to array of values
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_CoilBlockWrite(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT length,
    TMWTYPES_UCHAR *pValueArray);
  
  /* function: smbsim_CoilWrite
   * purpose: Write the specified holding registers
   * arguments:
   *  pPoint - point to write
   *  value - pointer to array of values
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_CoilWrite(
    void *pPoint, 
    TMWTYPES_BOOL value);
  
  /* Input Registers */

  /* function: smbsim_addInputRegister
   * purpose: Add a simulated input register
   * arguments:
   * returns:
   */
  TMWDEFS_SCL_API void * TMWDEFS_GLOBAL smbsim_addInputRegister(
    void *pHandle,
    TMWTYPES_USHORT pointNum,
    TMWTYPES_USHORT value);

  /* function: smbsim_deleteInputRegister
   * purpose: Delete a simulated input register
   * arguments:
   * returns:
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_deleteInputRegister(
    void *pHandle,
    TMWTYPES_USHORT pointNumber);

  /* function: smbsim_iRegQuantity
   * purpose: Return the number of input registers in the 
   *  specified database.
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   * returns:
   *  The number of input registers
   */
  TMWDEFS_SCL_API TMWTYPES_USHORT TMWDEFS_GLOBAL smbsim_iRegQuantity(
    void *pHandle);

  /* function: smbsim_InputRegistersValidateRange
   * purpose: Validate the range of input registers in the 
   *  specified database.
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   * returns:
   *  TMWDEFS_TRUE if range exists, otherwise TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_InputRegistersValidateRange(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT length);
 
  TMWDEFS_SCL_API void * TMWDEFS_GLOBAL smbsim_InputRegisterGetPoint(
    void *pHandle,
    TMWTYPES_USHORT pointNum);
    
  /* function: smbsim_InputRegisterGetPointByIndex */
  TMWDEFS_SCL_API void * TMWDEFS_GLOBAL smbsim_iRegGetPointByIndex(
    void *pHandle,
    TMWTYPES_USHORT index);

  /* function: smbsim_InputRegisterGetLastPoint */
  TMWDEFS_SCL_API void * TMWDEFS_GLOBAL smbsim_iRegGetLastPoint(
    void *pHandle);

  /* function: smbsim_iRegBlockRead
   * purpose: Read the specified input registers.
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   *  startAddr - 0 based address of first point to read
   *  length - number of 16 bit registers to read
   *  pValueArray - pointer to array of values
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_iRegBlockRead(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT length,
    TMWTYPES_USHORT *pValueArray);

  /* function: smbsim_iRegRead
   * purpose: Read the specified input register
   * arguments:
   *  pPoint - handle to database point 
   *  pValue - pointer to value
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_iRegRead(
    void *pPoint,
    TMWTYPES_USHORT *pValue);

  /* function: smbsim_iRegWrite
   * purpose: Write the specified input register
   * arguments:
   *  pPoint - handle to database point
   *  value - value to write
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_iRegWrite(
    void *pPoint,
    TMWTYPES_USHORT value);

  /* Holding Registers */

  /* function: smbsim_addHoldingRegister
   * purpose: Add a simulated holding register
   * arguments:
   * returns:
   */
  TMWDEFS_SCL_API void * TMWDEFS_GLOBAL smbsim_addHoldingRegister(
    void *pHandle,
    TMWTYPES_USHORT pointNum,
    TMWTYPES_USHORT value);

  /* function: smbsim_deleteHoldingRegister
   * purpose: Delete a simulated holding register
   * arguments:
   * returns:
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_deleteHoldingRegister(
    void *pHandle,
    TMWTYPES_USHORT pointNumber);

  /* function: smbsim_hRegQuantity
   * purpose: Return the number of holding registers in the 
   *  specified database.
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   * returns:
   *  The number of holding registers
   */
  TMWDEFS_SCL_API TMWTYPES_USHORT TMWDEFS_GLOBAL smbsim_hRegQuantity(
    void *pHandle);

  /* function: smbsim_HoldingRegistersValidateRange
   * purpose: Validate the range of holding registers in the 
   *  specified database.
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   * returns:
   *  TMWDEFS_TRUE if range exists, otherwise TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_HoldingRegistersValidateRange(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT length);

  TMWDEFS_SCL_API void * TMWDEFS_GLOBAL smbsim_HoldingRegisterGetPoint(
    void *pHandle,
    TMWTYPES_USHORT pointNum);

  /* function: smbsim_hRegGetPointByIndex */
  TMWDEFS_SCL_API void * TMWDEFS_GLOBAL smbsim_hRegGetPointByIndex(
    void *pHandle,
    TMWTYPES_USHORT index);
   
  /* function: smbsim_hRegGetLastPoint */
  TMWDEFS_SCL_API void * TMWDEFS_GLOBAL smbsim_hRegGetLastPoint(
    void *pHandle);


  /* function: smbsim_hRegBlockRead
   * purpose: Read the specified holding registers.
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   *  startAddr - 0 based address of first point to read
   *  length - number of 16 bit registers to read
   *  pValueArray - pointer to array of values
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_hRegBlockRead(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT length,
    TMWTYPES_USHORT *pValueArray);

  /* function: smbsim_hRegRead
   * purpose: Read the specified holding registers.
   * arguments:
   *  pPoint - handle to database returned from smbsim_init
   *  pValue - pointer to value
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_hRegRead(
    void *pPoint,
    TMWTYPES_USHORT *pValue);

  /* function: smbsim_hRegBlockWrite
   * purpose: Write the specified holding registers
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   *  startAddr - 0 based address of first point to write
   *  length - number of 16 bit registers to write
   *  pValueArray - pointer to array of values
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_hRegBlockWrite(
    void *pHandle,
    TMWTYPES_USHORT startAddr,
    TMWTYPES_USHORT length,
    TMWTYPES_USHORT *pValueArray);

  /* function: smbsim_hRegWrite
   * purpose: Write the specified holding registers
   * arguments:
   *  pPoint - handle to database point
   *  value - value to write
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_hRegWrite(
    void *pPoint,
    TMWTYPES_USHORT value);

  /* function: smbsim_saveDatabase
   * purpose: Save database state to XML
   * arguments:
   *  pSector - sector containing database to save
   * returns:
   *  XML string
   */
  TMWDEFS_SCL_API TMWTYPES_CHAR * TMWDEFS_GLOBAL smbsim_saveDatabase(
    TMWSESN *pSession);

  /* function: smbsim_readExceptionStatus
   * purpose: Read the exception status 
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   *  pStatus - pointer to uchar to hold exception status bits
   * returns:
   *  TMWDEFS_TRUE if successful, else TMWDEFS_FALSE
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_readExceptionStatus(  
    void *pHandle,
    TMWTYPES_UCHAR *pStatus);

  /* function: smbsim_setExceptionStatus
   * purpose: Set the exception status 
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   *  status -  uchar to hold exception status bits
   * returns:
   *  void
   */
  TMWDEFS_SCL_API void TMWDEFS_GLOBAL smbsim_setExceptionStatus(  
    void *pHandle,
    TMWTYPES_UCHAR status);

  /* function: smbsim_addDeviceId
   * purpose: Add Device Identification object
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   *  objectId - object id for this device identifier   
   *  length - length of the device identifier value
   *  pData - value for device identifier
   * returns:
   *  void
   */
  TMWDEFS_SCL_API void * TMWDEFS_GLOBAL smbsim_addDeviceId(
    void *pHandle,
    TMWTYPES_UCHAR  objectId,
    TMWTYPES_UCHAR  length,
    TMWTYPES_UCHAR *pData);
   
  /* function: smbsim_deleteDeviceId
   * purpose: Delete Device Identification object
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   *  objectId - object id for the device identifier   
   * returns:
   *  TMWDFS_TRUE if successful
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_deleteDeviceId(
    void *pHandle,
    TMWTYPES_UCHAR objectId);
   
  /* function: smbsim_deviceIdGetPoint
   * purpose: Get Device Identification object
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   *  objectId - object id for the device identifier   
   * returns:
   *  pointer to device id sim point
   */
  void * TMWDEFS_GLOBAL smbsim_deviceIdGetPoint(
    void *pHandle,
    TMWTYPES_UCHAR objectId);

  /* function: smbsim_deviceIdGetPointByIndex
   * purpose: Get Device Identification object
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   *  index - index for this device identifier, there may be gaps in object ids.
   * returns:
   *  pointer to device id sim point
   */
  void * TMWDEFS_GLOBAL smbsim_deviceIdGetPointByIndex(
    void *pHandle,
    TMWTYPES_USHORT index);

  /* function: smbsim_getNextDeviceId
   * purpose: Get object id of next Device Identification object
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   *  index -   
   * returns:
   *  TMWDEFS_TRUE if another device identification object exists.
   */
  TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_getNextDeviceId(
    void *pHandle,
    TMWTYPES_UCHAR objectId,
    TMWTYPES_UCHAR *pNextObjectId);
    
  /* function: smbsim_deviceIdGetLastPoint
   * purpose: Get last Device Identification object
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   * returns:
   *  pointer to device id sim point
   */
  void * TMWDEFS_GLOBAL smbsim_deviceIdGetLastPoint(
    void *pHandle);

  /* function: smbsim_readDeviceId
   * purpose: Read Device Identification object
   * arguments:
   *  pHandle - handle to database returned from smbsim_init
   *  pLength - when called this is the maximum length of data that may be returned
   *            on return pointer to where length of data should be returned
   *  pData - pointer to where device identifier data should be returned
   * returns:
   *  TMWDEFS_TRUE if successful.
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_readDeviceId( 
    void *pPoint,
    TMWTYPES_UCHAR *pLength,
    TMWTYPES_UCHAR *pData); 

  /* function: smbsim_setDeviceId
   * purpose: set Device Identification object, not called by protocol library
   * arguments:
   *  pPoint - handle to device identifier point
   *  bufLength - length of data 
   *  pData - pointer to device identification value data
   * returns:
   *  TMWDEFS_TRUE if successful.
   */
  TMWDEFS_SCL_API TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_setDeviceId(
    void *pPoint,
    TMWTYPES_UCHAR bufLength,
    TMWTYPES_UCHAR *pBuf);

#ifdef __cplusplus
}
#endif
#endif /* TMWCNFG_USE_SIMULATED_DB */
#endif /* SMBSIM_DEFINED */
