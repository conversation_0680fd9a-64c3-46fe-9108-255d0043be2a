#!/usr/bin/env python3
"""Quick test of the RAG server endpoint"""

import requests
import json

def test_rag_endpoint():
    url = "http://home-ai-server.local:5002/tools/search_code"
    payload = {
        "query": "tmwmem_alloc",
        "codebase_name": "utils",
        "n_results": 5
    }

    try:
        response = requests.post(url, json=payload, timeout=30)
        print(f"Status: {response.status_code}")
        print(f"Full Response:")
        print(json.dumps(response.json(), indent=2))
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    test_rag_endpoint()
