﻿<Window x:Class="Essenbee.Z80.Debugger.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:controls="clr-namespace:Essenbee.Z80.Debugger"
        mc:Ignorable="d"
        Background="LightGray"
        MinHeight="650"
        MinWidth="1200
        "
        Title="Essenbee Z80 Emulator - Debugger" 
        Height="650" 
        Width="1000">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="75*" />
            <ColumnDefinition Width="25*" />
            <ColumnDefinition Width="256" />
        </Grid.ColumnDefinitions>

        <!-- Left-hand side -->
        <Grid Grid.Column="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="60*" />
                <RowDefinition Height="30*" />
                <RowDefinition Height="10*" />
            </Grid.RowDefinitions>

            <GroupBox Header="Disassembler"
                      Grid.Row="0"
                      Margin="5,0,0,5">
                <controls:Disassembler Grid.Row="0" />
            </GroupBox>
            <GroupBox Header="Memory"
                      Grid.Row="1"
                  Margin="5,0,0,5">
                <controls:MemoryMap Grid.Row="1" />
                </GroupBox>
            <controls:ControlPanel Grid.Row="2" />

        </Grid>

        <!-- Mid side -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="60*" />
                <RowDefinition Height="40*" />
            </Grid.RowDefinitions>

            <GroupBox Grid.Row="0"
                      Header="Z80"
                      Margin="5,0,5,5" >
                <controls:CpuStatus Grid.Row="0" />
            </GroupBox>
            
            <GroupBox Header="Stack"
                      Margin="5,0,5,5"
                      Grid.Row="1">
                <controls:StackDisplay Grid.Row="1" />
            </GroupBox>
        </Grid>
        <!-- Right-hand side -->
        <Grid Grid.Column="2">
          <controls:MsxScreen RawMemory="{Binding RawMemory}"/>
        </Grid>
    </Grid>
    
</Window>
