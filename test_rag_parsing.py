#!/usr/bin/env python3
"""Test the RAG parsing logic"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from test_openwebui_api import DirectRAGClient

async def test_rag_parsing():
    client = DirectRAGClient()
    
    print("Testing RAG server parsing...")
    result = await client.query_rag_server("tmwmem_alloc", "utils")
    
    print(f"Success: {result.get('success')}")
    print(f"Chunks Count: {result.get('chunks_count')}")
    print(f"Max Similarity: {result.get('max_similarity'):.3f}")
    print(f"Avg Similarity: {result.get('avg_similarity'):.3f}")
    print(f"Source Files: {result.get('source_files')}")
    print(f"Retrieval Time: {result.get('retrieval_time'):.2f}s")
    
    return result.get('chunks_count', 0) > 0

if __name__ == "__main__":
    success = asyncio.run(test_rag_parsing())
    print(f"\nTest {'PASSED' if success else 'FAILED'}")
