#!/usr/bin/env python3
"""
Test the dynamic codebase extraction fix
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
CODE_ANALYZER_SERVER_URL = "http://home-ai-server.local:5002"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_codebase_list_extraction():
    """Test that we can extract codebase names from the list"""
    print("🧪 Testing Dynamic Codebase Name Extraction")
    print("=" * 60)
    
    # Test the Code Analysis server list endpoint
    try:
        response = requests.post(f"{CODE_ANALYZER_SERVER_URL}/tools/list_codebases", json={}, timeout=10)
        
        print(f"📡 List codebases endpoint: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            result = data.get("result", "")
            
            print(f"📊 Raw result ({len(result)} chars):")
            print("-" * 40)
            print(result)
            print("-" * 40)
            
            # Test extraction patterns
            import re
            patterns = [
                (r'\*\*([^*]+)\*\*', "**codebase_name**"),
                (r'• ([^\n]+)', "• codebase_name"),
                (r'\d+\.\s*([^\n]+)', "1. codebase_name"),
            ]
            
            for pattern, description in patterns:
                matches = re.findall(pattern, result)
                if matches:
                    print(f"✅ Pattern '{description}' found: {matches}")
                    
                    # Clean up names
                    cleaned = []
                    for match in matches:
                        clean_name = re.sub(r'[^\w_-]', '', match.strip())
                        if clean_name and len(clean_name) > 1:
                            cleaned.append(clean_name)
                    
                    print(f"   Cleaned names: {cleaned}")
                    return cleaned
                else:
                    print(f"❌ Pattern '{description}' not found")
            
            return []
        else:
            print(f"❌ Error: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return []

def test_stats_with_dynamic_extraction():
    """Test stats queries with dynamic codebase extraction"""
    print(f"\n🧪 Testing Stats with Dynamic Extraction")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test various stats queries
    queries = [
        "get stats for utils",
        "show statistics for z80emu", 
        "modbus codebase stats",
        "get stats for test_project"
    ]
    
    results = []
    
    for query in queries:
        print(f"\n🔍 Testing: '{query}'")
        print("-" * 40)
        
        try:
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json={
                    "model": "llama3:latest",
                    "messages": [{"role": "user", "content": query}],
                    "tool_ids": ["code_analyzer_tool"],
                    "stream": False,
                    "max_tokens": 1000
                },
                timeout=90
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                print(f"✅ Response received ({len(content)} chars)")
                
                # Check for real codebase data indicators
                real_indicators = [
                    "Total chunks:",
                    "Unique files:",
                    "Languages:",
                    "Last updated:",
                    "Basic Codebase Statistics"
                ]
                
                # Check for generic data indicators
                generic_indicators = [
                    "_add_chunk_clarification",
                    "Number of runs",
                    "Average run time",
                    "Memory usage"
                ]
                
                found_real = [ind for ind in real_indicators if ind in content]
                found_generic = [ind for ind in generic_indicators if ind in content]
                
                if found_real and not found_generic:
                    print(f"🎉 REAL DATA: {found_real[:2]}")
                    status = "success"
                elif found_generic:
                    print(f"❌ GENERIC DATA: {found_generic[:2]}")
                    status = "generic"
                else:
                    print("⚠️ UNCLEAR DATA")
                    status = "unclear"
                
                results.append({
                    "query": query,
                    "status": status,
                    "length": len(content)
                })
                
                # Show preview
                preview = content[:150] + "..." if len(content) > 150 else content
                print(f"Preview: {preview}")
                
            else:
                print(f"❌ HTTP {response.status_code}")
                results.append({"query": query, "status": "error"})
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append({"query": query, "status": "error"})
    
    return results

def main():
    """Main test function"""
    print("🔧 Dynamic Codebase Extraction Test")
    print("=" * 70)
    print("Testing improved codebase name extraction from dynamic list")
    
    # Test codebase name extraction
    extracted_names = test_codebase_list_extraction()
    
    # Test stats queries
    results = test_stats_with_dynamic_extraction()
    
    # Analysis
    print(f"\n📊 ANALYSIS")
    print("=" * 50)
    
    valid_results = [r for r in results if r.get("status") != "error"]
    success_count = len([r for r in valid_results if r.get("status") == "success"])
    total_count = len(valid_results)
    
    print(f"📈 Codebase extraction: {len(extracted_names)} names found")
    if extracted_names:
        print(f"   Names: {extracted_names}")
    
    print(f"📈 Stats query success: {success_count}/{total_count}")
    
    if success_count == total_count and success_count > 0:
        print("🎉 EXCELLENT: Dynamic extraction working perfectly!")
        print("✅ All stats queries return real codebase data")
        print("✅ No more hard-coded codebase names")
    elif success_count > 0:
        print("👍 GOOD: Partial success with dynamic extraction")
        print("⚠️ Some queries still need work")
    else:
        print("❌ ISSUE: Dynamic extraction not working")
        print("🔧 Need to update tool in OpenWebUI")

if __name__ == "__main__":
    main()
