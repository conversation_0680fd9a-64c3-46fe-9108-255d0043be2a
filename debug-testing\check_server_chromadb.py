#!/usr/bin/env python3
"""
Check the server's ChromaDB state via health endpoint
"""

import requests
import json

CODE_ANALYZER_SERVER_URL = "http://home-ai-server.local:5002"

def check_server_health():
    """Check server health for ChromaDB info"""
    print("🏥 Checking server health for ChromaDB details...")
    
    try:
        response = requests.get(f"{CODE_ANALYZER_SERVER_URL}/health", timeout=30)
        if response.status_code == 200:
            health = response.json()
            
            print("✅ Server health response:")
            print(f"   ChromaDB collections: {health.get('chromadb_collections', 'unknown')}")
            print(f"   Collection names: {health.get('collection_names', [])}")
            
            # Check for collection details
            collection_details = health.get('collection_details', {})
            if collection_details:
                print("\n📁 Collection Details:")
                for name, details in collection_details.items():
                    print(f"   {name}:")
                    print(f"     Documents: {details.get('document_count', 0)}")
                    print(f"     Status: {details.get('status', 'unknown')}")
                    print(f"     Enhanced: {details.get('enhanced_metadata', False)}")
            
            return health
        else:
            print(f"❌ Health endpoint returned status {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Failed to check server health: {e}")
        return None

def test_search_endpoint():
    """Test the search endpoint to see what happens"""
    print("\n🔍 Testing basic search endpoint...")

    try:
        payload = {
            "query": "test",
            "codebase_name": "utils",
            "n_results": 1
        }

        response = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/search_code",
            json=payload,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            result_text = result.get('result', '')
            print("✅ Basic search endpoint responded:")
            print(f"   Response: {result_text[:200]}...")
        else:
            print(f"❌ Basic search endpoint returned status {response.status_code}")
            print(f"   Response: {response.text}")

    except Exception as e:
        print(f"❌ Basic search endpoint test failed: {e}")

def test_enhanced_search_endpoint():
    """Test the enhanced search endpoint with better filtering"""
    print("\n🔍 Testing enhanced search endpoint...")

    try:
        # Test enhanced search with filters
        payload = {
            "query": "memory allocation",
            "codebase_name": "utils",
            "n_results": 3,
            "filter_language": "c",
            "filter_type": "function"
        }

        response = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/enhanced_search",
            json=payload,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            result_text = result.get('result', '')
            print("✅ Enhanced search endpoint responded:")
            print(f"   Response: {result_text[:300]}...")

            # Check if we got filtered results
            if "function" in result_text.lower() and ("malloc" in result_text.lower() or "alloc" in result_text.lower()):
                print("✅ Enhanced search appears to be working with filters")
                return True
            else:
                print("⚠️ Enhanced search responded but filtering may not be working")
                return False
        else:
            print(f"❌ Enhanced search endpoint returned status {response.status_code}")
            print(f"   Response: {response.text}")

            # If enhanced search doesn't exist, that's expected
            if response.status_code == 404:
                print("ℹ️  Enhanced search endpoint not available (using basic search)")
                return None
            return False

    except Exception as e:
        print(f"❌ Enhanced search endpoint test failed: {e}")
        return False

def test_processing_endpoint():
    """Test processing a small codebase"""
    print("\n⚙️ Testing processing endpoint...")
    
    try:
        payload = {
            "codebase_name": "utils",
            "exclude_dirs": ["build", "test", "bin", "obj", "__pycache__", ".git"]
        }
        
        print("   Sending processing request...")
        response = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/process_codebase",
            json=payload,
            timeout=300  # 5 minutes
        )
        
        if response.status_code == 200:
            result = response.json()
            result_text = result.get('result', '')
            print("✅ Processing endpoint responded:")
            print(f"   Response: {result_text[:300]}...")
            
            # Check if it mentions success
            if "✅" in result_text and "complete" in result_text.lower():
                print("✅ Processing appears successful")
                return True
            else:
                print("⚠️ Processing may have failed")
                return False
        else:
            print(f"❌ Processing endpoint returned status {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Processing endpoint test failed: {e}")
        return False

def main():
    print("🚀 Server ChromaDB State Checker")
    print("=" * 50)
    print("ℹ️  Checking ChromaDB state on home-ai-server.local:5002")
    print()

    # Check server health first
    health = check_server_health()

    # Test basic search to see current state
    test_search_endpoint()

    # Test enhanced search with filtering
    enhanced_result = test_enhanced_search_endpoint()
    
    # Try processing to see if it works
    print("\n" + "=" * 50)
    print("🔧 ATTEMPTING TO FIX BY REPROCESSING...")
    print("=" * 50)
    
    processing_success = test_processing_endpoint()
    
    if processing_success:
        print("\n🔄 Checking state after processing...")
        health_after = check_server_health()
        test_search_endpoint()
        
        if health_after and health_after.get('chromadb_collections', 0) > 0:
            print("\n✅ SUCCESS: Collections now exist!")
        else:
            print("\n❌ STILL FAILING: Collections still not created")
    
    print("\n" + "=" * 50)
    print("📊 ANALYSIS:")
    print("=" * 50)

    # Enhanced search analysis
    if enhanced_result is True:
        print("✅ Enhanced search with filtering is working")
        print("💡 Multiple filters (type + language) are now supported!")
        print("🎉 ChromaDB query syntax fix has been successfully applied")
    elif enhanced_result is False:
        print("⚠️ Enhanced search available but may have issues")
        print("💡 Check server logs for enhanced search problems")
        print("🔧 May need to rebuild container: docker-compose build code-analyzer-server")
    elif enhanced_result is None:
        print("ℹ️  Enhanced search not available - using basic search")
        print("💡 Consider implementing enhanced_search endpoint for better filtering")

    if health:
        collections_count = health.get('chromadb_collections', 0)
        if collections_count == 0:
            print("❌ No collections exist in server's ChromaDB")
            print("🔧 The processing is not actually creating collections")
            print("💡 Possible causes:")
            print("   1. Source code path mismatch in Docker")
            print("   2. ChromaDB path mismatch")
            print("   3. Processing errors being silently ignored")
            print("   4. Embedding function issues")
        else:
            print(f"✅ {collections_count} collections exist")
            print("🔍 Issue might be in collection access, not creation")
    else:
        print("❌ Cannot determine server state")

if __name__ == "__main__":
    main()
