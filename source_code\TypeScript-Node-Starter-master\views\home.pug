extends layout

block content
  h1 Hackathon Starter
  p.lead A boilerplate for Node.js web applications.
  hr
  .row
    .col-sm-6
      h2 Heading
      p Donec id elit non mi porta gravida at eget metus. Fusce dapibus, tellus ac cursus commodo, tortor mauris condimentum nibh, ut fermentum massa justo sit amet risus. Etiam porta sem malesuada magna mollis euismod. Donec sed odio dui.
      p
        a.btn.btn-secondary(href='#', role='button') View details »
    .col-sm-6
      h2 Heading
      p Donec id elit non mi porta gravida at eget metus. <PERSON><PERSON>ce dapibus, tellus ac cursus commodo, tortor mauris condimentum nibh, ut fermentum massa justo sit amet risus. Etiam porta sem malesuada magna mollis euismod. Donec sed odio dui.
      p
        a.btn.btn-secondary(href='#', role='button') View details »
    .col-sm-6
      h2 Heading
      p Donec id elit non mi porta gravida at eget metus. <PERSON><PERSON><PERSON> dapibus, tellus ac cursus commodo, tortor mauris condimentum nibh, ut fermentum massa justo sit amet risus. Etiam porta sem malesuada magna mollis euismod. Donec sed odio dui.
      p
        a.btn.btn-secondary(href='#', role='button') View details »
    .col-sm-6
      h2 Heading
      p Donec id elit non mi porta gravida at eget metus. Fusce dapibus, tellus ac cursus commodo, tortor mauris condimentum nibh, ut fermentum massa justo sit amet risus. Etiam porta sem malesuada magna mollis euismod. Donec sed odio dui.
      p
        a.btn.btn-secondary(href='#', role='button') View details »