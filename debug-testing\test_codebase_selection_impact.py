#!/usr/bin/env python3
"""
Test the impact of codebase selection by properly unselecting between tests
"""

import asyncio
import requests
import json
import time
import re

class CodebaseSelectionTester:
    """Test the impact of codebase selection with proper unselection"""
    
    def __init__(self):
        self.base_url = "http://home-ai-server.local:8080"
        self.api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
        self.model = "llama3:latest"
        self.tool_id = "code_analyzer_tool"
        
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        print(f"🔧 Codebase Selection Impact Tester")
        print(f"   Tool ID: {self.tool_id}")
    
    def call_api(self, query: str, timeout: int = 120) -> dict:
        """Call OpenWebUI API"""
        
        payload = {
            "model": self.model,
            "messages": [{"role": "user", "content": query}],
            "tool_ids": [self.tool_id],
            "stream": False
        }
        
        print(f"\n🔧 API Call: '{query}'")
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/api/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=timeout
            )
            end_time = time.time()
            
            print(f"📊 Response: {response.status_code}, Time: {end_time - start_time:.2f}s")
            
            if response.status_code == 200:
                result = response.json()
                choices = result.get("choices", [])
                
                if choices:
                    message = choices[0].get("message", {})
                    content = message.get("content", "")
                    
                    # Show first 200 chars of response
                    print(f"💬 Response: {content[:200]}...")
                    
                    # Quick analysis
                    has_real_code = any(indicator in content.lower() for indicator in [
                        "tmwmem", "tmwdiag", "void *", "malloc", "struct", "context 1:"
                    ])
                    
                    is_generic = any(indicator in content.lower() for indicator in [
                        "tensorflow", "general programming", "typical implementation",
                        "common approach", "standard library"
                    ])
                    
                    has_codebase_info = any(indicator in content.lower() for indicator in [
                        "utils codebase", "chunks", "files", "languages"
                    ])
                    
                    print(f"🔍 Analysis: Real Code={has_real_code}, Generic={is_generic}, Codebase Info={has_codebase_info}")
                    
                    return {
                        "success": True,
                        "content": content,
                        "response_time": end_time - start_time,
                        "has_real_code": has_real_code,
                        "is_generic": is_generic,
                        "has_codebase_info": has_codebase_info
                    }
                else:
                    print("❌ No choices in response")
                    return {"success": False, "error": "No choices"}
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                return {"success": False, "error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            return {"success": False, "error": str(e)}

async def test_scenario_1_no_selection():
    """Test 1: Start fresh with no codebase selection"""
    print("🚀 SCENARIO 1: NO CODEBASE SELECTION")
    print("="*80)
    
    tester = CodebaseSelectionTester()
    
    # First, unselect any existing codebase
    print("\n📋 Step 1: Unselecting any existing codebase")
    unselect_result = tester.call_api("unselect codebase")
    
    if not unselect_result.get("success"):
        print("⚠️ Could not unselect codebase, proceeding anyway")
    
    # Test a query without codebase selection
    print("\n📋 Step 2: Testing query without codebase selection")
    test_result = tester.call_api("tmwmem_alloc")
    
    return {
        "scenario": "No Selection",
        "unselect_success": unselect_result.get("success", False),
        "test_result": test_result
    }

async def test_scenario_2_with_selection():
    """Test 2: Select utils codebase then test"""
    print("\n\n🚀 SCENARIO 2: WITH CODEBASE SELECTION")
    print("="*80)
    
    tester = CodebaseSelectionTester()
    
    # First, select utils codebase
    print("\n📋 Step 1: Selecting utils codebase")
    select_result = tester.call_api("select utils codebase")
    
    if not select_result.get("success"):
        print("❌ Failed to select codebase")
        return {"scenario": "With Selection", "select_success": False, "test_result": None}
    
    # Test the same query with codebase selection
    print("\n📋 Step 2: Testing query with codebase selection")
    test_result = tester.call_api("tmwmem_alloc")
    
    return {
        "scenario": "With Selection",
        "select_success": select_result.get("success", False),
        "test_result": test_result
    }

async def test_scenario_3_unselect_then_test():
    """Test 3: Unselect codebase and test again"""
    print("\n\n🚀 SCENARIO 3: UNSELECT AFTER SELECTION")
    print("="*80)
    
    tester = CodebaseSelectionTester()
    
    # Unselect codebase
    print("\n📋 Step 1: Unselecting codebase")
    unselect_result = tester.call_api("unselect codebase")
    
    # Test query after unselection
    print("\n📋 Step 2: Testing query after unselection")
    test_result = tester.call_api("tmwmem_alloc")
    
    return {
        "scenario": "After Unselect",
        "unselect_success": unselect_result.get("success", False),
        "test_result": test_result
    }

def compare_results(results):
    """Compare the results from different scenarios"""
    print(f"\n{'='*80}")
    print("📊 CODEBASE SELECTION IMPACT ANALYSIS")
    print(f"{'='*80}")
    
    for result in results:
        scenario = result["scenario"]
        test_result = result.get("test_result")
        
        if test_result and test_result.get("success"):
            has_real_code = test_result.get("has_real_code", False)
            is_generic = test_result.get("is_generic", False)
            has_codebase_info = test_result.get("has_codebase_info", False)
            response_time = test_result.get("response_time", 0)
            
            print(f"\n🎯 {scenario}:")
            print(f"   Success: ✅")
            print(f"   Real Code Content: {'✅' if has_real_code else '❌'}")
            print(f"   Generic Response: {'⚠️' if is_generic else '✅'}")
            print(f"   Codebase Info: {'✅' if has_codebase_info else '❌'}")
            print(f"   Response Time: {response_time:.2f}s")
            
            # Overall assessment
            if has_real_code and not is_generic:
                print(f"   Assessment: ✅ EXCELLENT - Real codebase analysis")
            elif has_codebase_info:
                print(f"   Assessment: ✅ GOOD - Has codebase information")
            elif is_generic:
                print(f"   Assessment: ❌ POOR - Generic response")
            else:
                print(f"   Assessment: ⚠️ UNCLEAR - Mixed signals")
        else:
            print(f"\n🎯 {scenario}:")
            print(f"   Success: ❌")
            print(f"   Error: {test_result.get('error', 'Unknown') if test_result else 'No result'}")
    
    # Summary
    print(f"\n🔍 SUMMARY:")
    
    successful_scenarios = [r for r in results if r.get("test_result", {}).get("success")]
    real_code_scenarios = [r for r in successful_scenarios if r.get("test_result", {}).get("has_real_code")]
    generic_scenarios = [r for r in successful_scenarios if r.get("test_result", {}).get("is_generic")]
    
    print(f"   Total Scenarios: {len(results)}")
    print(f"   Successful: {len(successful_scenarios)}")
    print(f"   With Real Code: {len(real_code_scenarios)}")
    print(f"   Generic Responses: {len(generic_scenarios)}")
    
    if len(real_code_scenarios) > 0:
        best_scenarios = [r["scenario"] for r in real_code_scenarios]
        print(f"   Best Performance: {', '.join(best_scenarios)}")
    
    if len(real_code_scenarios) == len(successful_scenarios):
        print(f"   ✅ CONCLUSION: Codebase selection doesn't affect quality - tool works consistently")
    elif len(real_code_scenarios) > len(successful_scenarios) // 2:
        print(f"   ⚠️ CONCLUSION: Codebase selection improves results")
    else:
        print(f"   ❌ CONCLUSION: Codebase selection is critical for good results")

async def main():
    """Main test execution"""
    print("🚀 TESTING CODEBASE SELECTION IMPACT")
    print("="*80)
    
    # Run all three scenarios
    scenario1 = await test_scenario_1_no_selection()
    await asyncio.sleep(3)  # Delay between tests
    
    scenario2 = await test_scenario_2_with_selection()
    await asyncio.sleep(3)  # Delay between tests
    
    scenario3 = await test_scenario_3_unselect_then_test()
    
    # Compare results
    results = [scenario1, scenario2, scenario3]
    compare_results(results)

if __name__ == "__main__":
    asyncio.run(main())
