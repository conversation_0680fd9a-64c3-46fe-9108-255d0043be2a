#!/usr/bin/env python3
"""
<PERSON>ript to fix embedding dimension mismatches by recreating affected collections
"""

import requests
import json
import time

# Configuration
CODE_ANALYZER_SERVER_URL = "http://home-ai-server:5002"

def test_codebase_compatibility(codebase_name):
    """Test if a codebase has embedding dimension issues"""
    print(f"🔍 Testing {codebase_name} for embedding compatibility...")
    
    try:
        payload = {
            "query": "test",
            "codebase_name": codebase_name,
            "n_results": 1
        }
        
        response = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/search_code",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60  # Increased for better reliability
        )
        
        if response.status_code == 200:
            data = response.json()
            result = data.get("result", "")
            
            if "dimension" in result.lower() and ("mismatch" in result.lower() or "does not match" in result.lower()):
                print(f"❌ {codebase_name}: Embedding dimension mismatch detected")
                return False
            else:
                print(f"✅ {codebase_name}: Compatible")
                return True
        else:
            print(f"⚠️ {codebase_name}: HTTP {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ {codebase_name}: Error testing - {e}")
        return None

def delete_codebase(codebase_name):
    """Delete a codebase"""
    print(f"🗑️ Deleting {codebase_name}...")
    
    try:
        payload = {"codebase_name": codebase_name}
        response = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/delete_codebase",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60  # Increased for better reliability
        )
        
        if response.status_code == 200:
            print(f"✅ {codebase_name}: Deleted successfully")
            return True
        else:
            print(f"❌ {codebase_name}: Delete failed - HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ {codebase_name}: Delete error - {e}")
        return False

def process_codebase(codebase_name):
    """Process/reindex a codebase"""
    print(f"⚙️ Processing {codebase_name}...")
    
    try:
        payload = {
            "codebase_name": codebase_name,
            "exclude_dirs": ["build", "test", "bin", "obj", "__pycache__", ".git"]
        }
        response = requests.post(
            f"{CODE_ANALYZER_SERVER_URL}/tools/process_codebase",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=300  # 5 minutes timeout for processing
        )
        
        if response.status_code == 200:
            print(f"✅ {codebase_name}: Processed successfully")
            return True
        else:
            print(f"❌ {codebase_name}: Process failed - HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ {codebase_name}: Process error - {e}")
        return False

def get_codebases():
    """Get list of available codebases"""
    try:
        response = requests.post(f"{CODE_ANALYZER_SERVER_URL}/tools/list_codebases", timeout=30)  # Increased for better reliability
        if response.status_code == 200:
            data = response.json()
            result = data.get("result", "")
            
            # Extract codebase names from the result
            # This is a simple parser - adjust if the format changes
            codebases = []
            lines = result.split('\n')
            for line in lines:
                if '. ' in line and '(' in line and ')' in line:
                    # Extract name between number and status
                    parts = line.split('. ', 1)
                    if len(parts) > 1:
                        name_part = parts[1].split(' (')[0].strip()
                        if name_part:
                            codebases.append(name_part)
            
            return codebases
        else:
            print(f"❌ Failed to list codebases: HTTP {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Error listing codebases: {e}")
        return []

def fix_embedding_dimensions():
    """Main function to fix embedding dimension issues"""
    print("🚀 Code Analysis Server Embedding Dimension Fix")
    print("=" * 50)
    
    # Get all codebases
    print("📚 Getting list of codebases...")
    codebases = get_codebases()
    
    if not codebases:
        print("❌ No codebases found or unable to retrieve list")
        return
    
    print(f"Found {len(codebases)} codebases: {', '.join(codebases)}")
    
    # Test each codebase for compatibility
    print("\n🔍 Testing codebases for embedding compatibility...")
    problematic_codebases = []
    
    for codebase in codebases:
        compatibility = test_codebase_compatibility(codebase)
        if compatibility is False:
            problematic_codebases.append(codebase)
    
    if not problematic_codebases:
        print("\n✅ All codebases are compatible! No fixes needed.")
        return
    
    print(f"\n⚠️ Found {len(problematic_codebases)} codebases with embedding dimension issues:")
    for cb in problematic_codebases:
        print(f"   • {cb}")
    
    # Ask for confirmation
    print(f"\n🔧 This will delete and recreate {len(problematic_codebases)} codebase(s).")
    print("   Source code files will NOT be deleted, only the indexed data.")
    confirm = input("   Continue? (yes/no): ").strip().lower()
    
    if confirm != 'yes':
        print("❌ Operation cancelled")
        return
    
    # Fix each problematic codebase
    print(f"\n🔧 Fixing {len(problematic_codebases)} codebase(s)...")
    
    for codebase in problematic_codebases:
        print(f"\n{'='*30}")
        print(f"🔧 Fixing: {codebase}")
        print(f"{'='*30}")
        
        # Step 1: Delete
        if delete_codebase(codebase):
            # Step 2: Wait a moment
            time.sleep(2)
            
            # Step 3: Process
            if process_codebase(codebase):
                # Step 4: Test again
                time.sleep(2)
                if test_codebase_compatibility(codebase):
                    print(f"✅ {codebase}: Successfully fixed!")
                else:
                    print(f"❌ {codebase}: Still has issues after fix")
            else:
                print(f"❌ {codebase}: Failed to reprocess")
        else:
            print(f"❌ {codebase}: Failed to delete")
    
    print(f"\n🎉 Embedding dimension fix completed!")
    print("   You can now use search_code() and ask_about_code() functions normally.")

if __name__ == "__main__":
    fix_embedding_dimensions()
