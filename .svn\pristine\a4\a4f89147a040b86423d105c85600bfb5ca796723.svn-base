# OpenWebUI Code Analyzer Test Suite

Comprehensive testing framework for the OpenWebUI Code Analyzer Tool and Server, built by analyzing existing debug/test scripts and creating a unified testing approach.

## 🎯 Overview

This test suite provides comprehensive testing for:
- **Code Analyzer Server APIs** - All endpoints and functionality
- **OpenWebUI Plugin/Tool** - Plugin functionality and integration
- **Intelligent Caching System** - Performance and reliability
- **End-to-End Integration** - Complete workflow testing
- **Performance Benchmarking** - Response times and optimization
- **Regression Testing** - Prevent functionality breakage

## 📁 Test Suite Structure

```
├── test_suite.py              # Main unit test suite
├── performance_test_suite.py  # Performance and benchmark tests
├── run_all_tests.py          # Comprehensive test orchestrator
├── test_config.json          # Test configuration and settings
├── debug-testing/            # Existing debug/test scripts (inspiration source)
├── test_reports/             # Generated test reports
└── TEST_SUITE_README.md      # This documentation
```

## 🚀 Quick Start

### Run All Tests
```bash
python run_all_tests.py
```

### Run Specific Test Categories
```bash
# Quick smoke tests (2-3 minutes)
python run_all_tests.py --quick

# Comprehensive test suite (15-20 minutes)
python run_all_tests.py --full

# Performance tests only
python run_all_tests.py --performance

# Regression tests only
python run_all_tests.py --regression
```

### Run Individual Test Suites
```bash
# Unit tests
python test_suite.py                    # All unit tests
python test_suite.py TestServer         # Server tests only
python test_suite.py TestPlugin         # Plugin tests only
python test_suite.py TestCaching        # Caching tests only
python test_suite.py TestIntegration    # Integration tests only

# Performance tests
python performance_test_suite.py                # All performance tests
python performance_test_suite.py --benchmark    # Benchmarks only
python performance_test_suite.py --cache-test   # Cache performance only
```

## 🧪 Test Categories

### 1. Smoke Tests (Critical)
**Purpose**: Quick verification that basic functionality works
**Duration**: ~2 minutes
**Tests**:
- Server health check
- Basic connectivity
- Core endpoint availability

```bash
python run_all_tests.py --quick
```

### 2. Unit Tests
**Purpose**: Test individual components in isolation
**Duration**: ~5 minutes
**Coverage**:
- Server API endpoints (`TestCodeAnalyzerServer`)
- Plugin functionality (`TestOpenWebUIPlugin`) 
- Caching system (`TestIntelligentCaching`)

```bash
python test_suite.py
```

### 3. Integration Tests
**Purpose**: Test component interactions and workflows
**Duration**: ~8 minutes
**Coverage**:
- End-to-end workflows
- Plugin ↔ Server communication
- Cache integration
- Automatic context injection

```bash
python test_suite.py TestIntegration
```

### 4. Performance Tests
**Purpose**: Benchmark performance and validate optimizations
**Duration**: ~10 minutes
**Coverage**:
- Response time benchmarks
- Cache performance validation
- Memory usage monitoring
- Throughput testing

```bash
python performance_test_suite.py
```

### 5. Regression Tests
**Purpose**: Prevent functionality breakage
**Duration**: ~5 minutes
**Coverage**:
- Language support
- Enhanced search functionality
- Codebase selection

```bash
python run_all_tests.py --regression
```

## 📊 Test Results and Reporting

### Automatic Report Generation
```bash
# Generate detailed report
python run_all_tests.py --report

# Save performance baseline
python run_all_tests.py --save-baseline
```

### Report Formats
- **Markdown**: Human-readable summary
- **JSON**: Machine-readable data
- **Console**: Real-time output

### Sample Report Structure
```
# Test Execution Report
Generated: 2024-01-15 14:30:00
Duration: 847.3 seconds

## Summary
- Total Tests: 45
- Passed: 42 (93.3%)
- Failed: 3
- Success Rate: 93.3%

## Results by Category
- Server: 12/12 passed (100.0%) - 156.2s
- Plugin: 8/10 passed (80.0%) - 203.1s
- Caching: 5/5 passed (100.0%) - 89.7s
- Integration: 6/6 passed (100.0%) - 298.9s
- Performance: 11/12 passed (91.7%) - 99.4s
```

## ⚙️ Configuration

### Test Environment Configuration
Edit `test_config.json`:
```json
{
  "test_environment": {
    "server_url": "http://home-ai-server.local:5002",
    "openwebui_url": "http://home-ai-server.local:8080",
    "test_codebase": "utils"
  }
}
```

### Performance Thresholds
```json
{
  "performance_thresholds": {
    "avg_response_time": 3.0,
    "p95_response_time": 5.0,
    "cache_hit_improvement": 0.5,
    "memory_usage_mb": 500,
    "error_rate": 0.05
  }
}
```

## 🔧 Development Workflow

### Before Making Changes
```bash
# Run smoke tests to ensure system is working
python run_all_tests.py --quick

# Save current performance baseline
python run_all_tests.py --save-baseline
```

### After Making Changes
```bash
# Run relevant test category
python test_suite.py TestServer  # If you changed server code
python test_suite.py TestPlugin  # If you changed plugin code

# Run integration tests
python test_suite.py TestIntegration

# Check for performance regressions
python performance_test_suite.py --benchmark
```

### Before Deployment
```bash
# Run comprehensive test suite
python run_all_tests.py --full

# Generate deployment report
python run_all_tests.py --report
```

## 🐛 Debugging Failed Tests

### Common Issues and Solutions

**Server Connection Failures**:
```bash
# Check server status
python debug-testing/check_server_status.py

# Verify server is running
curl http://home-ai-server.local:5002/health
```

**Plugin Test Failures**:
```bash
# Test plugin in isolation
python debug-testing/tool_test_harness.py

# Check OpenWebUI connectivity
python debug-testing/openwebui_auto_tester.py
```

**Performance Test Failures**:
```bash
# Check system resources
python debug-testing/check_server_chromadb.py

# Verify cache functionality
python test_suite.py TestIntelligentCaching.test_cache_hit_performance
```

### Verbose Output
```bash
# Run tests with detailed output
python test_suite.py -v

# Run with buffer disabled (see output immediately)
python -u test_suite.py
```

## 📈 Performance Monitoring

### Key Metrics Tracked
- **Response Times**: Mean, median, P95, P99
- **Cache Performance**: Hit rates, improvement ratios
- **Memory Usage**: Peak usage, memory leaks
- **Error Rates**: Success/failure ratios
- **Throughput**: Requests per second

### Performance Baselines
The test suite automatically tracks performance baselines and can detect regressions:

```bash
# Save current performance as baseline
python run_all_tests.py --save-baseline

# Compare against baseline
python performance_test_suite.py --benchmark
```

## 🤝 Contributing

### Adding New Tests

1. **Unit Tests**: Add to `test_suite.py`
2. **Performance Tests**: Add to `performance_test_suite.py`
3. **Integration Tests**: Add to `TestIntegration` class
4. **Configuration**: Update `test_config.json`

### Test Naming Convention
- `test_<functionality>_<scenario>()` for unit tests
- `test_<component>_performance()` for performance tests
- `test_<workflow>_integration()` for integration tests

### Example New Test
```python
def test_new_feature_functionality(self):
    """Test new feature works correctly"""
    # Arrange
    test_data = self.setup_test_data()
    
    # Act
    result = self.plugin.new_feature(test_data)
    
    # Assert
    self.assertIsNotNone(result)
    self.assertIn('expected_content', result)
```

## 📚 Inspiration Sources

This test suite was built by analyzing and consolidating patterns from existing debug/test scripts:

- `debug-testing/openwebui_auto_tester.py` - OpenWebUI API testing patterns
- `debug-testing/test_code_analyzer_server_apis.py` - Server API testing
- `debug-testing/test_collections.py` - Collection and performance testing
- `debug-testing/tool_test_harness.py` - Plugin testing framework
- `debug-testing/verify_complete_functionality.py` - Integration testing

The unified test suite provides all the functionality of these individual scripts in a structured, maintainable framework.

## 🎉 Success Criteria

A successful test run should show:
- ✅ All critical tests passing
- ✅ Performance within acceptable thresholds
- ✅ No memory leaks or resource issues
- ✅ Cache hit rates > 70% for repeated queries
- ✅ Average response times < 3 seconds
- ✅ Error rates < 5%

Run `python run_all_tests.py --full` to validate your system meets all criteria!
