#!/usr/bin/env python3
"""
Verify the tool update is working
"""

import requests
import json

OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def test_tool_fixes():
    """Test the specific fixes we made"""
    print("🔍 Testing Tool Fixes After Update")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test the specific fix: select codebase command
    print("\n🧪 Testing Select Codebase Fix")
    print("-" * 40)
    
    try:
        payload = {
            "model": "llama3:latest",
            "messages": [{"role": "user", "content": "select codebase utils"}],
            "tool_ids": ["codebase_analyzer"],
            "stream": False,
            "max_tokens": 500
        }
        
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            print(f"✅ Response received ({len(content)} chars)")
            
            # Check for the old error vs new success
            if "error message from the `code_analyzer_tools/select_codebase` tool" in content:
                print("❌ OLD ERROR STILL PRESENT: Tool not updated")
                print("🔧 You need to copy the updated tool code to OpenWebUI")
                return False
            elif any(indicator in content for indicator in ["✅", "Selected", "utils", "ready_enhanced"]):
                print("🎉 SUCCESS: Tool fix is working!")
                print("✅ Select codebase command now works correctly")
                return True
            else:
                print("⚠️ UNCLEAR: Response doesn't show clear success or old error")
                print(f"Response preview: {content[:200]}...")
                return None
                
        else:
            print(f"❌ HTTP {response.status_code}: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_basic_functionality():
    """Test basic tool functionality"""
    print(f"\n🧪 Testing Basic Tool Functionality")
    print("-" * 40)
    
    session = requests.Session()
    session.headers.update({"Authorization": f"Bearer {API_KEY}"})
    
    # Test basic commands
    basic_tests = [
        ("status", "System status"),
        ("list codebases", "List codebases"),
    ]
    
    results = []
    
    for command, description in basic_tests:
        print(f"\n🔍 Testing: '{command}' ({description})")
        
        try:
            payload = {
                "model": "llama3:latest",
                "messages": [{"role": "user", "content": command}],
                "tool_ids": ["codebase_analyzer"],
                "stream": False,
                "max_tokens": 500
            }
            
            response = session.post(
                f"{OPENWEBUI_URL}/api/chat/completions",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                # Check for tool indicators
                tool_indicators = ["🔧", "📚", "✅", "Available Codebases", "Tool Server"]
                found = any(ind in content for ind in tool_indicators)
                
                if found:
                    print("✅ Tool working")
                    results.append(True)
                else:
                    print("⚠️ Generic response (tool may not be active)")
                    results.append(False)
                    
            else:
                print(f"❌ HTTP {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append(False)
    
    return results

def main():
    """Main verification function"""
    print("🔄 Tool Update Verification")
    print("=" * 70)
    print("Run this AFTER copying the updated tool to OpenWebUI")
    
    # Test the specific fix
    fix_working = test_tool_fixes()
    
    # Test basic functionality
    basic_results = test_basic_functionality()
    basic_working = all(basic_results)
    
    # Summary
    print(f"\n📊 VERIFICATION RESULTS")
    print("=" * 50)
    
    if fix_working is True:
        print("✅ SELECT CODEBASE FIX: Working correctly")
    elif fix_working is False:
        print("❌ SELECT CODEBASE FIX: Still has old error - tool not updated")
    else:
        print("⚠️ SELECT CODEBASE FIX: Unclear result")
    
    if basic_working:
        print("✅ BASIC FUNCTIONALITY: All tests passed")
    else:
        print("❌ BASIC FUNCTIONALITY: Some tests failed")
    
    print(f"\n🎯 OVERALL STATUS:")
    if fix_working is True and basic_working:
        print("🎉 EXCELLENT: Tool update successful!")
        print("✅ All fixes are working")
        print("✅ Ready for comprehensive testing")
        
        print(f"\n🚀 Next Steps:")
        print("   python debug-testing/openwebui_auto_tester.py")
        print("   # Select category 2 (Management) to test all fixes")
        
    elif fix_working is False:
        print("🔧 ACTION NEEDED: Update tool in OpenWebUI")
        print("   1. Go to http://home-ai-server.local:8080")
        print("   2. Settings → Functions")
        print("   3. Edit/replace your tool with updated code")
        print("   4. Re-run this verification")
        
    else:
        print("⚠️ MIXED RESULTS: Some issues remain")
        print("   Check tool installation and configuration")

if __name__ == "__main__":
    main()
