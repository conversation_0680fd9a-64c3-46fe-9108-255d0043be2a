{".class": "MypyFile", "_fullname": "deploy_enhanced_framework", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "RemoteFrameworkDeployer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer", "name": "RemoteFrameworkDeployer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "deploy_enhanced_framework", "mro": ["deploy_enhanced_framework.RemoteFrameworkDeployer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "remote_host", "remote_user", "remote_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "remote_host", "remote_user", "remote_path"], "arg_types": ["deploy_enhanced_framework.RemoteFrameworkDeployer", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RemoteFrameworkDeployer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_copy_files_to_remote": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer._copy_files_to_remote", "name": "_copy_files_to_remote", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["deploy_enhanced_framework.RemoteFrameworkDeployer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy_files_to_remote of RemoteFrameworkDeployer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_run_remote_command": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "command", "cwd"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer._run_remote_command", "name": "_run_remote_command", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "command", "cwd"], "arg_types": ["deploy_enhanced_framework.RemoteFrameworkDeployer", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_run_remote_command of RemoteFrameworkDeployer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "subprocess.CompletedProcess"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_docker_image": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer.build_docker_image", "name": "build_docker_image", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["deploy_enhanced_framework.RemoteFrameworkDeployer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_docker_image of RemoteFrameworkDeployer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_deployment_package": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer.create_deployment_package", "name": "create_deployment_package", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["deploy_enhanced_framework.RemoteFrameworkDeployer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_deployment_package of RemoteFrameworkDeployer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deploy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer.deploy", "name": "deploy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["deploy_enhanced_framework.RemoteFrameworkDeployer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deploy of RemoteFrameworkDeployer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deploy_container": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer.deploy_container", "name": "deploy_container", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["deploy_enhanced_framework.RemoteFrameworkDeployer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deploy_container of RemoteFrameworkDeployer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deployment_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer.deployment_config", "name": "deployment_config", "type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "project_root": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer.project_root", "name": "project_root", "type": "pathlib.Path"}}, "remote_host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer.remote_host", "name": "remote_host", "type": "builtins.str"}}, "remote_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer.remote_path", "name": "remote_path", "type": "builtins.str"}}, "remote_target": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer.remote_target", "name": "remote_target", "type": "builtins.str"}}, "remote_user": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer.remote_user", "name": "remote_user", "type": "builtins.str"}}, "run_framework_tests": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer.run_framework_tests", "name": "run_framework_tests", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["deploy_enhanced_framework.RemoteFrameworkDeployer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_framework_tests of RemoteFrameworkDeployer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stop_existing_container": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer.stop_existing_container", "name": "stop_existing_container", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["deploy_enhanced_framework.RemoteFrameworkDeployer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stop_existing_container of RemoteFrameworkDeployer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_dockerfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer.update_dockerfile", "name": "update_dockerfile", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["deploy_enhanced_framework.RemoteFrameworkDeployer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_dockerfile of RemoteFrameworkDeployer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "verify_deployment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer.verify_deployment", "name": "verify_deployment", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["deploy_enhanced_framework.RemoteFrameworkDeployer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify_deployment of RemoteFrameworkDeployer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "verify_framework_components": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer.verify_framework_components", "name": "verify_framework_components", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["deploy_enhanced_framework.RemoteFrameworkDeployer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify_framework_components of RemoteFrameworkDeployer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "deploy_enhanced_framework.RemoteFrameworkDeployer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "deploy_enhanced_framework.RemoteFrameworkDeployer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "deploy_enhanced_framework.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "deploy_enhanced_framework.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "deploy_enhanced_framework.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "deploy_enhanced_framework.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "deploy_enhanced_framework.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "deploy_enhanced_framework.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "deploy_enhanced_framework.main", "name": "main", "type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}, "subprocess": {".class": "SymbolTableNode", "cross_ref": "subprocess", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\deploy_enhanced_framework.py"}