#!/usr/bin/env python3
"""
Test OpenWebUI tool integration specifically
Tests the code_analyzer_tool through OpenWebUI API at home-ai-server.local:8080
"""

import requests
import json
import time

class OpenWebUIToolTester:
    def __init__(self):
        self.base_url = "http://home-ai-server.local:8080"
        self.api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
        self.tool_id = "code_analyzer_tool"
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        })
    
    def test_openwebui_health(self):
        """Test if OpenWebUI is accessible"""
        try:
            # Try the main page first
            response = self.session.get(f"{self.base_url}")
            if response.status_code == 200:
                print(f"✅ OpenWebUI accessible (main page)")
                return True
            else:
                print(f"❌ OpenWebUI health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Cannot connect to OpenWebUI: {e}")
            return False
    
    def test_tool_availability(self):
        """Test if the code_analyzer_tool is available"""
        try:
            # Try to get tools list (if endpoint exists)
            response = self.session.get(f"{self.base_url}/api/v1/tools")
            if response.status_code == 200:
                tools = response.json()
                print(f"✅ Tools endpoint accessible, found tools: {list(tools.keys()) if isinstance(tools, dict) else 'N/A'}")
                return True
            else:
                print(f"⚠️ Tools endpoint returned: {response.status_code}")
                return False
        except Exception as e:
            print(f"⚠️ Tools endpoint test failed: {e}")
            return False
    
    def make_tool_call(self, message, timeout=60):
        """Make a tool call through OpenWebUI API"""
        payload = {
            "model": "llama3:latest",
            "messages": [
                {
                    "role": "user",
                    "content": message
                }
            ],
            "tool_ids": [self.tool_id],
            "stream": False
        }
        
        print(f"🔧 Making API call to OpenWebUI...")
        print(f"🔧 Message: '{message}'")
        print(f"🔧 Tool ID: {self.tool_id}")
        
        try:
            start_time = time.time()
            response = self.session.post(
                f"{self.base_url}/api/chat/completions",
                json=payload,
                timeout=timeout
            )
            elapsed = time.time() - start_time
            
            print(f"📊 Response: {response.status_code}, Time: {elapsed:.2f}s")
            
            if response.status_code == 200:
                data = response.json()
                content = data.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                # Check for tool usage indicators
                tool_used = any(indicator in content.lower() for indicator in [
                    'code_analyzer_tool', 'codebase', 'chunks', 'enhanced search', 
                    'file:', 'function:', 'result 1', 'result 2'
                ])
                
                print(f"💬 Response length: {len(content)} chars")
                print(f"🔍 Tool used: {'✅' if tool_used else '❌'}")
                print(f"💬 Content preview: {content[:200]}...")
                
                return {
                    "success": True,
                    "content": content,
                    "tool_used": tool_used,
                    "response_time": elapsed
                }
            else:
                print(f"❌ API call failed: {response.status_code}")
                print(f"❌ Error: {response.text}")
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}",
                    "response_time": elapsed
                }
                
        except Exception as e:
            print(f"❌ API call exception: {e}")
            return {
                "success": False,
                "error": str(e),
                "response_time": 0
            }
    
    def run_comprehensive_test(self):
        """Run comprehensive OpenWebUI tool integration test"""
        print("🚀 Testing OpenWebUI Tool Integration")
        print("="*60)
        
        # 1. Test OpenWebUI accessibility
        print("\n📡 STEP 1: Testing OpenWebUI accessibility")
        if not self.test_openwebui_health():
            print("❌ OpenWebUI not accessible, aborting tests")
            return
        
        # 2. Test tool availability
        print("\n🔧 STEP 2: Testing tool availability")
        self.test_tool_availability()
        
        # 3. Test explicit tool commands
        print("\n🎯 STEP 3: Testing explicit tool commands")
        
        test_cases = [
            "list codebases",
            "select utils codebase", 
            "search for tmwmem_alloc",
            "get code context for memory allocation",
            "show codebase statistics"
        ]
        
        results = []
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n--- Test {i}: '{test_case}' ---")
            result = self.make_tool_call(test_case)
            results.append({
                "test_case": test_case,
                "result": result
            })
            time.sleep(2)  # Brief pause between tests
        
        # 4. Summary
        print("\n" + "="*60)
        print("📊 TEST SUMMARY")
        print("="*60)
        
        successful_calls = sum(1 for r in results if r["result"]["success"])
        tool_usage_count = sum(1 for r in results if r["result"].get("tool_used", False))
        
        print(f"✅ Successful API calls: {successful_calls}/{len(results)}")
        print(f"🔧 Tool usage detected: {tool_usage_count}/{len(results)}")
        
        if tool_usage_count == 0:
            print("\n⚠️ CRITICAL ISSUE: Tool is not being triggered!")
            print("   Possible causes:")
            print("   - Tool not properly installed in OpenWebUI")
            print("   - Tool ID mismatch")
            print("   - Tool server connectivity issues")
            print("   - Tool function registration problems")
        elif tool_usage_count < len(results):
            print(f"\n⚠️ PARTIAL ISSUE: Tool triggered {tool_usage_count}/{len(results)} times")
            print("   Some queries may not be triggering the tool properly")
        else:
            print("\n✅ SUCCESS: Tool is working correctly!")
        
        return results

if __name__ == "__main__":
    tester = OpenWebUIToolTester()
    tester.run_comprehensive_test()
