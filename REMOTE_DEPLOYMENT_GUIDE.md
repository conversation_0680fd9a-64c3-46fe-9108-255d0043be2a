# 🚀 Remote Deployment Guide: Lynn-PC to Home-AI-Server

Deploy the Enhanced Language-Agnostic Framework from your laptop (lynn-pc) to home-ai-server Docker environment.

## 📋 Prerequisites

### 1. SSH Key Authentication
Ensure you have SSH key authentication set up:
```bash
# Test SSH connection (should not prompt for password)
ssh fvaneijk@home-ai-server "echo 'Connection successful'"
```

### 2. Docker Access on Home-AI-Server
Verify Docker is accessible:
```bash
ssh fvaneijk@home-ai-server "docker --version"
ssh fvaneijk@home-ai-server "docker-compose --version"
```

## 🔧 Deployment Process

### Step 1: Test Remote Connection
```bash
# Run connection tests first
python test_remote_connection.py
```

This will verify:
- ✅ SSH connectivity
- ✅ Docker access
- ✅ Docker Compose availability
- ✅ Remote directory access

### Step 2: Deploy Enhanced Framework
```bash
# Deploy with default settings
python deploy_enhanced_framework.py

# Or specify custom settings
python deploy_enhanced_framework.py \
    --remote-host home-ai-server \
    --remote-user fvaneijk \
    --remote-path /home/<USER>/home-ai-system/code_analyzer_server
```

## 📦 What Gets Deployed

### Framework Components
- ✅ **Language Registry**: 45 languages, 76 extensions
- ✅ **Processing Pipeline**: Multi-stage processing with dependencies
- ✅ **Chunk System**: 3 chunk types with intelligent generation
- ✅ **GPU Infrastructure**: Multi-GPU discovery and coordination
- ✅ **Framework Integration**: Unified system with fallback support

### Docker Configuration
- ✅ **Container**: `code-analyzer-server`
- ✅ **Port**: `5002`
- ✅ **Network**: `ollama-network`
- ✅ **Volumes**: `chroma_db`, `source_code`
- ✅ **Health Check**: Automatic monitoring

## 🔍 Verification Steps

### 1. Container Status
```bash
# Check if container is running
ssh fvaneijk@home-ai-server "docker ps | grep code-analyzer-server"
```

### 2. Health Check
```bash
# Test health endpoint
curl http://home-ai-server:5002/health
```

### 3. Framework Status
```bash
# Check framework components
curl http://home-ai-server:5002/tools/framework_status
```

### 4. GPU Infrastructure
```bash
# Check GPU discovery
curl http://home-ai-server:5002/tools/gpu_status
```

## 🌐 Access URLs

After successful deployment:

| Service | URL | Description |
|---------|-----|-------------|
| **Health Check** | `http://home-ai-server:5002/health` | Service health status |
| **Framework Status** | `http://home-ai-server:5002/tools/framework_status` | Framework component status |
| **GPU Status** | `http://home-ai-server:5002/tools/gpu_status` | GPU infrastructure status |
| **OpenWebUI** | `http://home-ai-server:8080` | Main chat interface |
| **Portainer** | `http://home-ai-server:9000` | Docker management |

## 🛠️ Troubleshooting

### SSH Connection Issues
```bash
# Generate SSH key if needed
ssh-keygen -t rsa -b 4096 -C "lynn-pc@deployment"

# Copy key to remote server
ssh-copy-id fvaneijk@home-ai-server
```

### Docker Permission Issues
```bash
# Add user to docker group on remote server
ssh fvaneijk@home-ai-server "sudo usermod -aG docker $USER"
```

### Container Not Starting
```bash
# Check container logs
ssh fvaneijk@home-ai-server "docker logs code-analyzer-server"

# Check docker-compose logs
ssh fvaneijk@home-ai-server "cd /home/<USER>/home-ai-system && docker-compose logs code-analyzer-server"
```

### Port Conflicts
```bash
# Check what's using port 5002
ssh fvaneijk@home-ai-server "netstat -tulpn | grep 5002"
```

## 🔄 Redeployment

To redeploy after changes:
```bash
# Full redeployment
python deploy_enhanced_framework.py

# Or manual steps
ssh fvaneijk@home-ai-server "cd /home/<USER>/home-ai-system && docker-compose down code-analyzer-server"
python deploy_enhanced_framework.py
```

## 📊 Deployment Features

### Remote Deployment Capabilities
- ✅ **File Synchronization**: Automatic file copy to remote server
- ✅ **Remote Command Execution**: All Docker commands run remotely
- ✅ **Error Handling**: Comprehensive error reporting
- ✅ **Health Verification**: Automatic deployment verification

### Framework Enhancements
- ✅ **45 Languages**: Complete language support
- ✅ **GPU Infrastructure**: Multi-GPU coordination
- ✅ **Processing Pipeline**: Intelligent processing stages
- ✅ **Chunk System**: Advanced chunk generation
- ✅ **Integration Layer**: Unified framework interface

## 🎯 Success Indicators

Deployment is successful when you see:
```
🎉 Enhanced Framework Deployment Complete!
============================================================
✅ Target: fvaneijk@home-ai-server
✅ Container: code-analyzer-server
✅ Port: 5002
✅ Health Check: http://home-ai-server:5002/health
✅ Framework Status: http://home-ai-server:5002/tools/framework_status
✅ GPU Status: http://home-ai-server:5002/tools/gpu_status
```

Your enhanced framework is now running on home-ai-server! 🚀
