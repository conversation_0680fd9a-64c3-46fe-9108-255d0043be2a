#!/usr/bin/env python3
"""
Test Docker network connectivity between OpenWebUI and code analyzer server
"""

import requests
import json
import time

def test_code_analyzer_direct():
    """Test direct access to code analyzer server from host"""
    print("🔧 Testing direct access to code analyzer server...")
    
    try:
        response = requests.get("http://home-ai-server.local:5002/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Direct access successful")
            print(f"   Status: {data.get('overall_status', 'unknown')}")
            print(f"   Codebases: {data.get('available_codebases', 0)}")
            print(f"   Enhanced: {data.get('enhanced_codebases', 0)}")
            return True
        else:
            print(f"❌ Direct access failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Direct access error: {e}")
        return False

def test_openwebui_tool_server_connectivity():
    """Test if OpenWebUI tool can reach the code analyzer server"""
    print("\n🔧 Testing OpenWebUI tool server connectivity...")
    
    # This simulates what the OpenWebUI tool does internally
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test with a specific query that should trigger the tool to connect to code analyzer
    payload = {
        "model": "llama3:latest",
        "messages": [
            {
                "role": "user",
                "content": "debug tool status and show server connectivity"
            }
        ],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False
    }
    
    try:
        print("🔧 Sending debug request to OpenWebUI...")
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            content = data.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            print(f"✅ OpenWebUI response received ({len(content)} chars)")
            
            # Look for connectivity indicators in the response
            if "code-analyzer-server:5002" in content:
                print("🔍 Found reference to code-analyzer-server:5002")
            if "connection" in content.lower() and "error" in content.lower():
                print("⚠️ Connection error detected in response")
            if "healthy" in content.lower():
                print("✅ Health status mentioned in response")
            
            # Print relevant parts of the response
            lines = content.split('\n')
            for line in lines:
                if any(keyword in line.lower() for keyword in ['server', 'connection', 'error', 'health', 'url']):
                    print(f"   📄 {line.strip()}")
            
            return True
        else:
            print(f"❌ OpenWebUI request failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ OpenWebUI request error: {e}")
        return False

def test_simple_tool_call():
    """Test a simple tool call to see basic functionality"""
    print("\n🔧 Testing simple tool call...")
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "llama3:latest",
        "messages": [
            {
                "role": "user",
                "content": "test server health check"
            }
        ],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=45
        )
        
        if response.status_code == 200:
            data = response.json()
            content = data.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            print(f"✅ Simple tool call successful")
            print(f"   Response length: {len(content)} chars")
            
            # Check for specific error patterns
            if "405" in content:
                print("⚠️ HTTP 405 error detected - method not allowed")
            if "connection" in content.lower() and "failed" in content.lower():
                print("⚠️ Connection failure detected")
            if "server" in content.lower() and "unavailable" in content.lower():
                print("⚠️ Server unavailable detected")
                
            return True
        else:
            print(f"❌ Simple tool call failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Simple tool call error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Docker Network Connectivity")
    print("="*60)
    
    # Test 1: Direct access to code analyzer
    direct_success = test_code_analyzer_direct()
    
    # Test 2: OpenWebUI tool connectivity
    tool_connectivity = test_openwebui_tool_server_connectivity()
    
    # Test 3: Simple tool call
    simple_call = test_simple_tool_call()
    
    print("\n" + "="*60)
    print("📊 CONNECTIVITY TEST SUMMARY")
    print("="*60)
    print(f"✅ Direct code analyzer access: {'✅' if direct_success else '❌'}")
    print(f"🔧 OpenWebUI tool connectivity: {'✅' if tool_connectivity else '❌'}")
    print(f"🎯 Simple tool call: {'✅' if simple_call else '❌'}")
    
    if direct_success and not tool_connectivity:
        print("\n🔍 DIAGNOSIS: Code analyzer server is healthy, but OpenWebUI tool cannot reach it")
        print("   This suggests a Docker network connectivity issue")
        print("   The tool is configured for: http://code-analyzer-server:5002")
        print("   But may need: http://home-ai-server.local:5002 or different network setup")
    elif direct_success and tool_connectivity:
        print("\n✅ DIAGNOSIS: All connectivity tests passed!")
    else:
        print("\n❌ DIAGNOSIS: Multiple connectivity issues detected")
