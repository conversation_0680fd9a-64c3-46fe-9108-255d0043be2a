{"export_date": "2025-07-02T13:49:16.190715", "total_chunks": 479, "statistics": {"languages": {"cpp": 15, "c": 460, "csharp": 4}, "complexity_distribution": {"low": 283, "medium": 72, "high": 76, "very_high": 48}, "semantic_tags": {"data_structures": 181, "memory_management": 65, "io_operations": 64, "testing": 38, "security_related": 24, "network_operations": 23, "system_calls": 17, "string_operations": 17, "error_handling": 9, "oop_concepts": 3, "thread_operations": 1}}, "chunks": [{"chunk_id": "7c3dfc76", "type": "function", "language": "cpp", "complexity_score": "low", "semantic_tags": ["memory_management"], "quality_score": "good", "file_path": "THtmwsim.cpp", "line_range": "44-48"}, {"chunk_id": "65dcf4e6", "type": "function", "language": "cpp", "complexity_score": "low", "semantic_tags": [], "quality_score": "good", "file_path": "THtmwsim.cpp", "line_range": "50-58"}, {"chunk_id": "65dcf4e6", "type": "function", "language": "cpp", "complexity_score": "low", "semantic_tags": ["memory_management"], "quality_score": "fair", "file_path": "THtmwsim.cpp", "line_range": "60-71"}, {"chunk_id": "65dcf4e6", "type": "function", "language": "cpp", "complexity_score": "medium", "semantic_tags": ["memory_management"], "quality_score": "fair", "file_path": "THtmwsim.cpp", "line_range": "106-131"}, {"chunk_id": "65dcf4e6", "type": "function", "language": "cpp", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "THtmwsim.cpp", "line_range": "295-302"}, {"chunk_id": "fbc559b6", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwappl.c", "line_range": "46-51"}, {"chunk_id": "fbc559b6", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwappl.c", "line_range": "54-66"}, {"chunk_id": "fbc559b6", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwappl.c", "line_range": "69-77"}, {"chunk_id": "a2672a14", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["memory_management", "error_handling"], "quality_score": "fair", "file_path": "tmwappl.c", "line_range": "118-143"}, {"chunk_id": "17cd04af", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwappl.c", "line_range": "146-155"}, {"chunk_id": "36e20fa0", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwappl.c", "line_range": "158-165"}, {"chunk_id": "c19b7189", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["io_operations"], "quality_score": "fair", "file_path": "tmwappl.c", "line_range": "168-202"}, {"chunk_id": "8f244f17", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwappl.c", "line_range": "205-226"}, {"chunk_id": "bd3c0aa7", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["io_operations", "data_structures"], "quality_score": "fair", "file_path": "tmwappl.h", "line_range": "70-79"}, {"chunk_id": "df0120eb", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["io_operations"], "quality_score": "poor", "file_path": "tmwchnl.c", "line_range": "35-97"}, {"chunk_id": "e1b9542e", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["io_operations"], "quality_score": "good", "file_path": "tmwchnl.c", "line_range": "100-115"}, {"chunk_id": "4530467f", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "good", "file_path": "tmwchnl.c", "line_range": "118-127"}, {"chunk_id": "9e8930f2", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwchnl.c", "line_range": "130-134"}, {"chunk_id": "4530467f", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwchnl.c", "line_range": "137-141"}, {"chunk_id": "d14d0814", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwchnl.c", "line_range": "144-151"}, {"chunk_id": "33270bc9", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwchnl.c", "line_range": "154-164"}, {"chunk_id": "88503454", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwchnl.c", "line_range": "167-174"}, {"chunk_id": "1301a4e9", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["io_operations"], "quality_score": "fair", "file_path": "tmwchnl.c", "line_range": "177-185"}, {"chunk_id": "ffd9a704", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["io_operations"], "quality_score": "fair", "file_path": "tmwchnl.c", "line_range": "188-196"}, {"chunk_id": "c91ec560", "type": "enum_specifier", "language": "c", "complexity_score": "high", "semantic_tags": ["network_operations", "data_structures", "testing"], "quality_score": "poor", "file_path": "tmwchnl.h", "line_range": "46-82"}, {"chunk_id": "a2457b3f", "type": "enum_specifier", "language": "c", "complexity_score": "high", "semantic_tags": ["network_operations", "data_structures"], "quality_score": "poor", "file_path": "tmwchnl.h", "line_range": "84-140"}, {"chunk_id": "1d44fde3", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwchnl.h", "line_range": "142-150"}, {"chunk_id": "232041a2", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwchnl.h", "line_range": "153-157"}, {"chunk_id": "01b5e2b3", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "good", "file_path": "tmwchnl.h", "line_range": "159-162"}, {"chunk_id": "794f4dff", "type": "struct_specifier", "language": "c", "complexity_score": "high", "semantic_tags": ["network_operations", "io_operations", "data_structures"], "quality_score": "fair", "file_path": "tmwchnl.h", "line_range": "247-330"}, {"chunk_id": "c2fad3dc", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwchnl.h", "line_range": "253-253"}, {"chunk_id": "bf2bb6a4", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwcrypto.c", "line_range": "94-99"}, {"chunk_id": "c49cb9d1", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["testing", "system_calls", "security_related", "string_operations", "io_operations", "memory_management"], "quality_score": "fair", "file_path": "tmwcrypto.c", "line_range": "186-298"}, {"chunk_id": "321ec621", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwcrypto.c", "line_range": "301-304"}, {"chunk_id": "876562df", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": [], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "307-382"}, {"chunk_id": "22b0383e", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["memory_management"], "quality_score": "fair", "file_path": "tmwcrypto.c", "line_range": "385-408"}, {"chunk_id": "b62c28c0", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["memory_management", "security_related", "data_structures"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "412-558"}, {"chunk_id": "3597b0ab", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["security_related"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "466-497"}, {"chunk_id": "3597b0ab", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["memory_management", "security_related", "data_structures"], "quality_score": "fair", "file_path": "tmwcrypto.c", "line_range": "499-514"}, {"chunk_id": "3597b0ab", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["memory_management", "security_related", "data_structures"], "quality_score": "fair", "file_path": "tmwcrypto.c", "line_range": "515-530"}, {"chunk_id": "8564290c", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwcrypto.c", "line_range": "561-590"}, {"chunk_id": "0abcf356", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["string_operations", "testing", "memory_management"], "quality_score": "good", "file_path": "tmwcrypto.c", "line_range": "598-614"}, {"chunk_id": "12ca5833", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["io_operations"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "618-686"}, {"chunk_id": "dac7a874", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["testing", "io_operations", "memory_management", "error_handling"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "690-876"}, {"chunk_id": "63797c1a", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["io_operations", "memory_management", "testing"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "879-1069"}, {"chunk_id": "4c395d13", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["testing", "data_structures", "security_related", "io_operations", "memory_management"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "1084-1412"}, {"chunk_id": "c49cb9d1", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["memory_management", "security_related"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "1228-1270"}, {"chunk_id": "c49cb9d1", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["io_operations", "memory_management", "security_related", "testing"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "1272-1395"}, {"chunk_id": "4c074be5", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["testing", "data_structures", "security_related", "io_operations", "memory_management"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "1415-1732"}, {"chunk_id": "c49cb9d1", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["memory_management", "security_related"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "1538-1585"}, {"chunk_id": "c49cb9d1", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["io_operations", "memory_management", "security_related", "testing"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "1589-1716"}, {"chunk_id": "5e302a7f", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwcrypto.c", "line_range": "1735-1764"}, {"chunk_id": "c7d4c632", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwcrypto.c", "line_range": "1766-1796"}, {"chunk_id": "e38bfb07", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["memory_management", "security_related"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "1799-1920"}, {"chunk_id": "eacad3fb", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["io_operations", "memory_management", "security_related"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "1923-2059"}, {"chunk_id": "f3b99faf", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["memory_management", "security_related"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "2062-2181"}, {"chunk_id": "986b340b", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["string_operations", "io_operations", "memory_management"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "2184-2341"}, {"chunk_id": "09f86a50", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["memory_management"], "quality_score": "fair", "file_path": "tmwcrypto.c", "line_range": "2346-2386"}, {"chunk_id": "e55e9470", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["memory_management"], "quality_score": "fair", "file_path": "tmwcrypto.c", "line_range": "2390-2408"}, {"chunk_id": "71b8093b", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["io_operations", "memory_management"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "2411-2486"}, {"chunk_id": "df0c8c07", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["testing"], "quality_score": "good", "file_path": "tmwcrypto.c", "line_range": "2491-2503"}, {"chunk_id": "2b5a5118", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["io_operations", "testing"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "2507-2569"}, {"chunk_id": "003de59b", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["string_operations", "io_operations", "memory_management", "data_structures"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "2572-2655"}, {"chunk_id": "a4f164e8", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["security_related"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "2658-2685"}, {"chunk_id": "e6f02e38", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["string_operations", "memory_management", "security_related"], "quality_score": "fair", "file_path": "tmwcrypto.c", "line_range": "2688-2718"}, {"chunk_id": "d9ed1334", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["testing", "data_structures", "security_related", "string_operations", "io_operations"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "2723-2994"}, {"chunk_id": "83c178c9", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["string_operations", "testing", "memory_management", "security_related"], "quality_score": "poor", "file_path": "tmwcrypto.c", "line_range": "3103-3553"}, {"chunk_id": "148553f3", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwcrypto.c", "line_range": "141-154"}, {"chunk_id": "13563beb", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "good", "file_path": "tmwcrypto.c", "line_range": "592-596"}, {"chunk_id": "a0467e54", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwcrypto.c", "line_range": "1072-1074"}, {"chunk_id": "5f0a188b", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwcrypto.c", "line_range": "2585-2585"}, {"chunk_id": "e33f1303", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwcrypto.c", "line_range": "2744-2744"}, {"chunk_id": "eb455406", "type": "enum_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["data_structures", "security_related"], "quality_score": "fair", "file_path": "tmwcrypto.h", "line_range": "43-79"}, {"chunk_id": "72d661f8", "type": "enum_specifier", "language": "c", "complexity_score": "high", "semantic_tags": ["network_operations", "data_structures", "security_related"], "quality_score": "poor", "file_path": "tmwcrypto.h", "line_range": "84-125"}, {"chunk_id": "a1e34a3e", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwcrypto.h", "line_range": "129-133"}, {"chunk_id": "43c88090", "type": "struct_specifier", "language": "c", "complexity_score": "high", "semantic_tags": ["data_structures", "security_related"], "quality_score": "fair", "file_path": "tmwcrypto.h", "line_range": "146-172"}, {"chunk_id": "118cf743", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdb.c", "line_range": "50-63"}, {"chunk_id": "f0bee0c2", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["memory_management"], "quality_score": "fair", "file_path": "tmwdb.c", "line_range": "66-82"}, {"chunk_id": "67bb8bea", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdb.c", "line_range": "85-95"}, {"chunk_id": "8384ab90", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdb.c", "line_range": "98-106"}, {"chunk_id": "bf5cdf8c", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "good", "file_path": "tmwdb.c", "line_range": "109-138"}, {"chunk_id": "54401e46", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["io_operations", "memory_management", "system_calls"], "quality_score": "fair", "file_path": "tmwdb.c", "line_range": "141-183"}, {"chunk_id": "2857f96d", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdb.c", "line_range": "186-189"}, {"chunk_id": "c3d7b612", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdb.c", "line_range": "192-195"}, {"chunk_id": "f0bee0c2", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdb.c", "line_range": "198-203"}, {"chunk_id": "3b2c54c9", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["memory_management"], "quality_score": "good", "file_path": "tmwdb.c", "line_range": "206-237"}, {"chunk_id": "5ed33ce9", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["io_operations", "data_structures"], "quality_score": "fair", "file_path": "tmwdb.c", "line_range": "37-45"}, {"chunk_id": "9cc430e8", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwdb.h", "line_range": "39-39"}, {"chunk_id": "2bad1cfb", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwdb.h", "line_range": "44-44"}, {"chunk_id": "08eb5929", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwdb.h", "line_range": "49-55"}, {"chunk_id": "1642e7db", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwdefs.h", "line_range": "225-231"}, {"chunk_id": "fe3597e8", "type": "enum_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwdefs.h", "line_range": "239-244"}, {"chunk_id": "e69bc27b", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwdefs.h", "line_range": "250-254"}, {"chunk_id": "12a0cac5", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwdefs.h", "line_range": "256-262"}, {"chunk_id": "42ac6ede", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "poor", "file_path": "tmwdefs.h", "line_range": "269-287"}, {"chunk_id": "ee719504", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwdefs.h", "line_range": "296-301"}, {"chunk_id": "53761ffc", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures", "system_calls"], "quality_score": "fair", "file_path": "tmwdefs.h", "line_range": "305-310"}, {"chunk_id": "67c65bdf", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures", "system_calls"], "quality_score": "fair", "file_path": "tmwdefs.h", "line_range": "312-318"}, {"chunk_id": "b501b6c8", "type": "enum_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwdefs.h", "line_range": "321-326"}, {"chunk_id": "8b7c5615", "type": "enum_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwdefs.h", "line_range": "329-338"}, {"chunk_id": "1384e03e", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["network_operations", "data_structures"], "quality_score": "poor", "file_path": "tmwdefs.h", "line_range": "343-356"}, {"chunk_id": "cb0c005e", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdiag.c", "line_range": "80-86"}, {"chunk_id": "234ec59f", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": [], "quality_score": "poor", "file_path": "tmwdiag.c", "line_range": "89-132"}, {"chunk_id": "9b5aac09", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["memory_management"], "quality_score": "poor", "file_path": "tmwdiag.c", "line_range": "135-170"}, {"chunk_id": "70749bcf", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["io_operations"], "quality_score": "fair", "file_path": "tmwdiag.c", "line_range": "173-218"}, {"chunk_id": "2d4c7700", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["io_operations"], "quality_score": "fair", "file_path": "tmwdiag.c", "line_range": "221-266"}, {"chunk_id": "2dc424bf", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["io_operations"], "quality_score": "fair", "file_path": "tmwdiag.c", "line_range": "269-315"}, {"chunk_id": "fd8b97e5", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["io_operations"], "quality_score": "fair", "file_path": "tmwdiag.c", "line_range": "318-335"}, {"chunk_id": "ebbf8895", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["io_operations"], "quality_score": "fair", "file_path": "tmwdiag.c", "line_range": "338-363"}, {"chunk_id": "6913e5a2", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdiag.c", "line_range": "366-371"}, {"chunk_id": "d5fc70b4", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["io_operations", "data_structures"], "quality_score": "good", "file_path": "tmwdiag.c", "line_range": "374-399"}, {"chunk_id": "7fdf0031", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["io_operations", "memory_management", "data_structures"], "quality_score": "good", "file_path": "tmwdiag.c", "line_range": "402-436"}, {"chunk_id": "1ba15413", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["io_operations", "security_related"], "quality_score": "fair", "file_path": "tmwdiag.c", "line_range": "441-558"}, {"chunk_id": "6a8ff187", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdlist.c", "line_range": "36-42"}, {"chunk_id": "67338633", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["memory_management"], "quality_score": "fair", "file_path": "tmwdlist.c", "line_range": "45-60"}, {"chunk_id": "ae7ac847", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdlist.c", "line_range": "63-66"}, {"chunk_id": "da80d9f9", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["testing", "error_handling"], "quality_score": "fair", "file_path": "tmwdlist.c", "line_range": "69-91"}, {"chunk_id": "9154baba", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdlist.c", "line_range": "94-134"}, {"chunk_id": "049a7d7f", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["testing", "error_handling"], "quality_score": "fair", "file_path": "tmwdlist.c", "line_range": "137-173"}, {"chunk_id": "656dcc5a", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["testing", "error_handling"], "quality_score": "fair", "file_path": "tmwdlist.c", "line_range": "176-209"}, {"chunk_id": "54a3a3b1", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["testing", "error_handling"], "quality_score": "poor", "file_path": "tmwdlist.c", "line_range": "212-263"}, {"chunk_id": "25e6c289", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdlist.c", "line_range": "353-362"}, {"chunk_id": "a89b326b", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwdlist.h", "line_range": "43-47"}, {"chunk_id": "88843ef5", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwdlist.h", "line_range": "44-44"}, {"chunk_id": "02faf7af", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwdlist.h", "line_range": "45-45"}, {"chunk_id": "4640c517", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwdlist.h", "line_range": "46-46"}, {"chunk_id": "e46ea1aa", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwdlist.h", "line_range": "50-54"}, {"chunk_id": "8529c8f8", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdtime.c", "line_range": "40-52"}, {"chunk_id": "d99ae1ef", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdtime.c", "line_range": "54-63"}, {"chunk_id": "ec7767be", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdtime.c", "line_range": "84-94"}, {"chunk_id": "972350d5", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdtime.c", "line_range": "112-134"}, {"chunk_id": "491e7ea4", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdtime.c", "line_range": "152-178"}, {"chunk_id": "42f9d309", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "poor", "file_path": "tmwdtime.c", "line_range": "186-197"}, {"chunk_id": "fe0704b9", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdtime.c", "line_range": "204-214"}, {"chunk_id": "399131c0", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "poor", "file_path": "tmwdtime.c", "line_range": "222-250"}, {"chunk_id": "f1bd12fd", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdtime.c", "line_range": "270-282"}, {"chunk_id": "22e36f87", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdtime.c", "line_range": "302-314"}, {"chunk_id": "14b3eeb1", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["testing"], "quality_score": "poor", "file_path": "tmwdtime.c", "line_range": "333-374"}, {"chunk_id": "e4b26c7d", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": [], "quality_score": "poor", "file_path": "tmwdtime.c", "line_range": "394-445"}, {"chunk_id": "8c8113fc", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "poor", "file_path": "tmwdtime.c", "line_range": "466-495"}, {"chunk_id": "5d76a243", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "poor", "file_path": "tmwdtime.c", "line_range": "505-529"}, {"chunk_id": "e9c5fc29", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "poor", "file_path": "tmwdtime.c", "line_range": "540-569"}, {"chunk_id": "701cb42a", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["testing", "data_structures"], "quality_score": "poor", "file_path": "tmwdtime.c", "line_range": "587-668"}, {"chunk_id": "e4f43860", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["testing", "data_structures"], "quality_score": "poor", "file_path": "tmwdtime.c", "line_range": "671-753"}, {"chunk_id": "cc5751d7", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwdtime.c", "line_range": "756-765"}, {"chunk_id": "6cf37d1b", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "poor", "file_path": "tmwdtime.h", "line_range": "40-59"}, {"chunk_id": "74fa9aa2", "type": "struct_specifier", "language": "c", "complexity_score": "high", "semantic_tags": ["network_operations", "data_structures"], "quality_score": "poor", "file_path": "tmwdtime.h", "line_range": "61-94"}, {"chunk_id": "5dd06bc8", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["network_operations"], "quality_score": "fair", "file_path": "tmwlink.c", "line_range": "48-92"}, {"chunk_id": "632e7424", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwlink.c", "line_range": "95-136"}, {"chunk_id": "4760901b", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwlink.c", "line_range": "139-164"}, {"chunk_id": "dd872b79", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["network_operations", "io_operations"], "quality_score": "fair", "file_path": "tmwlink.c", "line_range": "167-216"}, {"chunk_id": "54b02641", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwlink.c", "line_range": "219-236"}, {"chunk_id": "847997bb", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["io_operations"], "quality_score": "fair", "file_path": "tmwlink.c", "line_range": "239-251"}, {"chunk_id": "381a936a", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwlink.c", "line_range": "254-267"}, {"chunk_id": "177c6ec5", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["network_operations"], "quality_score": "good", "file_path": "tmwlink.c", "line_range": "270-290"}, {"chunk_id": "b7d5e96c", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwlink.c", "line_range": "293-301"}, {"chunk_id": "e5ea26b2", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["network_operations"], "quality_score": "fair", "file_path": "tmwlink.c", "line_range": "304-339"}, {"chunk_id": "fe27a3f9", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwlink.c", "line_range": "349-365"}, {"chunk_id": "c1cd9483", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwlink.h", "line_range": "40-40"}, {"chunk_id": "d90f94fe", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwlink.h", "line_range": "45-45"}, {"chunk_id": "042b84ce", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwlink.h", "line_range": "46-46"}, {"chunk_id": "ba424663", "type": "struct_specifier", "language": "c", "complexity_score": "high", "semantic_tags": ["testing", "data_structures"], "quality_score": "good", "file_path": "tmwlink.h", "line_range": "82-116"}, {"chunk_id": "c4f94a92", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwlink.h", "line_range": "84-84"}, {"chunk_id": "3e15b7f9", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["io_operations", "data_structures"], "quality_score": "fair", "file_path": "tmwlink.h", "line_range": "163-172"}, {"chunk_id": "452817fb", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwlink.h", "line_range": "185-185"}, {"chunk_id": "eb0fefee", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwlink.h", "line_range": "201-201"}, {"chunk_id": "734f2ebf", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["memory_management"], "quality_score": "fair", "file_path": "tmwmem.c", "line_range": "125-138"}, {"chunk_id": "b66f9200", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwmem.c", "line_range": "142-152"}, {"chunk_id": "cc427395", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwmem.c", "line_range": "155-165"}, {"chunk_id": "6f43e9f1", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["memory_management"], "quality_score": "fair", "file_path": "tmwmem.c", "line_range": "168-179"}, {"chunk_id": "7e296d4e", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "good", "file_path": "tmwmem.c", "line_range": "182-195"}, {"chunk_id": "b66f9200", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["memory_management", "data_structures"], "quality_score": "fair", "file_path": "tmwmem.c", "line_range": "200-236"}, {"chunk_id": "c97540ec", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwmem.c", "line_range": "239-249"}, {"chunk_id": "a74bd5a7", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["memory_management", "data_structures"], "quality_score": "good", "file_path": "tmwmem.c", "line_range": "252-279"}, {"chunk_id": "b52c88cf", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["memory_management", "data_structures"], "quality_score": "fair", "file_path": "tmwmem.c", "line_range": "281-324"}, {"chunk_id": "b66f9200", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwmem.c", "line_range": "326-329"}, {"chunk_id": "9c387521", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["memory_management"], "quality_score": "poor", "file_path": "tmwmem.c", "line_range": "331-390"}, {"chunk_id": "116226b3", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwmem.c", "line_range": "40-44"}, {"chunk_id": "daa8c972", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwmem.c", "line_range": "46-50"}, {"chunk_id": "0cece385", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwmem.c", "line_range": "52-56"}, {"chunk_id": "b08c78e8", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwmem.c", "line_range": "59-63"}, {"chunk_id": "d02c4c84", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwmem.c", "line_range": "65-69"}, {"chunk_id": "982c1579", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwmem.c", "line_range": "72-77"}, {"chunk_id": "51602c0e", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["io_operations", "data_structures"], "quality_score": "fair", "file_path": "tmwmem.c", "line_range": "111-117"}, {"chunk_id": "b656e9f7", "type": "enum_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwmem.h", "line_range": "51-63"}, {"chunk_id": "3f0d6828", "type": "struct_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwmem.h", "line_range": "65-76"}, {"chunk_id": "67615de9", "type": "struct_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["memory_management", "data_structures"], "quality_score": "fair", "file_path": "tmwmem.h", "line_range": "81-99"}, {"chunk_id": "52aacb43", "type": "struct_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["memory_management", "data_structures"], "quality_score": "fair", "file_path": "tmwmem.h", "line_range": "102-122"}, {"chunk_id": "8eb3e44a", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwmsim.c", "line_range": "91-105"}, {"chunk_id": "8eb3e44a", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwmsim.c", "line_range": "108-131"}, {"chunk_id": "8eb3e44a", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwmsim.c", "line_range": "134-158"}, {"chunk_id": "8eb3e44a", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwmsim.c", "line_range": "187-201"}, {"chunk_id": "8eb3e44a", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "good", "file_path": "tmwmsim.c", "line_range": "231-245"}, {"chunk_id": "8eb3e44a", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwmsim.c", "line_range": "248-272"}, {"chunk_id": "8eb3e44a", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwmsim.c", "line_range": "276-305"}, {"chunk_id": "8eb3e44a", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwmsim.c", "line_range": "308-334"}, {"chunk_id": "8eb3e44a", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["string_operations"], "quality_score": "fair", "file_path": "tmwmsim.c", "line_range": "364-378"}, {"chunk_id": "9f21801e", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwphys.c", "line_range": "94-119"}, {"chunk_id": "b1d726a7", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["io_operations", "testing"], "quality_score": "fair", "file_path": "tmwphys.c", "line_range": "128-208"}, {"chunk_id": "92bc24a6", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["io_operations"], "quality_score": "fair", "file_path": "tmwphys.c", "line_range": "217-226"}, {"chunk_id": "a73e9011", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["network_operations"], "quality_score": "good", "file_path": "tmwphys.c", "line_range": "235-245"}, {"chunk_id": "ac4e7657", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwphys.c", "line_range": "255-284"}, {"chunk_id": "5645f87d", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwphys.c", "line_range": "296-321"}, {"chunk_id": "2e146fbc", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwphys.c", "line_range": "333-353"}, {"chunk_id": "15c3f44d", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["network_operations", "io_operations", "system_calls"], "quality_score": "good", "file_path": "tmwphys.c", "line_range": "363-403"}, {"chunk_id": "f3d8ea11", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwphys.c", "line_range": "415-428"}, {"chunk_id": "f704a794", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["io_operations", "testing"], "quality_score": "fair", "file_path": "tmwphys.c", "line_range": "437-526"}, {"chunk_id": "7ab77cd2", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["network_operations", "system_calls"], "quality_score": "fair", "file_path": "tmwphys.c", "line_range": "536-587"}, {"chunk_id": "ce620f39", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["io_operations", "system_calls"], "quality_score": "poor", "file_path": "tmwphys.c", "line_range": "597-680"}, {"chunk_id": "90dfca2a", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["network_operations", "system_calls"], "quality_score": "fair", "file_path": "tmwphys.c", "line_range": "685-694"}, {"chunk_id": "afd12286", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["io_operations", "memory_management", "system_calls"], "quality_score": "fair", "file_path": "tmwphys.c", "line_range": "697-797"}, {"chunk_id": "c4f48962", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["io_operations", "memory_management"], "quality_score": "fair", "file_path": "tmwphys.c", "line_range": "800-846"}, {"chunk_id": "0ee2f762", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["io_operations", "system_calls"], "quality_score": "fair", "file_path": "tmwphys.c", "line_range": "849-888"}, {"chunk_id": "cdc074cb", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["system_calls"], "quality_score": "fair", "file_path": "tmwphys.c", "line_range": "891-902"}, {"chunk_id": "5ca05704", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["memory_management", "system_calls"], "quality_score": "good", "file_path": "tmwphys.c", "line_range": "905-945"}, {"chunk_id": "52dfe463", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["io_operations", "memory_management", "system_calls"], "quality_score": "fair", "file_path": "tmwphys.c", "line_range": "949-974"}, {"chunk_id": "88ccf8c7", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["testing", "data_structures"], "quality_score": "fair", "file_path": "tmwphys.h", "line_range": "41-53"}, {"chunk_id": "a61c69f3", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwphys.h", "line_range": "56-61"}, {"chunk_id": "95ac9cba", "type": "enum_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwphys.h", "line_range": "64-75"}, {"chunk_id": "7f2adf40", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwphys.h", "line_range": "79-84"}, {"chunk_id": "3c98f4e7", "type": "struct_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["network_operations", "data_structures", "system_calls"], "quality_score": "fair", "file_path": "tmwphys.h", "line_range": "121-151"}, {"chunk_id": "03c097fe", "type": "struct_specifier", "language": "c", "complexity_score": "high", "semantic_tags": ["testing", "data_structures", "system_calls", "network_operations", "io_operations"], "quality_score": "good", "file_path": "tmwphys.h", "line_range": "162-207"}, {"chunk_id": "6c0f7264", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwphys.h", "line_range": "168-168"}, {"chunk_id": "f39dcc6c", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwphys.h", "line_range": "236-242"}, {"chunk_id": "da1e8f1c", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwphys.h", "line_range": "244-244"}, {"chunk_id": "3512b278", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwphys.h", "line_range": "274-274"}, {"chunk_id": "e879796f", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwphys.h", "line_range": "277-277"}, {"chunk_id": "6545daa0", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwphys.h", "line_range": "292-292"}, {"chunk_id": "c96a959b", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwphys.h", "line_range": "305-305"}, {"chunk_id": "e2d54630", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwphys.h", "line_range": "317-317"}, {"chunk_id": "95214428", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwphys.h", "line_range": "332-332"}, {"chunk_id": "04c253cf", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwphys.h", "line_range": "334-334"}, {"chunk_id": "212f9387", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwphys.h", "line_range": "345-345"}, {"chunk_id": "5263e18f", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["io_operations"], "quality_score": "fair", "file_path": "tmwphysd.c", "line_range": "40-57"}, {"chunk_id": "8885292f", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["network_operations"], "quality_score": "fair", "file_path": "tmwphysd.c", "line_range": "59-82"}, {"chunk_id": "8aa05257", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["io_operations"], "quality_score": "fair", "file_path": "tmwphysd.c", "line_range": "85-117"}, {"chunk_id": "c3ffcfe3", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["io_operations"], "quality_score": "fair", "file_path": "tmwphysd.c", "line_range": "120-164"}, {"chunk_id": "fa1757cc", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["io_operations"], "quality_score": "fair", "file_path": "tmwphysd.c", "line_range": "167-211"}, {"chunk_id": "e482e3bc", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["io_operations"], "quality_score": "good", "file_path": "tmwphysd.c", "line_range": "214-231"}, {"chunk_id": "b781b5b8", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["io_operations"], "quality_score": "good", "file_path": "tmwphysd.c", "line_range": "234-251"}, {"chunk_id": "28d51071", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwpltmr.c", "line_range": "51-60"}, {"chunk_id": "46a70fba", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwpltmr.c", "line_range": "63-66"}, {"chunk_id": "46a70fba", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwpltmr.c", "line_range": "69-86"}, {"chunk_id": "e71fd519", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwpltmr.c", "line_range": "91-97"}, {"chunk_id": "4204a659", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwpltmr.c", "line_range": "39-45"}, {"chunk_id": "9036e02b", "type": "function", "language": "cpp", "complexity_score": "high", "semantic_tags": ["thread_operations"], "quality_score": "fair", "file_path": "tmwscl.cpp", "line_range": "36-57"}, {"chunk_id": "b98f8e05", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwscl.h", "line_range": "33-45"}, {"chunk_id": "50fccfd5", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsctr.c", "line_range": "34-44"}, {"chunk_id": "ce8bcfa6", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsctr.c", "line_range": "47-54"}, {"chunk_id": "1cc67c08", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsctr.c", "line_range": "58-68"}, {"chunk_id": "4334083c", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsctr.c", "line_range": "72-77"}, {"chunk_id": "eba45bdd", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "good", "file_path": "tmwsctr.c", "line_range": "81-89"}, {"chunk_id": "eba45bdd", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "good", "file_path": "tmwsctr.c", "line_range": "92-96"}, {"chunk_id": "a2ea175d", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsctr.c", "line_range": "100-105"}, {"chunk_id": "eba45bdd", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsctr.c", "line_range": "108-112"}, {"chunk_id": "b836e7a5", "type": "enum_specifier", "language": "c", "complexity_score": "high", "semantic_tags": ["io_operations", "data_structures"], "quality_score": "fair", "file_path": "tmwsctr.h", "line_range": "37-77"}, {"chunk_id": "c4e097e4", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "good", "file_path": "tmwsctr.h", "line_range": "80-86"}, {"chunk_id": "419503ba", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsctr.h", "line_range": "108-125"}, {"chunk_id": "32661149", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["testing", "error_handling"], "quality_score": "poor", "file_path": "tmwsesn.c", "line_range": "37-73"}, {"chunk_id": "e0d8d8e8", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsesn.c", "line_range": "76-80"}, {"chunk_id": "ca6d5010", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsesn.c", "line_range": "83-87"}, {"chunk_id": "673cec0b", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsesn.c", "line_range": "90-94"}, {"chunk_id": "566c3a51", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsesn.c", "line_range": "97-104"}, {"chunk_id": "ec986d5d", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsesn.c", "line_range": "107-117"}, {"chunk_id": "673cec0b", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "good", "file_path": "tmwsesn.c", "line_range": "121-129"}, {"chunk_id": "673cec0b", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "good", "file_path": "tmwsesn.c", "line_range": "132-142"}, {"chunk_id": "5b5eddb3", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsesn.c", "line_range": "146-166"}, {"chunk_id": "c5bbb9b3", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["testing", "error_handling"], "quality_score": "good", "file_path": "tmwsesn.c", "line_range": "169-194"}, {"chunk_id": "9fe81132", "type": "enum_specifier", "language": "c", "complexity_score": "high", "semantic_tags": ["network_operations", "data_structures", "security_related"], "quality_score": "poor", "file_path": "tmwsesn.h", "line_range": "40-120"}, {"chunk_id": "72cf137c", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsesn.h", "line_range": "125-131"}, {"chunk_id": "a7bebd47", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsesn.h", "line_range": "133-139"}, {"chunk_id": "03a1aca1", "type": "struct_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsesn.h", "line_range": "178-226"}, {"chunk_id": "f45df007", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsesn.h", "line_range": "183-183"}, {"chunk_id": "e714290d", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsesn.h", "line_range": "186-186"}, {"chunk_id": "fd3c3980", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsesn.h", "line_range": "189-189"}, {"chunk_id": "b85e29a6", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsesn.h", "line_range": "220-220"}, {"chunk_id": "838f0fb3", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsesn.h", "line_range": "221-221"}, {"chunk_id": "0fa5a6e7", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsesn.h", "line_range": "222-222"}, {"chunk_id": "41057101", "type": "struct_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsesn.h", "line_range": "228-250"}, {"chunk_id": "74fb87aa", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsesn.h", "line_range": "231-231"}, {"chunk_id": "d71c0028", "type": "struct_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsesn.h", "line_range": "254-291"}, {"chunk_id": "e531aad3", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsesn.h", "line_range": "263-263"}, {"chunk_id": "e4008859", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsesn.h", "line_range": "310-310"}, {"chunk_id": "2a7461a8", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "45-50"}, {"chunk_id": "9abf39c0", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["memory_management"], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "53-57"}, {"chunk_id": "4067c0f7", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "107-121"}, {"chunk_id": "17222183", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "183-187"}, {"chunk_id": "27ccb2d6", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "196-199"}, {"chunk_id": "5bd360bc", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["memory_management"], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "202-219"}, {"chunk_id": "60ff9039", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "222-227"}, {"chunk_id": "0585d210", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "230-234"}, {"chunk_id": "4ff227f0", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["testing", "memory_management"], "quality_score": "poor", "file_path": "tmwsim.c", "line_range": "237-352"}, {"chunk_id": "6db6ebbc", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "355-359"}, {"chunk_id": "696a62df", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "362-366"}, {"chunk_id": "643cf7e4", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "369-374"}, {"chunk_id": "f0ff3663", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "377-381"}, {"chunk_id": "7096010c", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "384-389"}, {"chunk_id": "28f9ad1c", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "392-396"}, {"chunk_id": "6508ee11", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "399-404"}, {"chunk_id": "1ace159a", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["testing"], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "407-411"}, {"chunk_id": "a6056ac6", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["testing"], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "414-419"}, {"chunk_id": "47e93df4", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "422-426"}, {"chunk_id": "c6470518", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "429-434"}, {"chunk_id": "0485bd03", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "437-442"}, {"chunk_id": "d1ca0c05", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "445-450"}, {"chunk_id": "cdf07f50", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "453-457"}, {"chunk_id": "e48cd260", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "460-470"}, {"chunk_id": "05494b85", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "473-477"}, {"chunk_id": "5920f837", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "480-485"}, {"chunk_id": "65a94098", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "488-492"}, {"chunk_id": "7cb05cc2", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "495-500"}, {"chunk_id": "606312ac", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "503-507"}, {"chunk_id": "31b2d009", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "510-515"}, {"chunk_id": "5996df53", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "518-522"}, {"chunk_id": "3df0d9a1", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "525-530"}, {"chunk_id": "64a62ca5", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "533-537"}, {"chunk_id": "6867c5b5", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "540-545"}, {"chunk_id": "d63515e3", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "548-552"}, {"chunk_id": "cf4aac9a", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "555-560"}, {"chunk_id": "86bf8530", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "563-575"}, {"chunk_id": "4e50b7fa", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["string_operations", "memory_management"], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "578-587"}, {"chunk_id": "0edf1517", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "597-603"}, {"chunk_id": "cab0ec85", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "606-610"}, {"chunk_id": "c0a784c6", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "613-632"}, {"chunk_id": "9b3316a9", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "635-641"}, {"chunk_id": "b8be3514", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "644-648"}, {"chunk_id": "3fbc1c57", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "651-674"}, {"chunk_id": "4285e28e", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "677-683"}, {"chunk_id": "13b35b76", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "686-692"}, {"chunk_id": "dfd99b63", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "695-701"}, {"chunk_id": "66e4bebe", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "704-708"}, {"chunk_id": "ccc1a279", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "711-735"}, {"chunk_id": "404d200a", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "738-744"}, {"chunk_id": "96f21ce7", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "747-751"}, {"chunk_id": "3f8917a4", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "754-758"}, {"chunk_id": "ff08bb06", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "761-785"}, {"chunk_id": "f4af0ec0", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "788-792"}, {"chunk_id": "dca862f9", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "795-800"}, {"chunk_id": "f95cbef4", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "803-807"}, {"chunk_id": "2bbbf90a", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "810-815"}, {"chunk_id": "7c5e59a1", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["memory_management"], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "818-825"}, {"chunk_id": "544bf3cd", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "828-842"}, {"chunk_id": "592858ad", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "good", "file_path": "tmwsim.c", "line_range": "845-849"}, {"chunk_id": "3ee5ec2e", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "good", "file_path": "tmwsim.c", "line_range": "852-856"}, {"chunk_id": "02e427d6", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "good", "file_path": "tmwsim.c", "line_range": "859-893"}, {"chunk_id": "b872d229", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "good", "file_path": "tmwsim.c", "line_range": "896-900"}, {"chunk_id": "7f46cc2d", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "good", "file_path": "tmwsim.c", "line_range": "903-908"}, {"chunk_id": "aa413cf5", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "911-917"}, {"chunk_id": "d4d33870", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "919-923"}, {"chunk_id": "4e39316d", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "925-944"}, {"chunk_id": "4f05c8c3", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "946-950"}, {"chunk_id": "8af89204", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "952-958"}, {"chunk_id": "1f10ecc7", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "961-969"}, {"chunk_id": "8fc724f6", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "972-980"}, {"chunk_id": "d262d626", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "984-994"}, {"chunk_id": "7866c3f9", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "997-1002"}, {"chunk_id": "f1ca9270", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "1005-1009"}, {"chunk_id": "a20c5e05", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "1012-1016"}, {"chunk_id": "be30f945", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "1019-1031"}, {"chunk_id": "d203ddc6", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "1034-1046"}, {"chunk_id": "6ac8d654", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "1049-1061"}, {"chunk_id": "763d9c59", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "1064-1080"}, {"chunk_id": "cb12b5fb", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["string_operations"], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "1083-1094"}, {"chunk_id": "196928a4", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["memory_management"], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "1096-1110"}, {"chunk_id": "17a57835", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["memory_management"], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "1112-1140"}, {"chunk_id": "e7503ee8", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["string_operations"], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "1144-1155"}, {"chunk_id": "1c7f4034", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["memory_management"], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "1157-1171"}, {"chunk_id": "0bb6b34c", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["memory_management"], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "1181-1209"}, {"chunk_id": "e4618ad1", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "1213-1220"}, {"chunk_id": "68674457", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["string_operations", "memory_management"], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "1222-1262"}, {"chunk_id": "c68f2360", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["string_operations", "io_operations", "memory_management"], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "1264-1293"}, {"chunk_id": "1e5dbc32", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": ["io_operations"], "quality_score": "fair", "file_path": "tmwsim.c", "line_range": "1295-1306"}, {"chunk_id": "530fa881", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["string_operations", "io_operations"], "quality_score": "poor", "file_path": "tmwsim.c", "line_range": "1308-1327"}, {"chunk_id": "333d4d24", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsim.h", "line_range": "91-105"}, {"chunk_id": "d9cbb092", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsim.h", "line_range": "114-120"}, {"chunk_id": "09a890cf", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsim.h", "line_range": "122-126"}, {"chunk_id": "754d731e", "type": "struct_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsim.h", "line_range": "128-140"}, {"chunk_id": "653c6f0f", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsim.h", "line_range": "142-144"}, {"chunk_id": "852857ac", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["memory_management", "data_structures"], "quality_score": "fair", "file_path": "tmwsim.h", "line_range": "146-156"}, {"chunk_id": "beea6da3", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsim.h", "line_range": "158-161"}, {"chunk_id": "07f06484", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsim.h", "line_range": "163-166"}, {"chunk_id": "d1493a63", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsim.h", "line_range": "165-165"}, {"chunk_id": "198776f4", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsim.h", "line_range": "168-170"}, {"chunk_id": "28b832fe", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsim.h", "line_range": "169-169"}, {"chunk_id": "317c83a1", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsim.h", "line_range": "172-180"}, {"chunk_id": "bda02a4b", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsim.h", "line_range": "182-184"}, {"chunk_id": "9f68743f", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsim.h", "line_range": "186-190"}, {"chunk_id": "dd4ba105", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsim.h", "line_range": "192-196"}, {"chunk_id": "071c917a", "type": "struct_specifier", "language": "c", "complexity_score": "high", "semantic_tags": ["testing", "memory_management", "data_structures"], "quality_score": "good", "file_path": "tmwsim.h", "line_range": "202-248"}, {"chunk_id": "c89bab54", "type": "union_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "good", "file_path": "tmwsim.h", "line_range": "234-247"}, {"chunk_id": "6dc6bb57", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwsim.h", "line_range": "251-256"}, {"chunk_id": "79ed3215", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtarg.h", "line_range": "113-116"}, {"chunk_id": "0b2092be", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtarg.h", "line_range": "134-139"}, {"chunk_id": "1a0cc9cb", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtarg.h", "line_range": "142-147"}, {"chunk_id": "09551be2", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtarg.h", "line_range": "150-160"}, {"chunk_id": "f981c394", "type": "enum_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtarg.h", "line_range": "163-179"}, {"chunk_id": "3ffb28f6", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtarg.h", "line_range": "202-208"}, {"chunk_id": "9fd0e1ae", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["network_operations", "data_structures"], "quality_score": "fair", "file_path": "tmwtarg.h", "line_range": "216-234"}, {"chunk_id": "48a52f37", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtarg.h", "line_range": "237-242"}, {"chunk_id": "d13d283e", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtarg.h", "line_range": "244-247"}, {"chunk_id": "a1c3fe1c", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtarg.h", "line_range": "249-252"}, {"chunk_id": "2e9f2b20", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtarg.h", "line_range": "256-261"}, {"chunk_id": "b435dd35", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtarg.h", "line_range": "266-279"}, {"chunk_id": "1c343d78", "type": "struct_specifier", "language": "c", "complexity_score": "high", "semantic_tags": ["testing", "data_structures", "system_calls", "network_operations", "security_related", "io_operations"], "quality_score": "poor", "file_path": "tmwtarg.h", "line_range": "282-462"}, {"chunk_id": "b87a2e6b", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtarg.h", "line_range": "530-530"}, {"chunk_id": "0ab95852", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtarg.h", "line_range": "531-531"}, {"chunk_id": "551de8b5", "type": "struct_specifier", "language": "c", "complexity_score": "high", "semantic_tags": ["network_operations", "io_operations", "data_structures", "system_calls"], "quality_score": "poor", "file_path": "tmwtarg.h", "line_range": "589-646"}, {"chunk_id": "7f1cb6a5", "type": "struct_specifier", "language": "c", "complexity_score": "high", "semantic_tags": ["network_operations", "data_structures"], "quality_score": "poor", "file_path": "tmwtarg.h", "line_range": "653-748"}, {"chunk_id": "ee1ec7a2", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["io_operations", "data_structures", "error_handling"], "quality_score": "fair", "file_path": "tmwtarg.h", "line_range": "750-756"}, {"chunk_id": "8548d7dc", "type": "struct_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["network_operations", "io_operations", "data_structures"], "quality_score": "fair", "file_path": "tmwtarg.h", "line_range": "812-846"}, {"chunk_id": "274f6cf6", "type": "header", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "good", "file_path": "tmwtimer.c", "line_range": "0-0"}, {"chunk_id": "2641dd16", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwtimer.c", "line_range": "96-115"}, {"chunk_id": "9def426c", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwtimer.c", "line_range": "118-136"}, {"chunk_id": "b77fcafb", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwtimer.c", "line_range": "142-150"}, {"chunk_id": "2d7d005b", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwtimer.c", "line_range": "153-157"}, {"chunk_id": "058bdedd", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwtimer.c", "line_range": "166-184"}, {"chunk_id": "5d6d7cb0", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwtimer.c", "line_range": "186-189"}, {"chunk_id": "058bdedd", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwtimer.c", "line_range": "191-194"}, {"chunk_id": "058bdedd", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwtimer.c", "line_range": "196-199"}, {"chunk_id": "a77a3f7c", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["io_operations", "data_structures"], "quality_score": "poor", "file_path": "tmwtimer.c", "line_range": "201-269"}, {"chunk_id": "058bdedd", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwtimer.c", "line_range": "279-309"}, {"chunk_id": "299ebdb7", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["data_structures"], "quality_score": "poor", "file_path": "tmwtimer.c", "line_range": "312-410"}, {"chunk_id": "843546e1", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwtimer.c", "line_range": "413-435"}, {"chunk_id": "5d6d7cb0", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwtimer.c", "line_range": "446-449"}, {"chunk_id": "a77a3f7c", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwtimer.c", "line_range": "460-493"}, {"chunk_id": "1f65fa36", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": ["io_operations"], "quality_score": "good", "file_path": "tmwtimer.c", "line_range": "503-527"}, {"chunk_id": "d817fb40", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "good", "file_path": "tmwtimer.c", "line_range": "530-541"}, {"chunk_id": "299ebdb7", "type": "function", "language": "c", "complexity_score": "very_high", "semantic_tags": ["data_structures"], "quality_score": "poor", "file_path": "tmwtimer.c", "line_range": "544-634"}, {"chunk_id": "843546e1", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwtimer.c", "line_range": "637-653"}, {"chunk_id": "6af11656", "type": "struct_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["io_operations", "data_structures"], "quality_score": "fair", "file_path": "tmwtimer.c", "line_range": "37-54"}, {"chunk_id": "0201ce28", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtimer.c", "line_range": "315-315"}, {"chunk_id": "ed1e7580", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtimer.c", "line_range": "531-531"}, {"chunk_id": "da746821", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtimer.c", "line_range": "547-547"}, {"chunk_id": "8aae3310", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtimer.h", "line_range": "45-45"}, {"chunk_id": "1e8d61f4", "type": "struct_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["io_operations", "data_structures"], "quality_score": "fair", "file_path": "tmwtimer.h", "line_range": "48-69"}, {"chunk_id": "0f3dfd07", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtimer.h", "line_range": "60-60"}, {"chunk_id": "2c6a0c26", "type": "struct_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtimer.h", "line_range": "81-91"}, {"chunk_id": "4031bf15", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtimer.h", "line_range": "142-142"}, {"chunk_id": "8c09d9ac", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtimer.h", "line_range": "191-191"}, {"chunk_id": "9e4736aa", "type": "function", "language": "c", "complexity_score": "high", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwtprt.c", "line_range": "36-61"}, {"chunk_id": "c73a94ad", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwtprt.c", "line_range": "64-76"}, {"chunk_id": "9321ae17", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "good", "file_path": "tmwtprt.c", "line_range": "79-90"}, {"chunk_id": "2c0dccb2", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwtprt.c", "line_range": "93-98"}, {"chunk_id": "02c66e19", "type": "function", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "tmwtprt.c", "line_range": "101-106"}, {"chunk_id": "0c69ecaa", "type": "struct_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["testing", "data_structures"], "quality_score": "good", "file_path": "tmwtprt.h", "line_range": "67-85"}, {"chunk_id": "80f4713a", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtprt.h", "line_range": "69-69"}, {"chunk_id": "d3ae8f0f", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["io_operations", "data_structures"], "quality_score": "fair", "file_path": "tmwtprt.h", "line_range": "128-137"}, {"chunk_id": "b89db7d9", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtprt.h", "line_range": "150-150"}, {"chunk_id": "3014a66e", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtprt.h", "line_range": "166-166"}, {"chunk_id": "1c41ba3a", "type": "function", "language": "cpp", "complexity_score": "high", "semantic_tags": ["testing", "memory_management"], "quality_score": "fair", "file_path": "TMWTree.cpp", "line_range": "61-103"}, {"chunk_id": "1c41ba3a", "type": "function", "language": "cpp", "complexity_score": "high", "semantic_tags": ["testing", "memory_management"], "quality_score": "good", "file_path": "TMWTree.cpp", "line_range": "122-163"}, {"chunk_id": "1c41ba3a", "type": "function", "language": "cpp", "complexity_score": "high", "semantic_tags": [], "quality_score": "fair", "file_path": "TMWTree.cpp", "line_range": "207-248"}, {"chunk_id": "1c41ba3a", "type": "function", "language": "cpp", "complexity_score": "low", "semantic_tags": [], "quality_score": "good", "file_path": "TMWTree.cpp", "line_range": "266-276"}, {"chunk_id": "1c41ba3a", "type": "function", "language": "cpp", "complexity_score": "very_high", "semantic_tags": ["testing", "memory_management"], "quality_score": "poor", "file_path": "TMWTree.cpp", "line_range": "293-368"}, {"chunk_id": "1c41ba3a", "type": "function", "language": "cpp", "complexity_score": "high", "semantic_tags": ["testing"], "quality_score": "good", "file_path": "TMWTree.cpp", "line_range": "385-421"}, {"chunk_id": "1c41ba3a", "type": "function", "language": "cpp", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "TMWTree.cpp", "line_range": "611-617"}, {"chunk_id": "112d7e4e", "type": "function", "language": "cpp", "complexity_score": "low", "semantic_tags": [], "quality_score": "fair", "file_path": "TMWTree.cpp", "line_range": "621-624"}, {"chunk_id": "1c41ba3a", "type": "function", "language": "cpp", "complexity_score": "very_high", "semantic_tags": ["testing", "memory_management"], "quality_score": "poor", "file_path": "TMWTree.cpp", "line_range": "659-729"}, {"chunk_id": "c4c289d0", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "good", "file_path": "TMWTree.h", "line_range": "39-46"}, {"chunk_id": "20568dbc", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "TMWTree.h", "line_range": "43-43"}, {"chunk_id": "fa622497", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "TMWTree.h", "line_range": "44-44"}, {"chunk_id": "26400081", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "TMWTree.h", "line_range": "45-45"}, {"chunk_id": "71154aff", "type": "struct_specifier", "language": "c", "complexity_score": "medium", "semantic_tags": ["data_structures"], "quality_score": "good", "file_path": "TMWTree.h", "line_range": "55-78"}, {"chunk_id": "f16ffd7c", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtypes.h", "line_range": "104-114"}, {"chunk_id": "c8f14dde", "type": "enum_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtypes.h", "line_range": "118-127"}, {"chunk_id": "45f61143", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtypes.h", "line_range": "141-145"}, {"chunk_id": "5ff0f7ff", "type": "enum_specifier", "language": "c", "complexity_score": "high", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtypes.h", "line_range": "155-177"}, {"chunk_id": "71ceebe6", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtypes.h", "line_range": "185-188"}, {"chunk_id": "7a42da6a", "type": "struct_specifier", "language": "c", "complexity_score": "low", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtypes.h", "line_range": "196-199"}, {"chunk_id": "00dbd1b0", "type": "struct_specifier", "language": "c", "complexity_score": "high", "semantic_tags": ["data_structures"], "quality_score": "good", "file_path": "tmwtypes.h", "line_range": "201-236"}, {"chunk_id": "089dca0c", "type": "union_specifier", "language": "c", "complexity_score": "high", "semantic_tags": ["data_structures"], "quality_score": "fair", "file_path": "tmwtypes.h", "line_range": "203-225"}, {"chunk_id": "1a6e5a1d", "type": "header", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "good", "file_path": "tmwvrsn.c", "line_range": "0-0"}, {"chunk_id": "2befd0e2", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "good", "file_path": "tmwvrsn.c", "line_range": "29-39"}, {"chunk_id": "baa5fe67", "type": "function", "language": "c", "complexity_score": "medium", "semantic_tags": [], "quality_score": "poor", "file_path": "tmwvrsn.c", "line_range": "41-55"}, {"chunk_id": "25ab9522", "type": "header", "language": "csharp", "complexity_score": "low", "semantic_tags": ["string_operations", "oop_concepts", "memory_management"], "quality_score": "good", "file_path": "tmwvrsn.cs", "line_range": "0-0"}, {"chunk_id": "2e50f489", "type": "class", "language": "csharp", "complexity_score": "low", "semantic_tags": ["string_operations", "oop_concepts"], "quality_score": "good", "file_path": "tmwvrsn.cs", "line_range": "6-22"}, {"chunk_id": "3900af1d", "type": "namespace", "language": "csharp", "complexity_score": "medium", "semantic_tags": ["string_operations", "oop_concepts"], "quality_score": "good", "file_path": "tmwvrsn.cs", "line_range": "3-23"}, {"chunk_id": "97b40711", "type": "using_directive", "language": "csharp", "complexity_score": "low", "semantic_tags": ["memory_management"], "quality_score": "fair", "file_path": "tmwvrsn.cs", "line_range": "1-1"}, {"chunk_id": "8c798094", "type": "header", "language": "c", "complexity_score": "low", "semantic_tags": [], "quality_score": "good", "file_path": "tmwvrsn.h", "line_range": "0-0"}]}