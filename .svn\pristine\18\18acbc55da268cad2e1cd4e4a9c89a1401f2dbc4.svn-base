# RAG Server Testing Guide for home-ai-server

## Overview

The RAG system now runs as a service on `home-ai-server:5002`. This guide covers testing the system via API calls rather than direct database access.

## Quick Start

```bash
# Test server connectivity and basic functionality
python test_debug_system.py

# List all available collections
python test_collections.py --list-collections --server-url http://home-ai-server:5002

# Test a specific collection
python test_collections.py --collection my_codebase --server-url http://home-ai-server:5002

# Run comprehensive tests
python test_collections.py --all-collections --ai-test --benchmark --server-url http://home-ai-server:5002
```

## Configuration

The testing scripts use these environment variables:

```bash
export RAG_SERVER_HOST=home-ai-server
export RAG_SERVER_PORT=5002
# Or override with --server-url flag
```

## Updated Test Scripts

### 1. `test_debug_system.py` 
**Purpose**: Basic connectivity and functionality testing
**Usage**: 
```bash
python test_debug_system.py
```
**Tests**:
- Server connection to home-ai-server:5002
- Health endpoint validation
- Codebase discovery
- Basic search functionality
- AI question answering

### 2. `test_collections.py`
**Purpose**: Comprehensive collection testing via API
**Usage**:
```bash
# List collections
python test_collections.py --list-collections

# Test specific collection
python test_collections.py --collection utils

# Test with custom queries
python test_collections.py --collection utils --custom-queries "memory management" "error handling"

# AI testing
python test_collections.py --collection utils --ai-test --questions "What functions handle encryption?"

# Performance testing
python test_collections.py --collection utils --benchmark

# Full test suite
python test_collections.py --all-collections --ai-test --benchmark --output test_report.json
```

## API Endpoints Tested

### Health Check
- **Endpoint**: `GET /health`
- **Tests**: Server status, embedding configuration, collection counts

### Codebase Management
- **Endpoint**: `POST /tools/list_codebases`
- **Tests**: Available codebases, their status and document counts

- **Endpoint**: `POST /tools/select_codebase`
- **Tests**: Codebase selection for subsequent operations

- **Endpoint**: `POST /tools/get_code_stats`
- **Tests**: Collection statistics and metadata

### Search and Analysis
- **Endpoint**: `POST /tools/search_code`
- **Tests**: Code search functionality, result quality, performance

- **Endpoint**: `POST /tools/ask_about_code`
- **Tests**: AI-powered code analysis and question answering

## Test Categories

### 1. Connectivity Tests
- Server availability
- Network connectivity to home-ai-server
- API endpoint responsiveness

### 2. Functional Tests
- Codebase discovery and listing
- Search functionality across languages
- AI question answering quality
- Collection selection and management

### 3. Performance Tests
- Search response times
- Query throughput
- Large result set handling

### 4. Quality Tests
- Search result relevance
- AI response accuracy
- Cross-language code understanding

## Expected Output Examples

### Successful Connection Test
```
🔗 Testing connection to http://home-ai-server:5002
✅ RAG server is responding
   Version: 2.1.0
   Supported languages: ['C', 'C++', 'Python', 'C#']
   Current codebase: utils
```

### Collection Listing
```
📚 Available Collections (2):
   1. utils (ready)
      📊 Documents: 1,247
   2. project1 (needs_indexing)
      📊 Documents: 0
```

### Search Test Results
```
🔍 Query 1/5: 'function definition'
   ✅ Found 3 results
      [CPP] function `initialize_crypto()`
         📁 crypto/crypto_utils.cpp
         🎯 Relevance: 0.847
```

## Troubleshooting

### Connection Issues
```bash
# Check if container is running
docker ps | grep rag-server

# Check container logs
docker logs openwebui-rag-server

# Test direct connection
curl http://home-ai-server:5002/health

# Test from within network
ping home-ai-server
telnet home-ai-server 5002
```

### No Codebases Found
- Check if source code is properly mounted in container
- Verify source_code directory structure
- Check container environment variables

### Search Not Working
- Verify codebase is selected before searching
- Check if collections are properly created
- Test with simpler, broader queries

### AI Responses Poor
- Ensure Ollama service is running
- Check if embedding model is available
- Verify sufficient context is being retrieved

## Integration Examples

### CI/CD Pipeline
```bash
#!/bin/bash
# test_rag_system.sh

# Test basic functionality
python test_debug_system.py
if [ $? -ne 0 ]; then
    echo "Basic tests failed"
    exit 1
fi

# Test all collections
python test_collections.py --all-collections --quick --output ci_report.json
if [ $? -ne 0 ]; then
    echo "Collection tests failed"
    exit 1
fi

echo "All RAG system tests passed"
```

### Monitoring Script
```bash
#!/bin/bash
# monitor_rag_health.sh

# Check health every 5 minutes
while true; do
    curl -s http://home-ai-server:5002/health | jq '.overall_status'
    sleep 300
done
```

## Files Removed/Obsolete

The following files are no longer needed with the new architecture:

- `debug_directory_structure.py` - Directory checking now done via API
- `test_tree_sitter.py` - Tree-sitter testing is server-side only
- `test_requirements.py` - Requirements testing is server-side only
- `test_docker_debug.py` - Docker debugging now via container logs
- Most markdown documentation files - Consolidated into this guide

## Migration from Old Testing

If you have old test scripts:

**Old approach (direct ChromaDB access)**:
```python
client = chromadb.PersistentClient(path="./chroma_db")
collection = client.get_collection("my_collection")
results = collection.query(query_texts=["search term"])
```

**New approach (API-based testing)**:
```python
import requests
response = requests.post("http://home-ai-server:5002/tools/search_code", 
                        json={"query": "search term", "codebase_name": "my_collection"})
```

## Best Practices

1. **Always test connectivity first** with `test_debug_system.py`
2. **Use environment variables** for server configuration
3. **Run quick tests frequently**, comprehensive tests periodically
4. **Export test results** for historical analysis
5. **Monitor server health** as part of regular operations
6. **Test with realistic queries** from your actual use cases