#!/usr/bin/env python3
"""
Test the enhanced memory management search.
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path to import the tool
sys.path.append(str(Path(__file__).parent))

from open_webui_code_analyzer_tool import Tools

class MockEventEmitter:
    """Mock event emitter to capture status messages"""
    def __init__(self):
        self.events = []
    
    async def __call__(self, event):
        self.events.append(event)
        print(f"📡 Event: {event}")

async def test_enhanced_memory_search():
    """Test the enhanced memory management search"""
    print("🔍 Testing Enhanced Memory Management Search")
    print("=" * 60)
    
    # Initialize the tool
    tool = Tools()
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    
    # Clear cache to ensure fresh results
    print("1. Clearing cache...")
    if tool.cache:
        await tool.clear_cache()
        print("   ✅ Cache cleared")
    
    # Ensure utils is selected
    print("\n2. Selecting utils codebase...")
    await tool.select_codebase("utils")
    
    # Create mock event emitter
    event_emitter = MockEventEmitter()
    
    # Test the enhanced memory management query
    print("\n3. Testing enhanced memory management query...")
    try:
        # Check what search parameters will be used
        search_params = tool._analyze_query_for_context("how is memory managed in the utils codebase?")
        print(f"   Search params: {search_params}")
        
        result = await tool.get_code_context(
            "how is memory managed in the utils codebase?",
            codebase_name="utils",
            __event_emitter__=event_emitter
        )
        
        print(f"   Result length: {len(result)} characters")
        print(f"   Events captured: {len(event_emitter.events)}")
        
        # Check the final event for chunk count
        if event_emitter.events:
            final_event = event_emitter.events[-1]
            description = final_event.get('data', {}).get('description', '')
            print(f"   Final status: {description}")
        
        # Manual verification
        import re
        found_match = re.search(r'Found (\d+) relevant code section', result)
        context_matches = re.findall(r'## Context \d+', result)
        
        print(f"   'Found X relevant': {found_match.group(1) if found_match else 'None'}")
        print(f"   '## Context X' matches: {len(context_matches)}")
        
        if found_match and int(found_match.group(1)) > 1:
            print(f"   🎉 SUCCESS: Enhanced search found {found_match.group(1)} chunks!")
        elif len(context_matches) > 1:
            print(f"   🎉 SUCCESS: Found {len(context_matches)} context sections!")
        else:
            print(f"   ⚠️ Still only finding limited results")
            print(f"   First 300 chars: {result[:300]}...")
        
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 Enhanced memory search test completed!")

if __name__ == "__main__":
    try:
        asyncio.run(test_enhanced_memory_search())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
