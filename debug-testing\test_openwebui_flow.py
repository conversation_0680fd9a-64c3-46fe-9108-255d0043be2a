#!/usr/bin/env python3
"""
Test the actual OpenWebUI flow to see chunk accumulation
"""

import asyncio
import sys
import os

# Add the current directory to Python path so we can import the tool
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the tool
from open_webui_code_analyzer_tool import Tools

class MockEventEmitter:
    """Mock event emitter for testing"""
    def __init__(self, verbose=True):
        self.verbose = verbose
        self.events = []
    
    async def __call__(self, event):
        """Mock event emission"""
        if self.verbose:
            event_type = event.get("type", "unknown")
            data = event.get("data", {})
            description = data.get("description", "No description")
            done = data.get("done", False)
            status = "✅" if done else "🔄"
            print(f"   {status} Event: {description}")
        
        self.events.append(event)

class OpenWebUIFlowTester:
    """Test harness for the actual OpenWebUI flow"""
    
    def __init__(self):
        self.tool = Tools()
        self.emitter = MockEventEmitter(verbose=True)
        
        # Override server URL to match our working server
        self.tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
        self.tool.valves.current_codebase = "utils"  # Pre-select utils
        
        print(f"🔧 Tool initialized with server: {self.tool.valves.code_analyzer_server_url}")
        print(f"🔧 Current codebase: '{self.tool.valves.current_codebase}'")
    
    async def test_openwebui_flow(self):
        """Test the actual OpenWebUI flow that users experience"""
        print("\n" + "="*60)
        print("🧪 TESTING ACTUAL OPENWEBUI FLOW")
        print("="*60)
        
        # Test queries that should trigger multiple context retrievals
        test_queries = [
            "tell me about memory management in the utils codebase",
            "show me memory allocation functions",
            "explain how memory is handled in this codebase",
            "find memory management code"
        ]
        
        for query in test_queries:
            print(f"\n🔧 Testing OpenWebUI flow for: '{query}'")
            print("-" * 50)
            
            # Reset event tracking
            self.emitter.events = []
            
            try:
                # Call the main entry point that OpenWebUI uses
                result = await self.tool.__call__(
                    user_query=query,
                    __event_emitter__=self.emitter
                )
                
                # Analyze the events
                status_events = [e for e in self.emitter.events if e.get("type") == "status"]
                final_events = [e for e in status_events if e.get("data", {}).get("done", False)]
                
                print(f"\n📊 Results:")
                print(f"   Total events: {len(self.emitter.events)}")
                print(f"   Status events: {len(status_events)}")
                print(f"   Final events: {len(final_events)}")
                
                if final_events:
                    final_event = final_events[-1]
                    final_description = final_event.get("data", {}).get("description", "")
                    print(f"   Final status: {final_description}")
                
                if result:
                    print(f"   Result length: {len(result)} chars")
                    print(f"   Result preview: {result[:150]}...")
                else:
                    print(f"   No result returned")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
    
    async def test_direct_auto_inject(self):
        """Test the auto-inject function directly"""
        print("\n" + "="*60)
        print("🧪 TESTING DIRECT AUTO-INJECT")
        print("="*60)
        
        query = "tell me about memory management in the utils codebase"
        print(f"\n🔧 Testing direct auto-inject for: '{query}'")
        
        # Reset event tracking
        self.emitter.events = []
        
        try:
            # Call the auto-inject function directly
            result = await self.tool._auto_inject_optimized_context(
                user_query=query,
                __event_emitter__=self.emitter
            )
            
            # Analyze the events
            status_events = [e for e in self.emitter.events if e.get("type") == "status"]
            final_events = [e for e in status_events if e.get("data", {}).get("done", False)]
            
            print(f"\n📊 Results:")
            print(f"   Total events: {len(self.emitter.events)}")
            print(f"   Status events: {len(status_events)}")
            print(f"   Final events: {len(final_events)}")
            
            if final_events:
                final_event = final_events[-1]
                final_description = final_event.get("data", {}).get("description", "")
                print(f"   Final status: {final_description}")
            
            if result:
                print(f"   Result length: {len(result)} chars")
                print(f"   Result preview: {result[:150]}...")
            else:
                print(f"   No result returned")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

async def main():
    """Main test execution"""
    tester = OpenWebUIFlowTester()
    await tester.test_openwebui_flow()
    await tester.test_direct_auto_inject()

if __name__ == "__main__":
    asyncio.run(main())
