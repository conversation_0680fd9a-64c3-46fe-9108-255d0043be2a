#!/usr/bin/env python3
"""
Test script to verify the new language framework deployment
"""

import sys
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_framework_imports():
    """Test that all framework components can be imported"""
    try:
        from language_framework import CodeAnalysisFramework, LanguageProcessor
        from language_registry import create_language_registry, validate_language_coverage
        from language_processors import CCppProcessor, PythonProcessor, CSharpProcessor, JavaScriptProcessor
        from framework_bridge import FrameworkBridge
        from framework_integration import IntegratedCodeAnalysisSystem
        logger.info("✅ All framework imports successful")
        return True
    except Exception as e:
        logger.error(f"❌ Framework import failed: {e}")
        return False

def test_language_registry():
    """Test language registry creation and validation"""
    try:
        framework = create_language_registry()
        languages = framework.get_supported_languages()
        extensions = framework.get_supported_extensions()
        
        logger.info(f"✅ Framework created with {len(languages)} languages")
        logger.info(f"✅ Supporting {len(extensions)} file extensions")
        
        # Test validation
        validation = validate_language_coverage()
        logger.info(f"✅ Language coverage validation: {validation['coverage_complete']}")
        
        return True
    except Exception as e:
        logger.error(f"❌ Language registry test failed: {e}")
        return False

def test_framework_bridge():
    """Test framework bridge functionality"""
    try:
        # Use current directory as test path
        bridge = FrameworkBridge(".", use_new_framework=True)
        logger.info("✅ Framework bridge created successfully")
        
        # Test that it has both old and new systems
        has_old = bridge.old_preprocessor is not None
        has_new = bridge.new_system is not None
        
        logger.info(f"✅ Old system available: {has_old}")
        logger.info(f"✅ New system available: {has_new}")
        
        return True
    except Exception as e:
        logger.error(f"❌ Framework bridge test failed: {e}")
        return False

def test_main_integration():
    """Test integration with main.py functions"""
    try:
        # Import the updated main.py functions
        from main import get_supported_languages
        
        languages = get_supported_languages()
        logger.info(f"✅ Main.py integration: {len(languages)} languages supported")
        logger.info(f"✅ Sample languages: {languages[:5]}")
        
        return True
    except Exception as e:
        logger.error(f"❌ Main.py integration test failed: {e}")
        return False

def main():
    """Run all deployment tests"""
    logger.info("🚀 Starting framework deployment tests...")
    
    tests = [
        ("Framework Imports", test_framework_imports),
        ("Language Registry", test_language_registry),
        ("Framework Bridge", test_framework_bridge),
        ("Main Integration", test_main_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n📊 Test Results Summary:")
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        logger.info("🎉 All tests passed! Framework deployment ready.")
        return 0
    else:
        logger.error("💥 Some tests failed. Check logs above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
