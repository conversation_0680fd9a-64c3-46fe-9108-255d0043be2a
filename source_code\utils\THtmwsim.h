/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2023 */
/*****************************************************************************/
/*                                                                           */
/* This file is the property of:                                             */
/*                                                                           */
/*                       Triangle MicroWorks, Inc.                           */
/*                      Raleigh, North Carolina USA                          */
/*                       www.TriangleMicroWorks.com                          */
/*                          (919) 870-6615                                   */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*                                                                           */
/*****************************************************************************/

/* file: THtmwsim.h
 * description: This file simulates generic data points that can be used
 *  for both master and Outstation/ControlledStation/Server simulations.
 */
#ifndef THTMWSIM_DEFINED
#define THTMWSIM_DEFINED

#include "tmwscl/utils/tmwdefs.h"
#include "tmwscl/utils/tmwcnfg.h"
#include "tmwscl/utils/tmwdlist.h"
#include "tmwscl/utils/tmwdtime.h"
#include "tmwscl/utils/TMWTree.h"

#if TMWCNFG_USE_SIMULATED_DB && TMW_USE_BINARY_TREE
/* Use a balanced binary tree implementation */
#define TMWSIM_TABLE_HEAD  TMWTREE_TREE *
#define TMWSIM_TABLE       TMWTREE_TREE
#endif

#endif /* THTMWSIM_DEFINED */
