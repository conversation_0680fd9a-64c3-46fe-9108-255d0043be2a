/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2023 */
/*****************************************************************************/
/*                                                                           */
/* This file is the property of:                                             */
/*                                                                           */
/*                       Triangle MicroWorks, Inc.                           */
/*                      Raleigh, North Carolina USA                          */
/*                       www.TriangleMicroWorks.com                          */
/*                          (919) 870-6615                                   */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*                                                                           */
/*****************************************************************************/

/* file: smbsim.c
 * description: Simulates a MB Server database.
 */
#include "tmwscl/modbus/mbdiag.h"
#include "tmwscl/utils/tmwcnfg.h"
#if TMWCNFG_USE_SIMULATED_DB

#include "tmwscl/modbus/smbsim.h"
#include "tmwscl/modbus/smbmem.h"
#include "tmwscl/modbus/smbdata.h"

#include "tmwscl/utils/tmwsim.h"
#include "tmwscl/utils/tmwtarg.h"

/* Call update callback if set */
static void _callCallback(
  SMBSIM_DATABASE *pDbHandle,
  TMWSIM_EVENT_TYPE type,
  TMWTYPES_UCHAR dataType,
  TMWTYPES_USHORT address)
{
  if(pDbHandle->pUpdateCallback != TMWDEFS_NULL)
  {
    pDbHandle->pUpdateCallback(pDbHandle->pUpdateCallbackParam, type, dataType, address);
  }
}

/* Delete all points in the database */
static void TMWDEFS_LOCAL _clearDb(
  void *pHandle)
{
  void *pPoint;
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;

  _callCallback(pDbHandle, TMWSIM_CLEAR_DATABASE, 0, 0);
  
  while((pPoint = smbsim_dInputGetPointByIndex(pDbHandle, 0)) != TMWDEFS_NULL)
  {
    smbsim_deleteDiscreteInput(pDbHandle, (TMWTYPES_USHORT)tmwsim_getPointNumber((TMWSIM_POINT *)pPoint));  
  } 
  tmwsim_tableDestroy(&pDbHandle->discreteInputs);
   
  while((pPoint = smbsim_coilGetPointByIndex(pDbHandle, 0)) != TMWDEFS_NULL)
  {
    smbsim_deleteCoil(pDbHandle, (TMWTYPES_USHORT)tmwsim_getPointNumber((TMWSIM_POINT *)pPoint));  
  } 
  tmwsim_tableDestroy(&pDbHandle->coils);
  
  while((pPoint = smbsim_iRegGetPointByIndex(pDbHandle, 0)) != TMWDEFS_NULL)
  {
    smbsim_deleteInputRegister(pDbHandle, (TMWTYPES_USHORT)tmwsim_getPointNumber((TMWSIM_POINT *)pPoint));  
  } 
  tmwsim_tableDestroy(&pDbHandle->inputRegisters);
  
  while((pPoint = smbsim_hRegGetPointByIndex(pDbHandle, 0)) != TMWDEFS_NULL)
  {
    smbsim_deleteHoldingRegister(pDbHandle, (TMWTYPES_USHORT)tmwsim_getPointNumber((TMWSIM_POINT *)pPoint));  
  } 
  tmwsim_tableDestroy(&pDbHandle->holdingRegisters);

  while((pPoint = smbsim_deviceIdGetPointByIndex(pDbHandle, 0)) != TMWDEFS_NULL)
  {
    smbsim_deleteDeviceId(pDbHandle, (TMWTYPES_UCHAR)tmwsim_getPointNumber((TMWSIM_POINT *)pPoint));  
  } 
  tmwsim_tableDestroy(&pDbHandle->deviceIds);
}

/* Build default database */
void TMWDEFS_GLOBAL _buildDb(void *pHandle)
{
  TMWTYPES_CHAR *pDeviceId;
  TMWTYPES_USHORT regIndex = 0;
  TMWTYPES_BOOL bValue = TMWDEFS_FALSE;
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;

  tmwsim_tableCreate(&pDbHandle->discreteInputs);
  for(regIndex = 0; regIndex < SMBSIM_NUM_DISCRETE_INPUTS; regIndex++)
  {
    smbsim_addDiscreteInput(pDbHandle, regIndex, bValue);
    if (bValue == TMWDEFS_FALSE)
      bValue = TMWDEFS_TRUE;
    else
      bValue = TMWDEFS_FALSE;
  } 

  bValue = TMWDEFS_FALSE;
  tmwsim_tableCreate(&pDbHandle->coils);
  for(regIndex = 0; regIndex < SMBSIM_NUM_COILS; regIndex++)
  {
    smbsim_addCoil(pDbHandle, regIndex, bValue);
    if (bValue == TMWDEFS_FALSE)
      bValue = TMWDEFS_TRUE;
    else
      bValue = TMWDEFS_FALSE;
  }

  tmwsim_tableCreate(&pDbHandle->inputRegisters);
  for(regIndex = 0; regIndex < SMBSIM_NUM_INPUT_REGISTERS; regIndex++)
    smbsim_addInputRegister(pDbHandle, regIndex, regIndex);

  tmwsim_tableCreate(&pDbHandle->holdingRegisters);
  for(regIndex = 0; regIndex < SMBSIM_NUM_HOLDING_REGISTERS; regIndex++)
  {
    smbsim_addHoldingRegister(pDbHandle, regIndex, regIndex);
  }

  tmwsim_tableCreate(&pDbHandle->deviceIds);
  pDeviceId = "VendorName";
  smbsim_addDeviceId(pDbHandle, 0, (TMWTYPES_UCHAR)strlen(pDeviceId), (TMWTYPES_UCHAR*)pDeviceId);
  pDeviceId = "ProductCode";
  smbsim_addDeviceId(pDbHandle, 1, (TMWTYPES_UCHAR)strlen(pDeviceId), (TMWTYPES_UCHAR*)pDeviceId);
  pDeviceId = "MajorMinorRevision";
  smbsim_addDeviceId(pDbHandle, 2, (TMWTYPES_UCHAR)strlen(pDeviceId), (TMWTYPES_UCHAR*)pDeviceId);
  pDeviceId = "VendorUrl";
  smbsim_addDeviceId(pDbHandle, 3, (TMWTYPES_UCHAR)strlen(pDeviceId), (TMWTYPES_UCHAR*)pDeviceId);
  pDeviceId = "ProductName";
  smbsim_addDeviceId(pDbHandle, 4, (TMWTYPES_UCHAR)strlen(pDeviceId), (TMWTYPES_UCHAR*)pDeviceId);
  pDeviceId = "ModelName";
  smbsim_addDeviceId(pDbHandle, 5, (TMWTYPES_UCHAR)strlen(pDeviceId), (TMWTYPES_UCHAR*)pDeviceId);
  pDeviceId = "UserApplicationName";
  smbsim_addDeviceId(pDbHandle, 6, (TMWTYPES_UCHAR)strlen(pDeviceId), (TMWTYPES_UCHAR*)pDeviceId);
  
  /* Initialize exception status bits */
  pDbHandle->exceptionStatus = 0x00;
}

/* function: smbsim_init */
void * TMWDEFS_GLOBAL smbsim_init(
  TMWSESN *pSession)
{
  SMBSIM_DATABASE *pDbHandle;
  TMWTARG_UNUSED_PARAM(pSession);

  pDbHandle = (SMBSIM_DATABASE *)smbmem_alloc(SMBMEM_SIM_DATABASE_TYPE);
  if(pDbHandle != TMWDEFS_NULL)
  {
#if TMWCNFG_USE_MANAGED_SCL
    memset(pDbHandle, 0, sizeof(SMBSIM_DATABASE));
    smbsim_clear(pDbHandle);
#else
    pDbHandle->pUpdateCallback = TMWDEFS_NULL;
    pDbHandle->pUpdateCallbackParam = TMWDEFS_NULL;
    _buildDb(pDbHandle);
#endif
  }

  return(pDbHandle);
}

/* function: smbsim_close */
void TMWDEFS_GLOBAL smbsim_close(
  void *pHandle)
{
  _clearDb(pHandle);
  smbmem_free(pHandle);
}

/* function: smbsim_clear */
void TMWDEFS_GLOBAL smbsim_clear(
  void *pHandle)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;

  _clearDb(pHandle);

  /* recreate these so that add individual points can be done */
  tmwsim_tableCreate(&pDbHandle->discreteInputs);
  tmwsim_tableCreate(&pDbHandle->coils);
  tmwsim_tableCreate(&pDbHandle->inputRegisters);
  tmwsim_tableCreate(&pDbHandle->holdingRegisters); 
  tmwsim_tableCreate(&pDbHandle->deviceIds); 
}

/* function: smbsim_reset */
void TMWDEFS_GLOBAL smbsim_reset(
  void *pHandle)
{
  _clearDb(pHandle);
  _buildDb(pHandle);
}

/* Set update callback and parameter */
void smbsim_setCallback(
  void *pHandle,
  SMBSIM_CALLBACK_FUNC pUpdateCallback,
  void *pUpdateCallbackParam)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  pDbHandle->pUpdateCallback = pUpdateCallback;
  pDbHandle->pUpdateCallbackParam = pUpdateCallbackParam;
}

/* function: smbsim_enablePoint */
void TMWDEFS_GLOBAL smbsim_enablePoint(
  void *pPoint,
  TMWTYPES_BOOL enabled)
{
  tmwsim_setEnabled((TMWSIM_POINT *)pPoint, enabled);
}

/* function: smbsim_isPointEnabled */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_isPointEnabled(
  void *pPoint)
{
  return(tmwsim_getEnabled((TMWSIM_POINT *)pPoint));
}

/* function: tmwsim_getPointNumber */
TMWTYPES_USHORT TMWDEFS_GLOBAL smbsim_getPointNumber(
  void *pPoint)
{
  return((TMWTYPES_USHORT)tmwsim_getPointNumber((TMWSIM_POINT *)pPoint));
}



/* DiscreteInputs _____________________________________________________________________*/

/* function: smbsim_addDiscreteInput */
void * TMWDEFS_GLOBAL smbsim_addDiscreteInput(
  void *pHandle,
  TMWTYPES_USHORT pointNum,
  TMWTYPES_BOOL value)
{
  TMWSIM_POINT *pPoint;
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;

  pPoint = tmwsim_tableAdd(&pDbHandle->discreteInputs, pointNum);
  if(pPoint != TMWDEFS_NULL)
  {
    tmwsim_initBinary(pPoint, pHandle, pointNum);
    pPoint->data.binary.value = value; 
    _callCallback(pDbHandle, TMWSIM_POINT_ADD, MBDEFS_OBJ_DISCRETE_INPUT_REGISTER, pointNum);
  }
  return(pPoint);
}

/* function: smbsim_deleteDiscreteInput */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_deleteDiscreteInput(
  void *pHandle,
  TMWTYPES_USHORT pointNumber)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;

  _callCallback(pDbHandle, TMWSIM_POINT_DELETE, MBDEFS_OBJ_DISCRETE_INPUT_REGISTER, pointNumber);

  return(tmwsim_tableDelete(&pDbHandle->discreteInputs, pointNumber));
}

static TMWTYPES_BOOL TMWDEFS_GLOBAL _validateRange(
  TMWSIM_TABLE_HEAD *pTable,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT length)
{
  TMWTYPES_USHORT i;
  TMWSIM_POINT *pPoint;
  TMWTYPES_USHORT pointNum = startAddr;

  for(i = 0; i < length; i++)
  {
    if(i==0)
      pPoint = tmwsim_tableFindPoint(pTable, startAddr);
    else
      pPoint = tmwsim_tableGetNextPoint(pTable, pPoint);

    if((pPoint == TMWDEFS_NULL) || (tmwsim_getPointNumber(pPoint) != pointNum++))
      return(TMWDEFS_FALSE);
  }

  return(TMWDEFS_TRUE);
}

/* function: smbsim_DiscreteInputsValidateRange */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_DiscreteInputsValidateRange(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT length)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return( _validateRange(&pDbHandle->discreteInputs, startAddr, length));
}

/* function: smbsim_DiscreteInputGetPoint */
void * TMWDEFS_GLOBAL smbsim_DiscreteInputGetPoint(
  void *pHandle,
  TMWTYPES_USHORT pointNum)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return(tmwsim_tableFindPoint(&pDbHandle->discreteInputs, pointNum));
}

/* function: smbsim_dInputGetPointByIndex */
void * TMWDEFS_GLOBAL smbsim_dInputGetPointByIndex(
  void *pHandle,
  TMWTYPES_USHORT index)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return(tmwsim_tableFindPointByIndex(&pDbHandle->discreteInputs, index)); 
}

/* function: smbsim_dInputGetLastPoint */
void * TMWDEFS_GLOBAL smbsim_dInputGetLastPoint(
  void *pHandle)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return(tmwsim_tableGetLastPoint(&pDbHandle->discreteInputs));
} 

/* function: smbsim_dInputBlockRead */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_dInputBlockRead(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT length,
  TMWTYPES_UCHAR *pValueArray)
{
  TMWTYPES_USHORT i;
  TMWSIM_POINT *pPoint;
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  TMWTYPES_USHORT pointNum = startAddr;

  for(i = 0; i < length; i++)
  {
    if(i==0)
      pPoint = tmwsim_tableFindPoint(&pDbHandle->discreteInputs, startAddr);
    else
      pPoint = tmwsim_tableGetNextPoint(&pDbHandle->discreteInputs, pPoint);

    if((pPoint == TMWDEFS_NULL) || (tmwsim_getPointNumber(pPoint) != pointNum++))
      return(TMWDEFS_FALSE);

    if (tmwsim_getBinaryValue(pPoint))
      pValueArray[i/8] = pValueArray[i/8] | (0x01 << i%8);
    else
      pValueArray[i/8] = pValueArray[i/8] & ~(0x01 << i%8);
  }

  return(TMWDEFS_TRUE);
}

/* function: smbsim_dInputRead */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_dInputRead(
  void *pPoint,
  TMWTYPES_UCHAR *pValue)
{
  if(pPoint == TMWDEFS_NULL)
    return(TMWDEFS_FALSE);
  
  if (tmwsim_getBinaryValue((TMWSIM_POINT *)pPoint))
    *pValue = TMWDEFS_TRUE;
  else
    *pValue = TMWDEFS_FALSE;

  return(TMWDEFS_TRUE);
}

/* function: smbsim_dInputWrite */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_dInputWrite(
  void *pPoint,
  TMWTYPES_BOOL value)
{
  TMWSIM_POINT *pSimPoint = (TMWSIM_POINT *)pPoint;
  if(pPoint == TMWDEFS_NULL)
    return(TMWDEFS_FALSE);

  tmwsim_setBinaryValue(pSimPoint, value, TMWDEFS_CHANGE_REMOTE_OP);

  _callCallback((SMBSIM_DATABASE *)pSimPoint->pDbHandle, TMWSIM_POINT_UPDATE, 
    MBDEFS_OBJ_DISCRETE_INPUT_REGISTER, (TMWTYPES_USHORT)tmwsim_getPointNumber(pSimPoint));
   
  return(TMWDEFS_TRUE);
}

/* Coils _____________________________________________________________________*/

/* function: smbsim_addCoil */
void * TMWDEFS_GLOBAL smbsim_addCoil(
  void *pHandle,
  TMWTYPES_USHORT pointNum,
  TMWTYPES_BOOL value)
{
  TMWSIM_POINT *pPoint;
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  pPoint = tmwsim_tableAdd(&pDbHandle->coils, pointNum);
  if(pPoint != TMWDEFS_NULL)
  { 
    tmwsim_initBinary(pPoint, pHandle, pointNum);
    pPoint->data.binary.value = value;
    _callCallback(pDbHandle, TMWSIM_POINT_ADD, MBDEFS_OBJ_COIL, pointNum);
  }
  return(pPoint);
}

/* function: smbsim_deleteCoil */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_deleteCoil(
  void *pHandle,
  TMWTYPES_USHORT pointNumber)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;

  _callCallback(pDbHandle, TMWSIM_POINT_DELETE, MBDEFS_OBJ_COIL, pointNumber);

  return(tmwsim_tableDelete(&pDbHandle->coils, pointNumber));
}

/* function: smbsim_CoilsValidateRange */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_CoilsValidateRange(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT length)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return( _validateRange(&pDbHandle->coils, startAddr, length));
}
 
/* function: smbsim_coilGetPoint */
void * TMWDEFS_GLOBAL smbsim_coilGetPoint(
  void *pHandle,
  TMWTYPES_USHORT pointNum)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return(tmwsim_tableFindPoint(&pDbHandle->coils, pointNum));
}

/* function: smbsim_coilByIndex */
void * TMWDEFS_GLOBAL smbsim_coilGetPointByIndex(
  void *pHandle,
  TMWTYPES_USHORT index)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return(tmwsim_tableFindPointByIndex(&pDbHandle->coils, index)); 
}

/* function: smbsim_coilGetLastPoint */ 
void * TMWDEFS_GLOBAL smbsim_coilGetLastPoint(
  void *pHandle)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return(tmwsim_tableGetLastPoint(&pDbHandle->coils));
}

/* function: smbsim_CoilBlockRead */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_CoilBlockRead(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT length,
  TMWTYPES_UCHAR *pValueArray)
{
  TMWTYPES_USHORT i;  
  TMWSIM_POINT *pPoint;
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  TMWTYPES_USHORT pointNum = startAddr;

  for(i = 0; i < length; i++)
  { 

    if(i==0)
      pPoint = tmwsim_tableFindPoint(&pDbHandle->coils, startAddr);
    else
      pPoint = tmwsim_tableGetNextPoint(&pDbHandle->coils, pPoint);

    if((pPoint == TMWDEFS_NULL) || (tmwsim_getPointNumber(pPoint) != pointNum++))
      return(TMWDEFS_FALSE);

    if (tmwsim_getBinaryValue(pPoint))
      pValueArray[i/8] = pValueArray[i/8] | (0x01 << i%8);
    else
      pValueArray[i/8] = pValueArray[i/8] & ~(0x01 << i%8);
  }

  return(TMWDEFS_TRUE);
}
  
/* function: smbsim_CoilRead */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_CoilRead(
  void *pPoint,
  TMWTYPES_BOOL *pValue)
{
  if(pPoint == TMWDEFS_NULL)
    return(TMWDEFS_FALSE);

  if (tmwsim_getBinaryValue((TMWSIM_POINT *)pPoint))
    *pValue = TMWDEFS_TRUE;
  else
    *pValue = TMWDEFS_FALSE;

  return(TMWDEFS_TRUE);
}

/* function: smbsim_CoilBlockWrite */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_CoilBlockWrite(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT length,
  TMWTYPES_UCHAR *pValueArray)
{
  TMWTYPES_USHORT i;
  TMWSIM_POINT *pPoint;

  /* Check to make sure that points exist before writing any */ 
  if(!smbsim_CoilsValidateRange(pHandle, startAddr, length))
  {
    return(TMWDEFS_FALSE);
  }

  for(i = 0; i < length; i++)
  {
    SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
    TMWTYPES_BOOL bVal;

    if(i == 0)
      pPoint = tmwsim_tableFindPoint(&pDbHandle->coils, startAddr);
    else
      pPoint = tmwsim_tableGetNextPoint(&pDbHandle->coils, pPoint);

    if(pPoint == TMWDEFS_NULL)
      return(TMWDEFS_FALSE);
 
    bVal = (pValueArray[i/8] >> i%8) & 0x01;
    tmwsim_setBinaryValue(pPoint, bVal, TMWDEFS_CHANGE_NONE);
       
    _callCallback(pDbHandle, TMWSIM_POINT_UPDATE, MBDEFS_OBJ_COIL, (TMWTYPES_USHORT)(startAddr+i));
  }

  return(TMWDEFS_TRUE);
} 

/* function: smbsim_CoilWrite */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_CoilWrite(
  void *pPoint,
  TMWTYPES_BOOL value)
{
  TMWSIM_POINT *pSimPoint = (TMWSIM_POINT *)pPoint;
  if(pPoint == TMWDEFS_NULL)
    return(TMWDEFS_FALSE);

  tmwsim_setBinaryValue(pSimPoint, value, TMWDEFS_CHANGE_REMOTE_OP);

  _callCallback((SMBSIM_DATABASE *)pSimPoint->pDbHandle, TMWSIM_POINT_UPDATE, 
    MBDEFS_OBJ_COIL, (TMWTYPES_USHORT)tmwsim_getPointNumber(pSimPoint));
  
  return(TMWDEFS_TRUE);
}

/* Input Registers _________________________________________________________*/

/* function: smbsim_addInputRegister */
void * TMWDEFS_GLOBAL smbsim_addInputRegister(
  void *pHandle,
  TMWTYPES_USHORT pointNum,
  TMWTYPES_USHORT value)
{
  TMWSIM_POINT *pPoint;
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  pPoint = tmwsim_tableAdd(&pDbHandle->inputRegisters, pointNum);
  if(pPoint != TMWDEFS_NULL)
  {
    tmwsim_initAnalog(pPoint, pHandle, pointNum, 0, 65535, 65535, 0);
    pPoint->data.analog.value = value;
    _callCallback(pDbHandle, TMWSIM_POINT_ADD, MBDEFS_OBJ_INPUT_REGISTER, pointNum);
  }
  return(pPoint);
}
 
/* function: smbsim_deleteInputRegister */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_deleteInputRegister(
  void *pHandle,
  TMWTYPES_USHORT pointNumber)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;

  _callCallback(pDbHandle, TMWSIM_POINT_DELETE, MBDEFS_OBJ_INPUT_REGISTER, pointNumber);

  return(tmwsim_tableDelete(&pDbHandle->inputRegisters, pointNumber));
}

/* function: smbsim_InputRegistersValidateRange */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_InputRegistersValidateRange(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT length)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return( _validateRange(&pDbHandle->inputRegisters, startAddr, length));
}
 
/* function: smbsim_InputRegisterGetPoint */
void * TMWDEFS_GLOBAL smbsim_InputRegisterGetPoint(
  void *pHandle,
  TMWTYPES_USHORT pointNum)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return(tmwsim_tableFindPoint(&pDbHandle->inputRegisters, pointNum));
}

/* function: smbsim_iRegGetPointByIndex */
void * TMWDEFS_GLOBAL smbsim_iRegGetPointByIndex(
  void *pHandle,
  TMWTYPES_USHORT index)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return(tmwsim_tableFindPointByIndex(&pDbHandle->inputRegisters, index)); 
} 

/* function: smbsim_iRegGetLastPoint */
void * TMWDEFS_GLOBAL smbsim_iRegGetLastPoint(
  void *pHandle)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return(tmwsim_tableGetLastPoint(&pDbHandle->inputRegisters));
}

/* function: smbsim_iRegBlockRead */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_iRegBlockRead(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT length,
  TMWTYPES_USHORT *pValueArray)
{
  TMWTYPES_USHORT i;
    TMWSIM_POINT *pPoint;
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  TMWTYPES_USHORT pointNum = startAddr;

  for(i = 0; i < length; i++)
  {  
    if(i==0)
      pPoint = tmwsim_tableFindPoint(&pDbHandle->inputRegisters, startAddr);
    else
      pPoint = tmwsim_tableGetNextPoint(&pDbHandle->inputRegisters, pPoint);

    if((pPoint == TMWDEFS_NULL) || (tmwsim_getPointNumber(pPoint) != pointNum++))
      return(TMWDEFS_FALSE);

    *(pValueArray + i) = (TMWTYPES_USHORT)tmwsim_getAnalogValue(pPoint);
  }

  return(TMWDEFS_TRUE);
}

/* function: smbsim_iRegRead */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_iRegRead(
  void *pPoint,
  TMWTYPES_USHORT *pValue)
{
  if(pPoint == TMWDEFS_NULL)
    return(TMWDEFS_FALSE);

  *pValue = (TMWTYPES_USHORT)tmwsim_getAnalogValue((TMWSIM_POINT *)pPoint);

  return(TMWDEFS_TRUE);
}

/* function: smbsim_iRegWrite */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_iRegWrite(
  void *pPoint,
  TMWTYPES_USHORT value)
{
  TMWSIM_POINT *pSimPoint = (TMWSIM_POINT *)pPoint;
  if(pPoint == TMWDEFS_NULL)
    return(TMWDEFS_FALSE);

  tmwsim_setAnalogValue(pSimPoint, value, TMWDEFS_CHANGE_REMOTE_OP);

  _callCallback((SMBSIM_DATABASE *)pSimPoint->pDbHandle, TMWSIM_POINT_UPDATE, 
    MBDEFS_OBJ_INPUT_REGISTER, (TMWTYPES_USHORT)tmwsim_getPointNumber(pSimPoint));

  return(TMWDEFS_TRUE);
}

/* Holding Registers _________________________________________________________*/

/* function: SMBSIM_NUM_HOLDING_REGISTERS */
void * TMWDEFS_GLOBAL smbsim_addHoldingRegister(
  void *pHandle,
  TMWTYPES_USHORT pointNum,
  TMWTYPES_USHORT value)
{
  TMWSIM_POINT *pPoint;
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  pPoint = tmwsim_tableAdd(&pDbHandle->holdingRegisters, pointNum);
  if(pPoint != TMWDEFS_NULL)
  {
    tmwsim_initAnalog(pPoint, pHandle, pointNum, 0, 65535, 65535, 0);
    pPoint->data.analog.value = value;
    _callCallback(pDbHandle, TMWSIM_POINT_ADD, MBDEFS_OBJ_HOLDING_REGISTER, pointNum);
  }
  return(pPoint);
}

/* function: smbsim_deleteHoldingRegister */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_deleteHoldingRegister(
  void *pHandle,
  TMWTYPES_USHORT pointNumber)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;

  _callCallback(pDbHandle, TMWSIM_POINT_DELETE, MBDEFS_OBJ_HOLDING_REGISTER, pointNumber);

  return(tmwsim_tableDelete(&pDbHandle->holdingRegisters, pointNumber));
}

/* function: smbsim_HoldingRegistersValidateRange */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_HoldingRegistersValidateRange(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT length)
{ 
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return( _validateRange(&pDbHandle->holdingRegisters, startAddr, length));
}

/* function: smbsim_HoldingRegisterGetPoint */
void * TMWDEFS_GLOBAL smbsim_HoldingRegisterGetPoint(
  void *pHandle,
  TMWTYPES_USHORT pointNum)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return(tmwsim_tableFindPoint(&pDbHandle->holdingRegisters, pointNum));
}

/* function: smbsim_hRegGetPointByIndex */
void * TMWDEFS_GLOBAL smbsim_hRegGetPointByIndex(
  void *pHandle,
  TMWTYPES_USHORT index)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return(tmwsim_tableFindPointByIndex(&pDbHandle->holdingRegisters, index)); 
}

/* function: smbsim_hRegGetLastPoint */
void * TMWDEFS_GLOBAL smbsim_hRegGetLastPoint(
  void *pHandle)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return(tmwsim_tableGetLastPoint(&pDbHandle->holdingRegisters));
}

/* function: smbsim_hRegBlockRead */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_hRegBlockRead(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT length,
  TMWTYPES_USHORT *pValueArray)
{
  TMWTYPES_USHORT i;
  TMWSIM_POINT *pPoint;
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  TMWTYPES_USHORT pointNum = startAddr;

  for(i = 0; i < length; i++)
  { 
    if(i==0) 
      pPoint = tmwsim_tableFindPoint(&pDbHandle->holdingRegisters, startAddr);
    else
      pPoint = tmwsim_tableGetNextPoint(&pDbHandle->holdingRegisters, pPoint);

    if((pPoint == TMWDEFS_NULL) || (tmwsim_getPointNumber(pPoint) != pointNum++))
      return(TMWDEFS_FALSE);

    *(pValueArray + i) = (TMWTYPES_USHORT)tmwsim_getAnalogValue((TMWSIM_POINT *)pPoint);
  }
 
  return(TMWDEFS_TRUE);
}

/* function: smbsim_hRegRead */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_hRegRead(
  void *pPoint,
  TMWTYPES_USHORT *pValue)
{
  if(pPoint == TMWDEFS_NULL)
    return(TMWDEFS_FALSE);

  *pValue = (TMWTYPES_USHORT)tmwsim_getAnalogValue((TMWSIM_POINT *)pPoint);

  return(TMWDEFS_TRUE);
}

/* function: smbsim_hRegBlockWrite */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_hRegBlockWrite(
  void *pHandle,
  TMWTYPES_USHORT startAddr,
  TMWTYPES_USHORT length,
  TMWTYPES_USHORT *pValueArray)
{
  TMWTYPES_USHORT value;
  TMWTYPES_USHORT i;
  TMWSIM_POINT *pPoint;
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
 
  /* Check to make sure that points exist before writing any */ 
  if(!smbsim_HoldingRegistersValidateRange(pHandle, startAddr, length))
  {
    return(TMWDEFS_FALSE);
  }
 
  for(i = 0; i < length; i++)
  {   
    if(i==0)
      pPoint = tmwsim_tableFindPoint(&pDbHandle->holdingRegisters, startAddr);
    else
      pPoint = tmwsim_tableGetNextPoint(&pDbHandle->holdingRegisters, pPoint);

    if(pPoint == TMWDEFS_NULL)
      return(TMWDEFS_FALSE);
 
    value = *(pValueArray + i);
    tmwsim_setAnalogValue(pPoint, value, TMWDEFS_CHANGE_NONE);

    _callCallback(pDbHandle, TMWSIM_POINT_UPDATE, MBDEFS_OBJ_HOLDING_REGISTER, (TMWTYPES_USHORT)(startAddr+i));
  }

  return(TMWDEFS_TRUE);
}

/* function: smbsim_hRegWrite */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_hRegWrite(
  void *pPoint,
  TMWTYPES_USHORT value)
{
  TMWSIM_POINT *pSimPoint = (TMWSIM_POINT *)pPoint;
  if(pPoint == TMWDEFS_NULL)
    return(TMWDEFS_FALSE);

  tmwsim_setAnalogValue(pSimPoint, value, TMWDEFS_CHANGE_REMOTE_OP);

  _callCallback((SMBSIM_DATABASE *)pSimPoint->pDbHandle, TMWSIM_POINT_UPDATE, 
    MBDEFS_OBJ_HOLDING_REGISTER, (TMWTYPES_USHORT)tmwsim_getPointNumber(pSimPoint));

  return(TMWDEFS_TRUE);
}

/* function: smbsim_readExeptionStatus */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_readExceptionStatus(  
  void *pHandle,
  TMWTYPES_UCHAR *pStatus)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  *pStatus = pDbHandle->exceptionStatus;
  return(TMWDEFS_TRUE);
}

/* function: smbsim_setExeptionStatus */
void TMWDEFS_GLOBAL smbsim_setExceptionStatus(  
  void *pHandle,
  TMWTYPES_UCHAR status)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  pDbHandle->exceptionStatus = status;
}
  
/* routine: smbsim_addDeviceId */
void * TMWDEFS_GLOBAL smbsim_addDeviceId(
  void *pHandle,
  TMWTYPES_UCHAR  objectId,
  TMWTYPES_UCHAR  length,
  TMWTYPES_UCHAR *pData)
{
  TMWSIM_POINT *pPoint;
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  pPoint = tmwsim_tableAdd(&pDbHandle->deviceIds, objectId);
  if(pPoint != TMWDEFS_NULL)
  {
    tmwsim_initString(pPoint, pHandle, objectId);  
    tmwsim_setStringValue(pPoint, pData, length, TMWDEFS_CHANGE_NONE);
    _callCallback(pDbHandle, TMWSIM_POINT_ADD, MBDEFS_OBJ_DEVICE_ID, objectId);
  }
  return(pPoint);
}
 
/* routine: smbsim_deleteDeviceId */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_deleteDeviceId(
  void *pHandle,
  TMWTYPES_UCHAR objectId)
{
  void *pPoint;
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle; 
  pPoint = tmwsim_tableFindPoint(&pDbHandle->deviceIds, objectId);
  if(pPoint != TMWDEFS_NULL)
  {
    _callCallback(pDbHandle, TMWSIM_POINT_DELETE, MBDEFS_OBJ_DEVICE_ID, objectId); 
    return(tmwsim_tableDelete(&pDbHandle->deviceIds, objectId));
  }

  return(TMWDEFS_FALSE);
}

/* function: smbsim_deviceIdGetPoint */
void * TMWDEFS_GLOBAL smbsim_deviceIdGetPoint(
  void *pHandle,
  TMWTYPES_UCHAR objectId)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return(tmwsim_tableFindPoint(&pDbHandle->deviceIds, objectId));
}

/* function: smbsim_deviceIdGetPointByIndex */
void * TMWDEFS_GLOBAL smbsim_deviceIdGetPointByIndex(
  void *pHandle,
  TMWTYPES_USHORT index)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return(tmwsim_tableFindPointByIndex(&pDbHandle->deviceIds, index)); 
}

/* function: smbsim_getNextDeviceId */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_getNextDeviceId(
  void *pHandle,
  TMWTYPES_UCHAR objectId,
  TMWTYPES_UCHAR *pNextObjectId)
{
  TMWSIM_POINT *pDataPoint;
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  pDataPoint = tmwsim_tableFindPoint(&pDbHandle->deviceIds, objectId);
  if(pDataPoint != TMWDEFS_NULL)
  {
    pDataPoint = tmwsim_tableGetNextPoint(&pDbHandle->deviceIds, pDataPoint);
  }
  if(pDataPoint != TMWDEFS_NULL)
  {
    *pNextObjectId = (TMWTYPES_UCHAR)pDataPoint->pointNumber;   
    return TMWDEFS_TRUE;
  }

  return TMWDEFS_FALSE;
}

/* function: smbsim_deviceIdGetLastPoint */
void * TMWDEFS_GLOBAL smbsim_deviceIdGetLastPoint(
  void *pHandle)
{
  SMBSIM_DATABASE *pDbHandle = (SMBSIM_DATABASE *)pHandle;
  return(tmwsim_tableGetLastPoint(&pDbHandle->deviceIds));
}

/* routine: smbsim_readDeviceId */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_readDeviceId( 
  void *pPoint,
  TMWTYPES_UCHAR *pLength,
  TMWTYPES_UCHAR *pData)
{ 
  if(pPoint != TMWDEFS_NULL)
  {
    TMWSIM_POINT * pSimPoint = (TMWSIM_POINT *)pPoint;
    TMWTYPES_UCHAR maxLength = *pLength;
    if(pSimPoint->data.string.length <= maxLength)
    {
      tmwsim_getStringValue(pSimPoint, maxLength, pData, pLength);
      return(TMWDEFS_TRUE);
    }
  }

  return(TMWDEFS_FALSE);
}

/* function: smbsim_setDeviceId */
TMWTYPES_BOOL TMWDEFS_GLOBAL smbsim_setDeviceId(
  void *pPoint,
  TMWTYPES_UCHAR bufLength,
  TMWTYPES_UCHAR *pBuf)
{ 
  TMWSIM_POINT *pSimPoint = (TMWSIM_POINT *)pPoint;

  tmwsim_setStringValue(pSimPoint, pBuf, bufLength, TMWDEFS_CHANGE_REMOTE_OP);
  _callCallback((SMBSIM_DATABASE *)pSimPoint->pDbHandle, TMWSIM_POINT_UPDATE, MBDEFS_OBJ_DEVICE_ID, 
    (TMWTYPES_USHORT)tmwsim_getPointNumber(pSimPoint));
 
  return(TMWDEFS_TRUE);
}

/* routine: smbsim_saveDatabase */
TMWTYPES_CHAR * TMWDEFS_GLOBAL smbsim_saveDatabase(
  TMWSESN *pSession)
{
  SMBSESN *pMBSession = (SMBSESN *)pSession;
  TMWTYPES_CHAR *result = TMWDEFS_NULL;
  TMWTYPES_CHAR buf[256];
  TMWSIM_POINT *pPoint;
  SMBSIM_DATABASE *pDbHandle;

  result = tmwtarg_appendString(result, "<?xml version=\"1.0\"?>\n");
  result = tmwtarg_appendString(result, "<tmw:mbdata\n");
  result = tmwtarg_appendString(result, " xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n");
  result = tmwtarg_appendString(result, " xmlns:tmw=\"http://www.TriangleMicroWorks.com/TestHarness/Schemas/mbdata\">\n");
  result = tmwtarg_appendString(result, " <device>\n");

  pDbHandle = (SMBSIM_DATABASE *)pMBSession->mb.pDbHandle;
  pPoint = (TMWSIM_POINT *)tmwsim_tableGetFirstPoint(&pDbHandle->coils);
  while(pPoint != TMWDEFS_NULL)
  {
    TMWTYPES_CHAR *desc = tmwsim_getDescription(pPoint);
    TMWTYPES_BOOL value;
    
    smbsim_CoilRead(pPoint, &value);

    result = tmwtarg_appendString(result, "   <coil>\n");

    tmwtarg_snprintf(buf, sizeof(buf), "    <address>%d</address>\n", tmwsim_getPointNumber(pPoint));
    result = tmwtarg_appendString(result, buf);

    if(desc && (strlen(desc) > 0))
    {
      tmwsim_xmlFormatDesc(desc, buf, sizeof(buf));
      result = tmwtarg_appendString(result, buf);
    }
    
    tmwtarg_snprintf(buf, sizeof(buf), "    <value>%d</value>\n", value);
    result = tmwtarg_appendString(result, buf);

    result = tmwtarg_appendString(result, "   </coil>\n");

    pPoint = tmwsim_tableGetNextPoint(&pDbHandle->coils, pPoint);
  }
 
  pPoint = tmwsim_tableGetFirstPoint(&pDbHandle->discreteInputs);
  while(pPoint != TMWDEFS_NULL)
  {
    TMWTYPES_CHAR *desc = tmwsim_getDescription(pPoint);
    TMWTYPES_UCHAR value;
    
    smbsim_dInputRead(pPoint, &value);

    result = tmwtarg_appendString(result, "   <discreteInput>\n");

    tmwtarg_snprintf(buf, sizeof(buf), "    <address>%d</address>\n", tmwsim_getPointNumber(pPoint));
    result = tmwtarg_appendString(result, buf);

    if(desc && (strlen(desc) > 0))
    {
      tmwsim_xmlFormatDesc(desc, buf, sizeof(buf));
      result = tmwtarg_appendString(result, buf);
    }
    
    tmwtarg_snprintf(buf, sizeof(buf), "    <value>%d</value>\n", value);
    result = tmwtarg_appendString(result, buf);

    result = tmwtarg_appendString(result, "   </discreteInput>\n");

    pPoint = tmwsim_tableGetNextPoint(&pDbHandle->discreteInputs, pPoint);
  }

  pPoint = tmwsim_tableGetFirstPoint(&pDbHandle->inputRegisters);
  while(pPoint != TMWDEFS_NULL)
  {
    TMWTYPES_CHAR *desc = tmwsim_getDescription(pPoint);
    TMWTYPES_USHORT value;
    
    smbsim_iRegRead(pPoint, &value);

    result = tmwtarg_appendString(result, "   <inputRegister>\n");

    tmwtarg_snprintf(buf, sizeof(buf), "    <address>%d</address>\n", tmwsim_getPointNumber(pPoint));
    result = tmwtarg_appendString(result, buf);

    if(desc && (strlen(desc) > 0))
    {
      tmwsim_xmlFormatDesc(desc, buf, sizeof(buf));
      result = tmwtarg_appendString(result, buf);
    }
    
    tmwtarg_snprintf(buf, sizeof(buf), "    <value>%d</value>\n", value);
    result = tmwtarg_appendString(result, buf);

    result = tmwtarg_appendString(result, "   </inputRegister>\n");

    pPoint = tmwsim_tableGetNextPoint(&pDbHandle->inputRegisters, pPoint);
  }

  pPoint = tmwsim_tableGetFirstPoint(&pDbHandle->holdingRegisters);
  while(pPoint != TMWDEFS_NULL)
  {
    TMWTYPES_CHAR *desc = tmwsim_getDescription(pPoint);
    TMWTYPES_USHORT value;
    
    smbsim_hRegRead(pPoint, &value);

    result = tmwtarg_appendString(result, "   <holdingRegister>\n");

    tmwtarg_snprintf(buf, sizeof(buf), "    <address>%d</address>\n", tmwsim_getPointNumber(pPoint));
    result = tmwtarg_appendString(result, buf);

    if(desc && (strlen(desc) > 0))
    {
      tmwsim_xmlFormatDesc(desc, buf, sizeof(buf));
      result = tmwtarg_appendString(result, buf);
    }
    
    tmwtarg_snprintf(buf, sizeof(buf), "    <value>%d</value>\n", value);
    result = tmwtarg_appendString(result, buf);

    result = tmwtarg_appendString(result, "   </holdingRegister>\n");

    pPoint = tmwsim_tableGetNextPoint(&pDbHandle->holdingRegisters, pPoint);
  }

  result = tmwtarg_appendString(result, " </device>\n");
  result = tmwtarg_appendString(result, "</tmw:mbdata>\n");
  return(result);
}


#endif /* TMWCNFG_USE_SIMULATED_DB */
