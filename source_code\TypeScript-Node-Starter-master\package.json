{"name": "express-typescript-starter", "version": "0.1.0", "description": "A starting point for Node.js express apps with TypeScript", "repository": {"type": "git", "url": "https://github.com/Microsoft/TypeScript-Node-Starter"}, "author": "<PERSON><PERSON>", "license": "MIT", "scripts": {"build-sass": "sass src/public/css/main.scss dist/public/css/main.css", "build-ts": "tsc", "build": "npm run build-sass && npm run build-ts && npm run lint && npm run copy-static-assets", "copy-static-assets": "ts-node copyStaticAssets.ts", "debug": "npm run build && npm run watch-debug", "lint": "tsc --noEmit && eslint \"**/*.{js,ts}\" --quiet --fix", "serve-debug": "nodemon --inspect dist/server.js", "serve": "node dist/server.js", "start": "npm run serve", "test": "jest --forceExit --coverage --verbose", "watch-debug": "concurrently -k -p \"[{name}]\" -n \"Sass,TypeScript,Node\" -c \"yellow.bold,cyan.bold,green.bold\" \"npm run watch-sass\" \"npm run watch-ts\" \"npm run serve-debug\"", "watch-node": "nodemon dist/server.js", "watch-sass": "sass --watch src/public/css/main.scss dist/public/css/main.css", "watch-test": "npm run test -- --watchAll", "watch-ts": "tsc -w", "watch": "concurrently -k -p \"[{name}]\" -n \"Sass,TypeScript,Node\" -c \"yellow.bold,cyan.bold,green.bold\" \"npm run watch-sass\" \"npm run watch-ts\" \"npm run watch-node\""}, "dependencies": {"async": "3.2.2", "bcrypt-nodejs": "0.0.3", "bluebird": "3.7.2", "body-parser": "1.19.0", "compression": "1.7.4", "connect-mongo": "4.4.0", "dotenv": "8.2.0", "errorhandler": "1.5.1", "express": "4.17.1", "express-flash": "0.0.2", "express-session": "1.17.1", "express-validator": "6.9.2", "fbgraph": "1.4.4", "lodash": "^4.17.21", "lusca": "1.6.1", "mongoose": "5.11.15", "nodemailer": "6.6.1", "passport": "0.4.1", "passport-facebook": "3.0.0", "passport-local": "1.0.0", "pug": "3.0.2", "winston": "3.3.3"}, "devDependencies": {"@types/async": "3.2.5", "@types/bcrypt-nodejs": "0.0.31", "@types/bluebird": "3.5.33", "@types/body-parser": "1.19.0", "@types/bson": "4.0.5", "@types/chai": "4.2.14", "@types/compression": "1.7.0", "@types/concurrently": "5.2.1", "@types/errorhandler": "1.5.0", "@types/eslint": "7.2.6", "@types/express": "4.17.11", "@types/express-flash": "0.0.2", "@types/express-session": "1.17.3", "@types/jest": "^26.0.23", "@types/jquery": "3.5.5", "@types/lodash": "^4.14.170", "@types/lusca": "1.6.2", "@types/mongodb": "3.6.5", "@types/node": "^14.18", "@types/nodemailer": "6.4.0", "@types/passport": "1.0.5", "@types/passport-facebook": "2.1.10", "@types/passport-local": "1.0.33", "@types/pug": "2.0.4", "@types/request": "2.48.5", "@types/request-promise": "4.1.47", "@types/shelljs": "0.8.8", "@types/supertest": "2.0.10", "@types/winston": "2.4.4", "@typescript-eslint/eslint-plugin": "4.14.2", "@typescript-eslint/parser": "4.14.2", "chai": "4.3.0", "concurrently": "6.0.2", "eslint": "7.19.0", "jest": "^27.0.6", "nodemon": "^2.0.7", "sass": "1.32.6", "shelljs": "0.8.5", "supertest": "6.1.3", "ts-jest": "^27.0.3", "ts-node": "9.1.1", "typescript": "4.1.3"}, "engines": {"node": ">=14.0.0"}}