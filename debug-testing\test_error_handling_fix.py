#!/usr/bin/env python3
"""
Test the error handling query enhancement fix.
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path to import the tool
sys.path.append(str(Path(__file__).parent))

from open_webui_code_analyzer_tool import Tools

async def test_error_handling_fix():
    """Test the error handling query enhancement"""
    print("🔍 Testing Error Handling Query Enhancement")
    print("=" * 60)
    
    # Initialize the tool
    tool = Tools()
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    
    # Ensure utils is selected
    print("1. Selecting utils codebase...")
    await tool.select_codebase("utils")
    
    # Test the previously failing queries
    test_queries = [
        "show me error handling in utils",
        "how are errors reported in the codebase",
        "error checking in tmwmem functions",
        "find error handling code"
    ]
    
    for i, query in enumerate(test_queries, 2):
        print(f"\n{i}. Testing: '{query}'")
        try:
            result = await tool.__call__(query)
            
            if "❌ Unable to retrieve code context" in result:
                print(f"   ❌ STILL FAILING: Context retrieval failed")
            elif "Context retrieval failed" in result:
                print(f"   ❌ STILL FAILING: Context retrieval failed")
            elif len(result) > 500:
                print(f"   ✅ SUCCESS: Got {len(result)} characters of content")
                # Check if it contains actual code
                if "tmwerr" in result.lower() or "error" in result.lower():
                    print(f"   ✅ Contains relevant error handling content")
                else:
                    print(f"   ⚠️ Content might be generic")
            else:
                print(f"   ⚠️ SHORT RESPONSE: {len(result)} characters")
                print(f"   Content: {result[:200]}...")
                
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
    
    # Test memory management still works
    print(f"\n{len(test_queries) + 2}. Testing memory management (should still work)...")
    try:
        result = await tool.__call__("how is memory managed in the utils codebase")
        if len(result) > 1000:
            print(f"   ✅ Memory management still works: {len(result)} characters")
        else:
            print(f"   ⚠️ Memory management response short: {len(result)} characters")
    except Exception as e:
        print(f"   ❌ Memory management exception: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 Error handling fix test completed!")

if __name__ == "__main__":
    try:
        asyncio.run(test_error_handling_fix())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
