#!/usr/bin/env python3
"""
Simple verification that the tool fix works
"""

import requests
import json

# Configuration
OPENWEBUI_URL = "http://home-ai-server.local:8080"
API_KEY = "sk-320242e0335e45a4b1fa4752f758f9ab"

def send_test_query(query):
    """Send a single test query"""
    session = requests.Session()
    session.headers.update({
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    })
    
    payload = {
        "model": "llama3:latest",
        "messages": [
            {
                "role": "user",
                "content": query
            }
        ],
        "tool_ids": ["code_analyzer_tool"],
        "stream": False,
        "temperature": 0.7,
        "max_tokens": 500
    }
    
    try:
        response = session.post(
            f"{OPENWEBUI_URL}/api/chat/completions",
            json=payload,
            timeout=90
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            return content
        else:
            return f"HTTP Error: {response.status_code}"
            
    except Exception as e:
        return f"Request failed: {e}"

def main():
    print("🔧 Verifying Tool Fix")
    print("=" * 40)
    
    # Test the specific query that was causing the error
    test_query = "How does this code work?"
    print(f"📤 Testing: '{test_query}'")
    
    response = send_test_query(test_query)
    
    # Check if we're getting the old error
    if "'NoneType' object has no attribute 'strip'" in response:
        print("❌ BUG STILL EXISTS - Tool not fixed")
        print("The 'NoneType' error is still occurring")
    elif "No codebase selected" in response and "select_codebase()" in response and len(response) < 1000:
        print("❌ GENERIC ERROR - Tool may not be working correctly")
        print("Getting generic error message instead of actual analysis")
    else:
        print("✅ FIX APPEARS TO BE WORKING")
        print("No 'NoneType' error detected")
    
    print(f"\n📄 Response preview (first 300 chars):")
    print("-" * 40)
    print(response[:300] + ("..." if len(response) > 300 else ""))
    print("-" * 40)

if __name__ == "__main__":
    main()
