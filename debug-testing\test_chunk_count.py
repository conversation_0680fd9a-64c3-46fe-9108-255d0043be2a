#!/usr/bin/env python3
"""
Test script to verify chunk count is properly reported in event emitters.
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path to import the tool
sys.path.append(str(Path(__file__).parent))

from open_webui_code_analyzer_tool import Tools

class MockEventEmitter:
    """Mock event emitter to capture status messages"""
    def __init__(self):
        self.events = []
    
    async def __call__(self, event):
        self.events.append(event)
        print(f"📡 Event: {event}")

async def test_chunk_count():
    """Test chunk count reporting in event emitters"""
    print("🧪 Testing Chunk Count Reporting")
    print("=" * 50)
    
    # Initialize the tool
    tool = Tools()
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    
    # Ensure utils is selected
    print("1. Selecting utils codebase...")
    await tool.select_codebase("utils")
    
    # Create mock event emitter
    event_emitter = MockEventEmitter()
    
    # Test get_code_context with event emitter
    print("\n2. Testing get_code_context with event emitter...")
    try:
        result = await tool.get_code_context(
            "memory management", 
            __event_emitter__=event_emitter
        )
        
        print(f"   Result length: {len(result)} characters")
        print(f"   Events captured: {len(event_emitter.events)}")
        
        # Check if any event mentions chunk count
        for event in event_emitter.events:
            if 'chunk' in event.get('data', {}).get('description', '').lower():
                print(f"   ✅ Found chunk count in event: {event['data']['description']}")
                break
        else:
            print("   ⚠️ No chunk count found in events")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Chunk count test completed!")

if __name__ == "__main__":
    try:
        asyncio.run(test_chunk_count())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
