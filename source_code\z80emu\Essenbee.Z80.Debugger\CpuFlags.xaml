﻿<UserControl x:Class="Essenbee.Z80.Debugger.CpuFlags"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:controls="clr-namespace:Essenbee.Z80.Debugger"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <GroupBox Header="Flags"
                  FontWeight="Bold"
                  Margin="15,0,15,0">
            <StackPanel Orientation="Horizontal" 
                        HorizontalAlignment="Center">

            <StackPanel Orientation="Vertical">
                <Label Content="S"
                       Margin="4,1,1,0"
                       FontWeight="Normal"/>
                <CheckBox x:Name="Bit7"
                         Margin="5,-5,1,1"
                         HorizontalAlignment="Left"
                         Width="15"
                         IsChecked="{Binding SignBit}" />
            </StackPanel>
            <StackPanel Orientation="Vertical">
                <Label Content="Z"
                       Margin="4,1,1,0"
                       FontWeight="Normal"/>
                <CheckBox x:Name="Bit6"
                         Margin="5,-5,1,1"
                         HorizontalAlignment="Left"
                         Width="15"
                         IsChecked="{Binding ZeroBit}" />
            </StackPanel>
            <StackPanel Orientation="Vertical">
                <Label Content="U"
                       Margin="4,1,1,0"
                       FontWeight="Normal"/>
                <CheckBox x:Name="Bit5"
                         Margin="5,-5,1,1"
                         HorizontalAlignment="Left"
                         Width="15"
                         IsChecked="{Binding UBit}" />
            </StackPanel>
            <StackPanel Orientation="Vertical">
                <Label Content="H"
                       Margin="4,1,1,0"
                       FontWeight="Normal"/>
                <CheckBox x:Name="Bit4"
                         Margin="5,-5,1,1"
                         HorizontalAlignment="Left"
                         Width="15"
                         IsChecked="{Binding HalfCarryBit}" />
            </StackPanel>
            <StackPanel Orientation="Vertical">
                <Label Content="X"
                       Margin="4,1,1,0"
                       FontWeight="Normal"/>
                <CheckBox x:Name="Bit3"
                         Margin="5,-5,1,1"
                         HorizontalAlignment="Left"
                         Width="15"
                         IsChecked="{Binding XBit}" />
            </StackPanel>
            <StackPanel Orientation="Vertical">
                <Label Content="P"
                       Margin="4,1,1,0"
                       FontWeight="Normal"/>
                <CheckBox x:Name="Bit2"
                         Margin="5,-5,1,1"
                         HorizontalAlignment="Left"
                         Width="15"
                         IsChecked="{Binding ParityOverflowBit}" />
            </StackPanel>
            <StackPanel Orientation="Vertical">
                <Label Content="N"
                       Margin="4,1,1,0"
                       FontWeight="Normal"/>
                <CheckBox x:Name="Bit1"
                         Margin="5,-5,1,1"
                         HorizontalAlignment="Left"
                         Width="15"
                         IsChecked="{Binding NegationBit}" />
            </StackPanel>

            <StackPanel Orientation="Vertical">
                <Label Content="C"
                       Margin="4,1,1,0"
                       FontWeight="Normal"/>
                <CheckBox x:Name="Bit0"
                         Margin="5,-5,1,1"
                         HorizontalAlignment="Left"
                         Width="15"
                         IsChecked="{Binding CarryBit}" />
            </StackPanel>

        </StackPanel>
        </GroupBox>
    </Grid>
</UserControl>
