#!/usr/bin/env python3
"""
Test script to verify that _format_enhanced_document_content supports all 27 languages
with proper language-specific searchable terms.
"""

from vector_db_creator import VectorD<PERSON><PERSON>

def test_all_language_formatting():
    """Test document formatting for all 27 supported languages"""
    
    # Language test cases with representative code and expected searchable terms
    test_cases = [
        # Core languages
        ('c', 'int main() { malloc(100); free(ptr); }', ['memory_management']),
        ('cpp', 'class Test { Test() { ptr = new int[100]; } ~Test() { delete[] ptr; } };', ['memory_management', 'oop_features']),
        ('python', 'async def main(): import asyncio', ['async_programming', 'module_usage']),
        ('csharp', 'public class Test { async Task<int> Method() { } }', ['async_programming']),
        ('javascript', 'async function test() { const component = <div/>; }', ['async_programming', 'react_framework']),
        ('typescript', 'interface User { name: string; } async function test() {}', ['type_system', 'async_programming']),
        ('rust', 'unsafe fn test() { trait MyTrait {} }', ['unsafe_code', 'trait_system']),
        ('java', '@Component public class Service { @Autowired private Repository repo; }', ['spring_framework', 'oop_patterns']),
        ('go', 'func main() { ch := make(chan int); go routine() }', ['concurrency']),
        ('sql', 'SELECT * FROM users JOIN orders WHERE id = 1', ['query_operations']),
        
        # Hardware description languages
        ('vhdl', 'entity test is port(clk: in std_logic); architecture rtl of test is signal counter: integer;', ['design_units', 'signal_processing']),
        ('verilog', 'module test(input clk); always @(posedge clk) begin assign wire_out = 1; end', ['sequential_logic', 'combinational_logic']),
        
        # Scripting languages
        ('tcl', 'proc test_proc {} { string match pattern text; list append mylist }', ['procedures', 'data_processing']),
        ('bash', 'function test() { if [ -f file ]; then grep pattern file | awk \'{print $1}\'; fi }', ['shell_scripting', 'text_processing']),
        ('lua', 'function test() local t = {}; setmetatable(t, {}); coroutine.yield() end', ['scripting', 'table_programming', 'coroutine_programming']),
        ('perl', 'sub test { my $var = shift; $var =~ s/pattern/replacement/g; my $ref = \\$var; }', ['variable_scoping', 'regex_processing', 'reference_programming']),
        
        # Functional languages
        ('commonlisp', '(defun test () (lambda (x) x)) (defmacro test-macro () `(list ,@args)) (defclass test-class () ())', ['functional_programming', 'macro_programming', 'object_oriented']),
        ('elisp', '(defun test () (current-buffer)) (add-hook \'test-hook #\'test-function) (define-key global-map "key" \'command)', ['functional_programming', 'buffer_management', 'emacs_customization']),
        ('scheme', '(define test (lambda (x) x)) (call/cc (lambda (k) (k 42)))', ['functional_programming', 'continuation_programming']),
        
        # Web technologies
        ('php', '<?php class Test extends Base implements Interface { $_GET[\'param\']; $_POST[\'data\']; } ?>', ['oop_patterns', 'web_development']),
        ('html', '<html><body><form><input type="text"><button>Submit</button></form><div><span>Content</span></div></body></html>', ['forms', 'layout']),
        
        # Data formats
        ('json', '{"object": {"array": [1, 2, 3]}, "schema": {"type": "object", "required": ["field"]}}', ['data_structure', 'data_validation']),
        ('yaml', 'mapping: value\nsequence:\n  - item1\n  - item2\nanchor: &ref\nalias: *ref', ['data_structure', 'yaml_features']),
        ('xml', '<?xml version="1.0"?><root xmlns:ns="uri"><element attribute="value">text</element><schema></schema></root>', ['markup_structure', 'xml_features']),
        
        # Build and documentation
        ('make', 'target: dependency\n\tcommand\n.PHONY: clean\nifdef DEBUG\nCFLAGS += -g\nendif', ['build_automation', 'conditional_build']),
        ('markdown', '# Header 1\n## Header 2\n[link](url) ![image](src) `code` ```python\nprint("hello")\n```', ['documentation_structure', 'content_formatting']),
        
        # Scientific computing
        ('fortran', 'program test\n  use module_name\n  real, dimension(100) :: array\n  interface\n    subroutine sub()\n  end interface\nend program', ['program_units', 'modular_programming', 'scientific_computing']),
    ]
    
    creator = VectorDBCreator(use_ollama=False)
    
    print("🧪 Testing document formatting for all 27 languages...")
    print("=" * 70)
    
    success_count = 0
    total_count = len(test_cases)
    
    for language, code, expected_terms in test_cases:
        try:
            test_chunk = {
                'content': code,
                'metadata': {
                    'relative_path': f'test.{language}',
                    'language': language,
                    'type': 'test',
                    'start_line': 1,
                    'end_line': 10,
                    'semantic_tags': ['test_domain'],
                    'quality_indicators': {'maintainability_score': 'medium'}
                }
            }
            
            formatted = creator._format_enhanced_document_content(test_chunk)
            
            # Check if expected terms are present
            found_terms = []
            missing_terms = []
            
            for term in expected_terms:
                if term in formatted:
                    found_terms.append(term)
                else:
                    missing_terms.append(term)
            
            if missing_terms:
                print(f"  ⚠️  {language.upper()}: Missing terms: {missing_terms}")
                print(f"      Found terms: {found_terms}")
            else:
                print(f"  ✅ {language.upper()}: All expected terms found: {found_terms}")
                success_count += 1
                
        except Exception as e:
            print(f"  ❌ {language.upper()}: Error - {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 Results: {success_count}/{total_count} languages have proper formatting support")
    
    if success_count == total_count:
        print("🎉 All languages have complete document formatting support!")
    else:
        print(f"⚠️  {total_count - success_count} languages need attention")
    
    return success_count == total_count

if __name__ == "__main__":
    test_all_language_formatting()
