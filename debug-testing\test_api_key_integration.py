#!/usr/bin/env python3
"""
Test the API key integration
"""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from openwebui_auto_tester import OpenWebUITester, DEFAULT_API_KEY

def test_api_key_integration():
    """Test the API key integration functionality"""
    print("🔑 Testing API Key Integration")
    print("=" * 50)
    
    # Test 1: Default API key
    print("\n1. Testing Default API Key:")
    print(f"   Default key: {DEFAULT_API_KEY[:10]}...{DEFAULT_API_KEY[-4:]}")
    
    # Test 2: Tester initialization with default key
    print("\n2. Testing Tester Initialization:")
    tester = OpenWebUITester("http://home-ai-server.local:8080", DEFAULT_API_KEY)
    
    # Check if Authorization header is set
    auth_header = tester.session.headers.get('Authorization')
    if auth_header:
        print(f"   ✅ Authorization header set: {auth_header[:20]}...")
    else:
        print("   ❌ Authorization header not set")
    
    # Test 3: Connection test
    print("\n3. Testing Connection with API Key:")
    try:
        connection_result = tester.test_connection()
        if connection_result:
            print("   ✅ Connection successful")
        else:
            print("   ❌ Connection failed")
    except Exception as e:
        print(f"   ❌ Connection error: {e}")
    
    # Test 4: API permissions test
    print("\n4. Testing API Permissions:")
    try:
        permissions = tester.test_api_permissions()
        chat_accessible = permissions.get("/api/chat/completions", False)
        
        if chat_accessible:
            print("   ✅ Chat completions endpoint accessible")
            print("   🎉 API key is working correctly!")
        else:
            print("   ⚠️ Chat completions endpoint not accessible")
            print("   This could indicate API key restrictions")
    except Exception as e:
        print(f"   ❌ Permission test error: {e}")

def show_api_key_info():
    """Show information about the integrated API key"""
    print("\n📋 API Key Integration Details")
    print("=" * 50)
    
    print(f"🔑 **Integrated API Key**: {DEFAULT_API_KEY[:10]}...{DEFAULT_API_KEY[-4:]}")
    print(f"🌐 **Target Server**: http://home-ai-server.local:8080")
    
    print(f"\n✅ **Benefits of Integration**:")
    print("   • No need to manually enter API key each time")
    print("   • Automatic authentication for all tests")
    print("   • Consistent testing experience")
    print("   • Ready to run out of the box")
    
    print(f"\n🔧 **Override Options**:")
    print("   • Environment variable: OPENWEBUI_API_KEY")
    print("   • Interactive prompt when running tests")
    print("   • Different key per test session")
    
    print(f"\n🚀 **Usage**:")
    print("   python debug-testing/openwebui_auto_tester.py")
    print("   # API key automatically used - no setup required!")

def test_environment_override():
    """Test environment variable override"""
    print("\n🌍 Testing Environment Variable Override")
    print("=" * 50)
    
    # Check current environment
    env_key = os.environ.get('OPENWEBUI_API_KEY')
    
    if env_key:
        print(f"✅ Environment API key found: {env_key[:10]}...{env_key[-4:]}")
        print("   This will override the default key")
    else:
        print("❌ No environment API key set")
        print("   Default integrated key will be used")
    
    print(f"\n💡 To set environment override:")
    print("   export OPENWEBUI_API_KEY='your-key-here'")
    print("   python debug-testing/openwebui_auto_tester.py")

def main():
    """Main test function"""
    print("🧪 API Key Integration Test Suite")
    print("=" * 50)
    
    print("Choose test mode:")
    print("1. Test API key integration")
    print("2. Show API key information")
    print("3. Test environment override")
    print("4. All tests")
    
    try:
        choice = input("\nEnter choice (1-4): ").strip()
        
        if choice == "1":
            test_api_key_integration()
        elif choice == "2":
            show_api_key_info()
        elif choice == "3":
            test_environment_override()
        elif choice == "4":
            test_api_key_integration()
            show_api_key_info()
            test_environment_override()
        else:
            print("Invalid choice. Running integration test...")
            test_api_key_integration()
            
    except KeyboardInterrupt:
        print("\n\n👋 Test interrupted")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
    
    print(f"\n🎯 Next Steps:")
    print("✅ Run the auto-tester with integrated API key:")
    print("   python debug-testing/openwebui_auto_tester.py")

if __name__ == "__main__":
    main()
